package com.pinwan.mir.module.system.service.tenant;

import com.pinwan.mir.framework.tenant.core.context.TenantContextHolder;
import com.pinwan.mir.framework.tenant.core.util.TenantUtils;
import com.pinwan.mir.module.member.service.level.MemberLevelService;
import com.pinwan.mir.module.member.dal.dataobject.level.MemberLevelDO;
import com.pinwan.mir.module.pay.service.app.PayAppService;
import com.pinwan.mir.module.pay.dal.dataobject.app.PayAppDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 租户初始化服务
 * 负责为新租户初始化基础数据
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TenantInitService {

    @Resource
    private MemberLevelService memberLevelService;

    @Resource
    private PayAppService payAppService;

    /**
     * 初始化租户基础数据
     *
     * @param tenantId 租户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void initTenantData(Long tenantId) {
        log.info("[initTenantData][开始初始化租户({})基础数据]", tenantId);
        
        // 使用租户工具类执行初始化，确保租户上下文正确
        TenantUtils.execute(tenantId, () -> {
            try {
                // 1. 初始化会员等级
                initMemberLevels();
                
                // 2. 初始化支付应用
                initPayApp();
                
                // 3. 初始化其他基础数据
                initOtherData();
                
                log.info("[initTenantData][租户({})基础数据初始化完成]", tenantId);
            } catch (Exception e) {
                log.error("[initTenantData][租户({})基础数据初始化失败]", tenantId, e);
                throw new RuntimeException("租户基础数据初始化失败", e);
            }
        });
    }

    /**
     * 初始化会员等级
     */
    private void initMemberLevels() {
        log.info("[initMemberLevels][初始化会员等级]");
        
        // 检查是否已经初始化过
        if (memberLevelService.getMemberLevelList().size() > 0) {
            log.info("[initMemberLevels][会员等级已存在，跳过初始化]");
            return;
        }
        
        // 创建默认会员等级
        List<MemberLevelDO> levels = Arrays.asList(
            MemberLevelDO.builder()
                .name("青铜会员")
                .level(1)
                .experience(0)
                .discountPercent(100)
                .icon("https://example.com/icons/bronze.png")
                .backgroundUrl("https://example.com/bg/bronze.jpg")
                .status(0) // 启用状态
                .build(),
                
            MemberLevelDO.builder()
                .name("白银会员")
                .level(2)
                .experience(1000)
                .discountPercent(95)
                .icon("https://example.com/icons/silver.png")
                .backgroundUrl("https://example.com/bg/silver.jpg")
                .status(0) // 启用状态
                .build(),
                
            MemberLevelDO.builder()
                .name("黄金会员")
                .level(3)
                .experience(5000)
                .discountPercent(90)
                .icon("https://example.com/icons/gold.png")
                .backgroundUrl("https://example.com/bg/gold.jpg")
                .status(0) // 启用状态
                .build(),
                
            MemberLevelDO.builder()
                .name("铂金会员")
                .level(4)
                .experience(10000)
                .discountPercent(85)
                .icon("https://example.com/icons/platinum.png")
                .backgroundUrl("https://example.com/bg/platinum.jpg")
                .status(0) // 启用状态
                .build(),
                
            MemberLevelDO.builder()
                .name("钻石会员")
                .level(5)
                .experience(50000)
                .discountPercent(80)
                .icon("https://example.com/icons/diamond.png")
                .backgroundUrl("https://example.com/bg/diamond.jpg")
                .status(0) // 启用状态
                .build()
        );
        
        // 批量创建会员等级
        for (MemberLevelDO level : levels) {
            memberLevelService.createMemberLevel(level);
        }
        
        log.info("[initMemberLevels][成功创建{}个会员等级]", levels.size());
    }

    /**
     * 初始化支付应用
     */
    private void initPayApp() {
        log.info("[initPayApp][初始化支付应用]");
        
        // 检查是否已经初始化过
        if (payAppService.getPayAppList().size() > 0) {
            log.info("[initPayApp][支付应用已存在，跳过初始化]");
            return;
        }
        
        // 创建默认支付应用
        PayAppDO payApp = PayAppDO.builder()
            .appKey("default_app_" + TenantContextHolder.getTenantId())
            .name("默认支付应用")
            .status(0) // 启用状态
            .remark("系统自动创建的默认支付应用")
            .orderNotifyUrl("http://localhost:8080/admin-api/pay/notify/order")
            .refundNotifyUrl("http://localhost:8080/admin-api/pay/notify/refund")
            .build();
            
        payAppService.createPayApp(payApp);
        
        log.info("[initPayApp][成功创建默认支付应用: {}]", payApp.getName());
    }

    /**
     * 初始化其他基础数据
     */
    private void initOtherData() {
        log.info("[initOtherData][初始化其他基础数据]");
        
        // 这里可以添加其他需要初始化的数据
        // 例如：默认文章分类、默认轮播图、默认配置等
        
        // TODO: 根据业务需要添加其他初始化逻辑
        
        log.info("[initOtherData][其他基础数据初始化完成]");
    }

    /**
     * 清理租户数据
     * 注意：这是一个危险操作，请谨慎使用
     *
     * @param tenantId 租户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void cleanTenantData(Long tenantId) {
        log.warn("[cleanTenantData][开始清理租户({})数据]", tenantId);
        
        TenantUtils.execute(tenantId, () -> {
            try {
                // 清理会员等级数据
                memberLevelService.getMemberLevelList().forEach(level -> {
                    memberLevelService.deleteMemberLevel(level.getId());
                });
                
                // 清理支付应用数据
                payAppService.getPayAppList().forEach(app -> {
                    payAppService.deletePayApp(app.getId());
                });
                
                log.warn("[cleanTenantData][租户({})数据清理完成]", tenantId);
            } catch (Exception e) {
                log.error("[cleanTenantData][租户({})数据清理失败]", tenantId, e);
                throw new RuntimeException("租户数据清理失败", e);
            }
        });
    }

    /**
     * 检查租户数据完整性
     *
     * @param tenantId 租户ID
     * @return 检查结果
     */
    public boolean checkTenantDataIntegrity(Long tenantId) {
        log.info("[checkTenantDataIntegrity][检查租户({})数据完整性]", tenantId);
        
        return TenantUtils.execute(tenantId, () -> {
            try {
                // 检查会员等级是否存在
                boolean hasLevels = memberLevelService.getMemberLevelList().size() > 0;
                
                // 检查支付应用是否存在
                boolean hasPayApps = payAppService.getPayAppList().size() > 0;
                
                boolean isComplete = hasLevels && hasPayApps;
                
                log.info("[checkTenantDataIntegrity][租户({})数据完整性检查结果: {}]", 
                    tenantId, isComplete ? "完整" : "不完整");
                
                return isComplete;
            } catch (Exception e) {
                log.error("[checkTenantDataIntegrity][租户({})数据完整性检查失败]", tenantId, e);
                return false;
            }
        });
    }
}
