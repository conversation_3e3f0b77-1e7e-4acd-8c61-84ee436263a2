#!/bin/bash

# =============================================
# 测试单个表的字段获取
# 用于调试增强表分析器的问题
# =============================================

echo "========================================"
echo "测试单个表的字段获取"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

# 测试表名
TEST_TABLE="account_statistic"

echo "数据库: $DB_NAME"
echo "测试表: $TEST_TABLE"
echo

# 1. 测试表注释获取
echo "=== 测试1: 获取表注释 ==="
TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$TEST_TABLE';" \
    --batch --skip-column-names 2>/dev/null)

echo "表注释: '$TABLE_COMMENT'"

# 2. 测试字段信息获取（直接输出）
echo
echo "=== 测试2: 直接获取字段信息 ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT 
        COLUMN_NAME,
        COLUMN_TYPE,
        IS_NULLABLE,
        IFNULL(COLUMN_KEY, ''),
        IFNULL(COLUMN_DEFAULT, ''),
        IFNULL(COLUMN_COMMENT, '')
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$TEST_TABLE' 
    ORDER BY ORDINAL_POSITION;" \
    --batch --skip-column-names 2>/dev/null

# 3. 测试临时文件方式
echo
echo "=== 测试3: 临时文件方式 ==="
temp_file="/tmp/test_columns_$$.txt"
echo "临时文件: $temp_file"

mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT 
        COLUMN_NAME,
        COLUMN_TYPE,
        IS_NULLABLE,
        IFNULL(COLUMN_KEY, ''),
        IFNULL(COLUMN_DEFAULT, ''),
        IFNULL(COLUMN_COMMENT, '')
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$TEST_TABLE' 
    ORDER BY ORDINAL_POSITION;" \
    --batch --skip-column-names 2>/dev/null > "$temp_file"

echo "临时文件大小: $(wc -c < "$temp_file") 字节"
echo "临时文件行数: $(wc -l < "$temp_file") 行"

if [ -s "$temp_file" ]; then
    echo "✅ 临时文件有内容"
    echo "前3行内容:"
    head -3 "$temp_file"
    echo
    echo "=== 测试4: 解析临时文件 ==="
    while IFS=$'\t' read -r col_name col_type is_null col_key col_default col_comment; do
        # 跳过空行
        [ -z "$col_name" ] && continue
        
        echo "字段: $col_name, 类型: $col_type, 注释: $col_comment"
    done < "$temp_file"
else
    echo "❌ 临时文件为空"
fi

# 清理
rm -f "$temp_file"

echo
echo "========================================"
echo "测试完成！"
echo "========================================"
