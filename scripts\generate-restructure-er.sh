#!/bin/bash

# =============================================
# 重构ER图生成脚本
# 基于模块分类生成清晰的ER图
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "========================================"
echo "重构ER图生成工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database/restructure

log_info "生成模块化ER图..."

# 获取所有表名
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

# 生成完整的模块化ER图
cat > docs/database/restructure/modular-er-diagram.mmd << 'EOF'
erDiagram
    %% 足球彩票系统模块化ER图
    %% 按功能模块分类展示
    
    %% ========================================
    %% 平台框架模块 (Framework)
    %% ========================================
    
    system_tenant {
        bigint id PK "租户ID"
        varchar name "租户名称"
        varchar contact_name "联系人"
        varchar contact_mobile "联系电话"
        tinyint status "状态"
        datetime expire_time "过期时间"
        datetime create_time "创建时间"
    }
    
    system_users {
        bigint id PK "用户ID"
        bigint tenant_id "租户ID"
        varchar username "用户名"
        varchar nickname "昵称"
        varchar mobile "手机号"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
    system_role {
        bigint id PK "角色ID"
        bigint tenant_id "租户ID"
        varchar name "角色名称"
        varchar code "角色编码"
        tinyint type "角色类型"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
    system_menu {
        bigint id PK "菜单ID"
        varchar name "菜单名称"
        varchar permission "权限标识"
        tinyint type "菜单类型"
        int sort "显示顺序"
        bigint parent_id "父菜单ID"
        varchar path "路由地址"
        varchar icon "菜单图标"
        varchar component "组件路径"
        tinyint status "菜单状态"
        datetime create_time "创建时间"
    }
    
    system_dict_type {
        bigint id PK "字典主键"
        varchar name "字典名称"
        varchar type "字典类型"
        tinyint status "状态"
        varchar remark "备注"
        datetime create_time "创建时间"
    }
    
    system_dict_data {
        bigint id PK "字典编码"
        int sort "字典排序"
        varchar label "字典标签"
        varchar value "字典键值"
        varchar dict_type "字典类型"
        tinyint status "状态"
        varchar remark "备注"
        datetime create_time "创建时间"
    }
    
    %% ========================================
    %% 会员模块 (Member)
    %% ========================================
    
    member_user {
        bigint id PK "用户ID"
        bigint tenant_id "租户ID"
        varchar username "用户名"
        varchar mobile "手机号"
        varchar nickname "昵称"
        varchar avatar "头像"
        decimal gold "鱼币"
        decimal balance "余额"
        bigint level_id "会员等级"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
    member_level {
        bigint id PK "等级ID"
        bigint tenant_id "租户ID"
        varchar name "等级名称"
        int level "等级值"
        decimal discount_percent "折扣比例"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
    member_level_record {
        bigint id PK "记录ID"
        bigint tenant_id "租户ID"
        bigint user_id "用户ID"
        bigint level_id "等级ID"
        varchar reason "变更原因"
        datetime create_time "创建时间"
    }
    
    member_point_record {
        bigint id PK "记录ID"
        bigint tenant_id "租户ID"
        bigint user_id "用户ID"
        varchar title "积分标题"
        int point "积分值"
        int total_point "总积分"
        varchar description "描述"
        datetime create_time "创建时间"
    }
    
    %% ========================================
    %% 支付模块 (Payment)
    %% ========================================
    
    pay_app {
        bigint id PK "应用ID"
        bigint tenant_id "租户ID"
        varchar name "应用名称"
        tinyint status "状态"
        varchar pay_notify_url "支付回调地址"
        varchar refund_notify_url "退款回调地址"
        datetime create_time "创建时间"
    }
    
    pay_order {
        bigint id PK "订单ID"
        bigint tenant_id "租户ID"
        bigint app_id "应用ID"
        varchar merchant_order_id "商户订单号"
        varchar subject "订单标题"
        varchar body "订单描述"
        int price "支付金额(分)"
        tinyint status "订单状态"
        datetime success_time "支付成功时间"
        datetime create_time "创建时间"
    }
    
    pay_wallet {
        bigint id PK "钱包ID"
        bigint tenant_id "租户ID"
        bigint user_id "用户ID"
        varchar user_type "用户类型"
        int balance "余额(分)"
        int total_expense "总支出"
        int total_recharge "总充值"
        datetime create_time "创建时间"
    }
    
    pay_wallet_transaction {
        bigint id PK "交易ID"
        bigint tenant_id "租户ID"
        bigint wallet_id "钱包ID"
        tinyint biz_type "业务类型"
        varchar biz_id "业务编号"
        varchar title "交易标题"
        int price "交易金额(分)"
        int balance "余额(分)"
        datetime create_time "创建时间"
    }
    
    %% ========================================
    %% 业务模块 (Business)
    %% ========================================
    
    author_article {
        bigint id PK "文章ID"
        bigint tenant_id "租户ID"
        bigint author_id "作者ID"
        varchar title "标题"
        text intro "简介"
        text free_contents "免费内容"
        text contents "付费内容"
        decimal price "价格"
        tinyint status "状态"
        datetime start_time "开始时间"
        datetime create_time "创建时间"
    }
    
    author_article_append {
        bigint id PK "追加ID"
        bigint tenant_id "租户ID"
        bigint article_id "文章ID"
        text content "追加内容"
        datetime create_time "创建时间"
    }
    
    match_team {
        bigint id PK "球队ID"
        bigint tenant_id "租户ID"
        varchar name "球队名称"
        varchar logo "球队logo"
        text description "球队描述"
        datetime create_time "创建时间"
    }
    
    banner {
        bigint id PK "轮播图ID"
        bigint tenant_id "租户ID"
        varchar title "标题"
        varchar pic_url "图片地址"
        varchar url "跳转地址"
        int sort "排序"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
    %% ========================================
    %% 表关系定义
    %% ========================================
    
    %% 租户关系
    system_tenant ||--o{ system_users : "tenant_id"
    system_tenant ||--o{ member_user : "tenant_id"
    system_tenant ||--o{ pay_app : "tenant_id"
    system_tenant ||--o{ pay_order : "tenant_id"
    system_tenant ||--o{ author_article : "tenant_id"
    
    %% 用户关系
    member_user ||--|| pay_wallet : "user_id"
    member_user ||--o{ pay_order : "user_id"
    member_user ||--o{ author_article : "author_id"
    member_user ||--o{ member_level_record : "user_id"
    member_user ||--o{ member_point_record : "user_id"
    
    %% 等级关系
    member_level ||--o{ member_user : "level_id"
    member_level ||--o{ member_level_record : "level_id"
    
    %% 支付关系
    pay_app ||--o{ pay_order : "app_id"
    pay_wallet ||--o{ pay_wallet_transaction : "wallet_id"
    
    %% 文章关系
    author_article ||--o{ author_article_append : "article_id"
    
    %% 字典关系
    system_dict_type ||--o{ system_dict_data : "dict_type"
EOF

log_success "模块化ER图已生成: docs/database/restructure/modular-er-diagram.mmd"

# 生成简化的核心业务ER图
cat > docs/database/restructure/core-business-er.mmd << 'EOF'
erDiagram
    %% 核心业务ER图 - 重构后的核心表关系
    
    %% 租户中心
    system_tenant {
        bigint id PK
        varchar name
        tinyint status
        datetime expire_time
    }
    
    %% 用户中心
    member_user {
        bigint id PK
        bigint tenant_id FK
        varchar username
        varchar mobile
        decimal gold
        decimal balance
        bigint level_id FK
    }
    
    member_level {
        bigint id PK
        bigint tenant_id FK
        varchar name
        int level
        decimal discount_percent
    }
    
    %% 支付中心
    pay_order {
        bigint id PK
        bigint tenant_id FK
        bigint app_id FK
        varchar merchant_order_id
        int price
        tinyint status
    }
    
    pay_wallet {
        bigint id PK
        bigint tenant_id FK
        bigint user_id FK
        int balance
        int total_expense
    }
    
    %% 内容中心
    author_article {
        bigint id PK
        bigint tenant_id FK
        bigint author_id FK
        varchar title
        decimal price
        tinyint status
    }
    
    %% 核心关系
    system_tenant ||--o{ member_user : "tenant_id"
    system_tenant ||--o{ pay_order : "tenant_id"
    system_tenant ||--o{ author_article : "tenant_id"
    
    member_user ||--|| pay_wallet : "user_id"
    member_user ||--o{ pay_order : "user_id"
    member_user ||--o{ author_article : "author_id"
    member_level ||--o{ member_user : "level_id"
EOF

log_success "核心业务ER图已生成: docs/database/restructure/core-business-er.mmd"

# 清理临时文件
rm -f /tmp/all_tables.txt

echo
echo "========================================"
echo "ER图生成完成！"
echo "========================================"
echo "🎨 docs/database/restructure/modular-er-diagram.mmd - 完整模块化ER图"
echo "🎯 docs/database/restructure/core-business-er.mmd - 核心业务ER图"
echo "========================================"
echo
echo "查看ER图："
echo "1. 访问 https://mermaid.live/ 粘贴内容"
echo "2. 在VSCode中安装Mermaid Preview插件"
