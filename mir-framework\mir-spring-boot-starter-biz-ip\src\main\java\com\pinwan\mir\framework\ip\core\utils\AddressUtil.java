package com.pinwan.mir.framework.ip.core.utils;

import com.alibaba.fastjson.JSON;
import com.pinwan.mir.framework.ip.core.utils.vo.AreaVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jsoup.internal.StringUtil;
import org.lionsoul.ip2region.xdb.Searcher;

import java.io.File;
import java.text.MessageFormat;
import java.util.Objects;

@Slf4j
public class AddressUtil {

    /**
     * 当前记录地址的本地DB
     */
    private static final String TEMP_FILE_DIR = "";

    /**
     * 根据IP地址查询登录来源
     *
     * @param ip
     * @return
     */
    public static String getCityInfo(String ip) {
        try {
            if (StringUtil.isBlank(ip)){
                return "";
            }

            String dbPath = System.getProperty("user.dir")+"/ip2region.xdb";
            File file = new File(dbPath);
            //如果当前文件不存在，则从缓存中复制一份
            if (!file.exists()) {
                dbPath =    TEMP_FILE_DIR + "ip.db";
                System.out.println(MessageFormat.format("当前目录为:[{0}]", dbPath));
                file = new File(dbPath);
                FileUtils.copyInputStreamToFile(Objects.requireNonNull(AddressUtil.class.getClassLoader().getResourceAsStream("classpath:ip2region.xdb")), file);
            }


            //创建查询对象
            Searcher searcher = Searcher.newWithFileOnly(dbPath);
            //开始查询
            String search = searcher.search(ip);
            String address = "";
            if (!StringUtil.isBlank(search)){
                String[] split = search.split("\\|");
                address = split[3];
                if ("0".equals(address)){
                    address = split[2];
                    if ("0".equals(address)){
                        address = split[1];
                        if ("0".equals(address)){
                            address = split[0];

                        }
                    }
                }
            }
            return address;
        } catch (Exception e) {
            e.printStackTrace();
        }
        //默认返回空字符串
        return "";
    }

    public static AreaVO getAreaInfo(String ip) {
        try {
            if (StringUtil.isBlank(ip)){
                return null;
            }


            String dbPath = System.getProperty("user.dir")+"/ip2region.xdb";
            File file = new File(dbPath);
            //如果当前文件不存在，则从缓存中复制一份
            if (!file.exists()) {
                dbPath =    TEMP_FILE_DIR + "ip.db";
                System.out.println(MessageFormat.format("当前目录为:[{0}]", dbPath));
                file = new File(dbPath);
                FileUtils.copyInputStreamToFile(Objects.requireNonNull(AddressUtil.class.getClassLoader().getResourceAsStream("classpath:ip2region.xdb")), file);
            }


            //创建查询对象
            Searcher searcher = Searcher.newWithFileOnly(dbPath);
            //开始查询
            String search = searcher.search(ip);
            AreaVO areaVO = new AreaVO();
            if (!StringUtil.isBlank(search)){
                String[] split = search.split("\\|");
                areaVO.setCountry(split[0]);
                areaVO.setProvince(split[2]);
                areaVO.setCity(split[3]);
            }
            log.info("根据IP["+ip+"]获取地域信息：" + JSON.toJSONString(areaVO));
            return areaVO;
        } catch (Exception e) {
            e.printStackTrace();
        }
        //默认返回
        return null;
    }

    public static void main(String[] args) {
        String ip="*************";
        AreaVO areaInfo = getAreaInfo(ip);
        System.out.println(areaInfo.getCountry());
        System.out.println(areaInfo.getProvince());
        System.out.println(areaInfo.getCity());
    }
}
