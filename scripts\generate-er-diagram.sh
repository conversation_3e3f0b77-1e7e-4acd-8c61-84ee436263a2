#!/bin/bash

# =============================================
# 生成数据库ER图脚本
# 连接数据库，分析表结构和关系，生成ER图
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "数据库ER图生成工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database
mkdir -p docs/database/er-diagrams

log_info "开始分析数据库结构..."

# 生成表结构分析SQL
cat > /tmp/analyze_db.sql << 'EOF'
-- 获取所有表信息
SELECT 
    TABLE_NAME as table_name,
    TABLE_COMMENT as table_comment,
    TABLE_ROWS as estimated_rows,
    CREATE_TIME as create_time
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_TYPE = 'BASE TABLE'
ORDER BY 
    TABLE_NAME;
EOF

# 执行分析并保存结果
log_info "获取表信息..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < /tmp/analyze_db.sql > docs/database/tables_info.txt

# 获取表结构详情
log_info "获取表结构详情..."
cat > /tmp/get_table_structure.sql << 'EOF'
-- 获取所有表的字段信息
SELECT 
    TABLE_NAME as table_name,
    COLUMN_NAME as column_name,
    DATA_TYPE as data_type,
    IS_NULLABLE as is_nullable,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment,
    COLUMN_KEY as key_type,
    EXTRA as extra
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
ORDER BY 
    TABLE_NAME, ORDINAL_POSITION;
EOF

mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < /tmp/get_table_structure.sql > docs/database/columns_info.txt

# 获取外键关系
log_info "获取外键关系..."
cat > /tmp/get_foreign_keys.sql << 'EOF'
-- 获取外键关系
SELECT 
    TABLE_NAME as table_name,
    COLUMN_NAME as column_name,
    REFERENCED_TABLE_NAME as referenced_table,
    REFERENCED_COLUMN_NAME as referenced_column,
    CONSTRAINT_NAME as constraint_name
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY 
    TABLE_NAME, COLUMN_NAME;
EOF

mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < /tmp/get_foreign_keys.sql > docs/database/foreign_keys.txt

# 获取索引信息
log_info "获取索引信息..."
cat > /tmp/get_indexes.sql << 'EOF'
-- 获取索引信息
SELECT 
    TABLE_NAME as table_name,
    INDEX_NAME as index_name,
    COLUMN_NAME as column_name,
    NON_UNIQUE as non_unique,
    SEQ_IN_INDEX as sequence
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME != 'PRIMARY'
ORDER BY 
    TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
EOF

mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < /tmp/get_indexes.sql > docs/database/indexes_info.txt

# 生成Mermaid ER图
log_info "生成Mermaid ER图..."

# 创建Python脚本来生成ER图
cat > /tmp/generate_mermaid_er.py << 'EOF'
#!/usr/bin/env python3
import sys
import re

def parse_tables_info(filename):
    """解析表信息"""
    tables = {}
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[1:]:  # 跳过标题行
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    table_name = parts[0]
                    table_comment = parts[1] if parts[1] != 'NULL' else ''
                    tables[table_name] = {
                        'comment': table_comment,
                        'columns': []
                    }
    except Exception as e:
        print(f"Error reading tables info: {e}")
    return tables

def parse_columns_info(filename, tables):
    """解析字段信息"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[1:]:  # 跳过标题行
                parts = line.strip().split('\t')
                if len(parts) >= 8:
                    table_name = parts[0]
                    column_name = parts[1]
                    data_type = parts[2]
                    is_nullable = parts[3]
                    default_value = parts[4] if parts[4] != 'NULL' else ''
                    comment = parts[5] if parts[5] != 'NULL' else ''
                    key_type = parts[6] if parts[6] != 'NULL' else ''
                    extra = parts[7] if parts[7] != 'NULL' else ''
                    
                    if table_name in tables:
                        tables[table_name]['columns'].append({
                            'name': column_name,
                            'type': data_type,
                            'nullable': is_nullable == 'YES',
                            'default': default_value,
                            'comment': comment,
                            'key_type': key_type,
                            'extra': extra
                        })
    except Exception as e:
        print(f"Error reading columns info: {e}")
    return tables

def parse_foreign_keys(filename):
    """解析外键关系"""
    foreign_keys = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[1:]:  # 跳过标题行
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    foreign_keys.append({
                        'table': parts[0],
                        'column': parts[1],
                        'ref_table': parts[2],
                        'ref_column': parts[3],
                        'constraint': parts[4]
                    })
    except Exception as e:
        print(f"Error reading foreign keys: {e}")
    return foreign_keys

def generate_mermaid_er(tables, foreign_keys):
    """生成Mermaid ER图"""
    mermaid = ["erDiagram"]
    
    # 按模块分组表
    modules = {
        'member': [],
        'pay': [],
        'system': [],
        'infra': [],
        'mp': [],
        'author': [],
        'other': []
    }
    
    for table_name in tables.keys():
        if table_name.startswith('member'):
            modules['member'].append(table_name)
        elif table_name.startswith('pay'):
            modules['pay'].append(table_name)
        elif table_name.startswith('system'):
            modules['system'].append(table_name)
        elif table_name.startswith('infra'):
            modules['infra'].append(table_name)
        elif table_name.startswith('mp'):
            modules['mp'].append(table_name)
        elif table_name.startswith('author'):
            modules['author'].append(table_name)
        else:
            modules['other'].append(table_name)
    
    # 生成表定义
    for module, table_list in modules.items():
        if table_list:
            mermaid.append(f"    %% {module.upper()} 模块")
            for table_name in table_list:
                table = tables[table_name]
                comment = table['comment'] or table_name
                mermaid.append(f"    {table_name} {{")
                
                # 添加主要字段
                for col in table['columns'][:10]:  # 限制字段数量
                    col_type = col['type']
                    key_indicator = ""
                    if col['key_type'] == 'PRI':
                        key_indicator = " PK"
                    elif col['key_type'] == 'UNI':
                        key_indicator = " UK"
                    elif col['key_type'] == 'MUL':
                        key_indicator = " FK"
                    
                    comment_text = f" \"{col['comment']}\"" if col['comment'] else ""
                    mermaid.append(f"        {col_type} {col['name']}{key_indicator}{comment_text}")
                
                if len(table['columns']) > 10:
                    mermaid.append(f"        string ... \"还有{len(table['columns'])-10}个字段\"")
                
                mermaid.append("    }")
            mermaid.append("")
    
    # 生成关系
    mermaid.append("    %% 表关系")
    for fk in foreign_keys:
        mermaid.append(f"    {fk['ref_table']} ||--o{{ {fk['table']} : \"{fk['column']}\"")
    
    return "\n".join(mermaid)

def main():
    # 解析数据
    tables = parse_tables_info('docs/database/tables_info.txt')
    tables = parse_columns_info('docs/database/columns_info.txt', tables)
    foreign_keys = parse_foreign_keys('docs/database/foreign_keys.txt')
    
    # 生成ER图
    mermaid_er = generate_mermaid_er(tables, foreign_keys)
    
    # 保存到文件
    with open('docs/database/er-diagram.mmd', 'w', encoding='utf-8') as f:
        f.write(mermaid_er)
    
    print("✅ Mermaid ER图已生成: docs/database/er-diagram.mmd")
    
    # 生成简化版本（只包含主要表）
    main_tables = {}
    for table_name, table_data in tables.items():
        if any(table_name.startswith(prefix) for prefix in ['member_', 'pay_', 'author_', 'system_user', 'system_tenant']):
            main_tables[table_name] = table_data
    
    main_mermaid = generate_mermaid_er(main_tables, [fk for fk in foreign_keys if fk['table'] in main_tables])
    
    with open('docs/database/er-diagram-main.mmd', 'w', encoding='utf-8') as f:
        f.write(main_mermaid)
    
    print("✅ 主要表ER图已生成: docs/database/er-diagram-main.mmd")

if __name__ == "__main__":
    main()
EOF

# 执行Python脚本生成ER图
python3 /tmp/generate_mermaid_er.py

# 清理临时文件
rm -f /tmp/analyze_db.sql /tmp/get_table_structure.sql /tmp/get_foreign_keys.sql /tmp/get_indexes.sql /tmp/generate_mermaid_er.py

log_success "数据库ER图生成完成！"

echo
echo "========================================"
echo "生成的文件："
echo "📊 docs/database/tables_info.txt - 表信息"
echo "📋 docs/database/columns_info.txt - 字段信息"
echo "🔗 docs/database/foreign_keys.txt - 外键关系"
echo "📇 docs/database/indexes_info.txt - 索引信息"
echo "🎨 docs/database/er-diagram.mmd - 完整ER图"
echo "🎯 docs/database/er-diagram-main.mmd - 主要表ER图"
echo "========================================"
