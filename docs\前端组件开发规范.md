# 前端组件开发规范

## 1. 组件命名规范

### 1.1 文件命名
- **组件文件**: 使用PascalCase（大驼峰）命名
- **页面文件**: 使用kebab-case（短横线分隔）
- **工具文件**: 使用camelCase（小驼峰）

```
components/
├── UserForm.vue          # 组件文件
├── DataTable.vue         # 组件文件
└── user-management/      # 页面目录
    ├── index.vue         # 页面文件
    └── components/       # 页面专用组件
        └── UserDialog.vue
```

### 1.2 组件名称
```vue
<!-- ✅ 正确 -->
<template>
  <div class="user-form">
    <BaseButton>提交</BaseButton>
    <UserInfoCard :user="userInfo" />
  </div>
</template>

<script>
export default {
  name: 'UserForm',  // 组件名使用PascalCase
  components: {
    BaseButton,      // 基础组件以Base开头
    UserInfoCard     // 业务组件使用描述性名称
  }
}
</script>
```

## 2. 组件结构规范

### 2.1 标准组件结构
```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script>
export default {
  name: 'ComponentName',
  
  components: {},
  
  props: {},
  
  emits: [],
  
  data() {
    return {}
  },
  
  computed: {},
  
  watch: {},
  
  created() {},
  
  mounted() {},
  
  methods: {},
  
  beforeUnmount() {}
}
</script>

<style lang="scss" scoped>
.component-name {
  // 样式定义
}
</style>
```

### 2.2 Composition API结构（Vue3）
```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// Props定义
const props = defineProps({
  title: {
    type: String,
    required: true
  }
})

// Emits定义
const emit = defineEmits(['update', 'close'])

// 响应式数据
const loading = ref(false)
const data = ref([])

// 计算属性
const filteredData = computed(() => {
  return data.value.filter(item => item.active)
})

// 方法
const handleSubmit = () => {
  emit('update', data.value)
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
.component-name {
  // 样式定义
}
</style>
```

## 3. Props规范

### 3.1 Props定义
```javascript
// ✅ 完整的Props定义
props: {
  // 基础类型
  title: {
    type: String,
    required: true,
    default: ''
  },
  
  // 数字类型
  count: {
    type: Number,
    default: 0,
    validator(value) {
      return value >= 0
    }
  },
  
  // 对象类型
  user: {
    type: Object,
    default: () => ({}),
    required: true
  },
  
  // 数组类型
  items: {
    type: Array,
    default: () => [],
    validator(value) {
      return Array.isArray(value)
    }
  },
  
  // 多类型
  value: {
    type: [String, Number],
    default: ''
  },
  
  // 布尔类型
  disabled: {
    type: Boolean,
    default: false
  }
}
```

### 3.2 Props命名
```javascript
// ✅ 正确的Props命名
props: {
  userName: String,        // 使用camelCase
  isVisible: Boolean,      // 布尔值使用is/has/can前缀
  maxCount: Number,        // 描述性命名
  onSubmit: Function       // 事件处理函数使用on前缀
}

// ❌ 错误的Props命名
props: {
  user_name: String,       // 不使用下划线
  visible: Boolean,        // 布尔值缺少前缀
  max: Number,            // 命名不够描述性
  submit: Function        // 事件处理函数缺少前缀
}
```

## 4. 事件规范

### 4.1 事件命名
```vue
<template>
  <div>
    <button @click="handleSubmit">提交</button>
    <input @input="handleInput" />
  </div>
</template>

<script>
export default {
  emits: [
    'submit',           // 动词形式
    'update:value',     // v-model更新事件
    'item-click',       // kebab-case命名
    'before-close'      // 生命周期事件
  ],
  
  methods: {
    handleSubmit() {
      // 验证数据
      if (this.validateForm()) {
        this.$emit('submit', this.formData)
      }
    },
    
    handleInput(event) {
      this.$emit('update:value', event.target.value)
    }
  }
}
</script>
```

### 4.2 事件参数
```javascript
// ✅ 正确的事件参数
methods: {
  handleItemClick(item, index, event) {
    this.$emit('item-click', {
      item,
      index,
      originalEvent: event
    })
  },
  
  handleSubmit() {
    this.$emit('submit', {
      data: this.formData,
      timestamp: Date.now()
    })
  }
}

// ❌ 错误的事件参数
methods: {
  handleItemClick(item, index, event) {
    this.$emit('item-click', item, index, event)  // 参数过多
  }
}
```

## 5. 样式规范

### 5.1 CSS类命名
```vue
<template>
  <div class="user-card">
    <div class="user-card__header">
      <h3 class="user-card__title">{{ title }}</h3>
      <button class="user-card__close-btn">×</button>
    </div>
    <div class="user-card__content">
      <div class="user-card__avatar user-card__avatar--large">
        <img :src="avatar" alt="用户头像">
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.user-card {
  border: 1px solid #ddd;
  border-radius: 4px;
  
  &__header {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #eee;
  }
  
  &__title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  &__close-btn {
    background: none;
    border: none;
    cursor: pointer;
    
    &:hover {
      color: #f56c6c;
    }
  }
  
  &__content {
    padding: 16px;
  }
  
  &__avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    
    &--large {
      width: 60px;
      height: 60px;
    }
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style>
```

### 5.2 CSS变量使用
```scss
// styles/variables.scss
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  
  --font-size-small: 12px;
  --font-size-base: 14px;
  --font-size-large: 16px;
  
  --border-radius-small: 2px;
  --border-radius-base: 4px;
  --border-radius-large: 6px;
  
  --spacing-small: 8px;
  --spacing-base: 16px;
  --spacing-large: 24px;
}

// 组件中使用
.user-card {
  border-radius: var(--border-radius-base);
  padding: var(--spacing-base);
  color: var(--primary-color);
}
```

## 6. 组件通信规范

### 6.1 父子组件通信
```vue
<!-- 父组件 -->
<template>
  <UserForm
    :user="currentUser"
    :loading="formLoading"
    @submit="handleUserSubmit"
    @cancel="handleUserCancel"
  />
</template>

<!-- 子组件 -->
<template>
  <el-form @submit="handleSubmit">
    <!-- 表单内容 -->
  </el-form>
</template>

<script>
export default {
  props: {
    user: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['submit', 'cancel'],
  
  methods: {
    handleSubmit() {
      this.$emit('submit', this.formData)
    }
  }
}
</script>
```

### 6.2 兄弟组件通信
```javascript
// 使用事件总线（Vue2）
// utils/eventBus.js
import Vue from 'vue'
export default new Vue()

// 组件A
import EventBus from '@/utils/eventBus'
EventBus.$emit('user-updated', userData)

// 组件B
import EventBus from '@/utils/eventBus'
EventBus.$on('user-updated', this.handleUserUpdate)

// 使用Provide/Inject（Vue3）
// 父组件
provide('userService', {
  updateUser: this.updateUser,
  deleteUser: this.deleteUser
})

// 子组件
const userService = inject('userService')
```

## 7. 组件文档规范

### 7.1 组件注释
```vue
<template>
  <!-- 用户信息卡片组件 -->
  <div class="user-card">
    <!-- 用户头像 -->
    <div class="user-card__avatar">
      <img :src="user.avatar" :alt="user.name">
    </div>
    
    <!-- 用户信息 -->
    <div class="user-card__info">
      <h3>{{ user.name }}</h3>
      <p>{{ user.email }}</p>
    </div>
  </div>
</template>

<script>
/**
 * 用户信息卡片组件
 * @description 展示用户基本信息的卡片组件
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0.0
 */
export default {
  name: 'UserCard',
  
  props: {
    /**
     * 用户信息对象
     * @type {Object}
     * @required true
     * @description 包含用户的基本信息
     */
    user: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  
  emits: [
    /**
     * 点击用户卡片时触发
     * @param {Object} user 用户信息
     */
    'click'
  ]
}
</script>
```

### 7.2 README文档
```markdown
# UserCard 用户卡片组件

## 描述
用于展示用户基本信息的卡片组件，支持点击交互。

## 使用示例
```vue
<template>
  <UserCard 
    :user="userInfo" 
    @click="handleUserClick"
  />
</template>

<script>
import UserCard from '@/components/UserCard.vue'

export default {
  components: {
    UserCard
  },
  data() {
    return {
      userInfo: {
        id: 1,
        name: '张三',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg'
      }
    }
  },
  methods: {
    handleUserClick(user) {
      console.log('点击了用户:', user)
    }
  }
}
</script>
```

## Props
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| user | Object | 是 | {} | 用户信息对象 |

## Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | user: Object | 点击用户卡片时触发 |

## Slots
| 插槽名 | 说明 |
|--------|------|
| default | 自定义内容 |
| actions | 操作按钮区域 |
```

## 8. 性能优化规范

### 8.1 组件懒加载
```javascript
// 路由懒加载
const routes = [
  {
    path: '/user',
    component: () => import('@/views/user/index.vue')
  }
]

// 组件懒加载
export default {
  components: {
    UserDialog: () => import('./components/UserDialog.vue')
  }
}
```

### 8.2 避免不必要的渲染
```vue
<template>
  <div>
    <!-- 使用v-show代替v-if（频繁切换） -->
    <div v-show="isVisible">内容</div>
    
    <!-- 使用key优化列表渲染 -->
    <div 
      v-for="item in items" 
      :key="item.id"
    >
      {{ item.name }}
    </div>
    
    <!-- 使用计算属性缓存复杂计算 -->
    <div>{{ expensiveCalculation }}</div>
  </div>
</template>

<script>
export default {
  computed: {
    expensiveCalculation() {
      // 复杂计算逻辑
      return this.items.reduce((sum, item) => sum + item.value, 0)
    }
  }
}
</script>
```

## 9. 测试规范

### 9.1 单元测试
```javascript
// tests/unit/UserCard.spec.js
import { mount } from '@vue/test-utils'
import UserCard from '@/components/UserCard.vue'

describe('UserCard.vue', () => {
  const mockUser = {
    id: 1,
    name: '张三',
    email: '<EMAIL>'
  }
  
  it('renders user information correctly', () => {
    const wrapper = mount(UserCard, {
      props: { user: mockUser }
    })
    
    expect(wrapper.text()).toContain('张三')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
  
  it('emits click event when clicked', async () => {
    const wrapper = mount(UserCard, {
      props: { user: mockUser }
    })
    
    await wrapper.trigger('click')
    expect(wrapper.emitted()).toHaveProperty('click')
    expect(wrapper.emitted().click[0]).toEqual([mockUser])
  })
})
```

## 10. 常见问题

### 10.1 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 组件应该易于在不同场景中复用
- **可维护性**: 代码结构清晰，易于理解和修改
- **可测试性**: 组件应该易于编写单元测试

### 10.2 避免的反模式
```vue
<!-- ❌ 错误示例 -->
<template>
  <div>
    <!-- 组件职责过多 -->
    <UserForm />
    <UserList />
    <UserStatistics />
  </div>
</template>

<!-- ✅ 正确示例 -->
<template>
  <div class="user-management">
    <!-- 单一职责，清晰的组件划分 -->
    <UserManagementHeader />
    <UserManagementContent />
    <UserManagementFooter />
  </div>
</template>
```

---

**遵循这些规范可以确保组件的质量、可维护性和团队协作效率。**
