package com.pinwan.mir.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ArticleAppendTypeEnum {
    FREE(0, "免费"),
    PAID(1, "付费");

    private final Integer type;
    private final String name;

    public ArticleAppendTypeEnum getArticleAppendTypeEnum(Integer type) {
        for (ArticleAppendTypeEnum articleAppendTypeEnum : ArticleAppendTypeEnum.values()) {
            if (articleAppendTypeEnum.getType().equals(type)) {
                return articleAppendTypeEnum;
            }
        }
        return null;
    }
}
