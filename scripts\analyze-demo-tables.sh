#!/bin/bash

# =============================================
# 分析Demo表脚本
# 基于代码分析，识别yudao框架的demo表和测试表
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "Demo表分析工具"
echo "========================================"

# 检查是否在项目根目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 请在项目根目录执行此脚本"
    exit 1
fi

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database

log_info "分析代码中的Demo表定义..."

# 从代码中提取demo表名
echo "从代码中发现的Demo表:" > /tmp/demo_tables_from_code.txt

# 查找所有demo相关的DO类中的@TableName注解
find . -name "*Demo*DO.java" -type f -exec grep -H "@TableName" {} \; | while read line; do
    # 提取表名
    table_name=$(echo "$line" | sed -n 's/.*@TableName.*[("]\([^"]*\)[")].*/\1/p')
    if [ -n "$table_name" ]; then
        echo "$table_name" >> /tmp/demo_tables_from_code.txt
        echo "  发现Demo表: $table_name (来源: $(echo "$line" | cut -d: -f1))"
    fi
done

log_info "获取数据库中的所有表..."

# 获取数据库中所有表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_db_tables.txt

log_info "生成Demo表分析报告..."

# 生成分析报告
cat > docs/database/demo-tables-analysis.md << 'EOF'
# Demo表分析报告

## 📊 概览

本报告基于代码分析和数据库检查，识别出项目中的Demo表和测试表，这些表通常可以安全删除。

## 🔍 分析依据

1. **代码分析**: 检查项目中的Demo相关DO类
2. **表名模式**: 匹配demo、test、sample等关键词
3. **表前缀**: 检查mir_demo、yudao_demo等前缀

## 🗑️ 发现的Demo表

### 1. 代码中定义的Demo表

基于项目代码中的DO类分析，发现以下Demo表：

EOF

# 检查代码中定义的demo表是否在数据库中存在
echo "" >> docs/database/demo-tables-analysis.md
while read table_name; do
    if [ -n "$table_name" ] && [ "$table_name" != "从代码中发现的Demo表:" ]; then
        # 检查表是否在数据库中存在
        if grep -q "^$table_name$" /tmp/all_db_tables.txt; then
            # 获取表信息
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO ✅ 存在于数据库" >> docs/database/demo-tables-analysis.md
            echo "$table_name" >> /tmp/confirmed_demo_tables.txt
        else
            echo "- **$table_name**: ❌ 不存在于数据库（可能已删除）" >> docs/database/demo-tables-analysis.md
        fi
    fi
done < /tmp/demo_tables_from_code.txt

# 检查数据库中其他可能的demo表
echo "" >> docs/database/demo-tables-analysis.md
echo "### 2. 数据库中的其他Demo/测试表" >> docs/database/demo-tables-analysis.md
echo "" >> docs/database/demo-tables-analysis.md

# 查找数据库中所有可能的demo/test表
POSSIBLE_DEMO_TABLES=$(grep -iE "demo|test|sample|temp|example" /tmp/all_db_tables.txt 2>/dev/null || echo "")

if [ -n "$POSSIBLE_DEMO_TABLES" ]; then
    echo "发现以下可能的Demo/测试表:" >> docs/database/demo-tables-analysis.md
    echo "" >> docs/database/demo-tables-analysis.md
    
    echo "$POSSIBLE_DEMO_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/demo-tables-analysis.md
            echo "$table_name" >> /tmp/confirmed_demo_tables.txt
        fi
    done
else
    echo "✅ 未发现其他Demo/测试表" >> docs/database/demo-tables-analysis.md
fi

echo "" >> docs/database/demo-tables-analysis.md

# 检查yudao相关表
echo "### 3. YuDao框架相关表" >> docs/database/demo-tables-analysis.md
echo "" >> docs/database/demo-tables-analysis.md

YUDAO_TABLES=$(grep -iE "yudao|yu_dao" /tmp/all_db_tables.txt 2>/dev/null || echo "")

if [ -n "$YUDAO_TABLES" ]; then
    echo "发现以下YuDao框架相关表:" >> docs/database/demo-tables-analysis.md
    echo "" >> docs/database/demo-tables-analysis.md
    
    echo "$YUDAO_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/demo-tables-analysis.md
            echo "$table_name" >> /tmp/confirmed_demo_tables.txt
        fi
    done
else
    echo "✅ 未发现YuDao框架相关表" >> docs/database/demo-tables-analysis.md
fi

echo "" >> docs/database/demo-tables-analysis.md

# 生成删除建议
echo "## 🗑️ 删除建议" >> docs/database/demo-tables-analysis.md
echo "" >> docs/database/demo-tables-analysis.md

# 统计demo表数量
DEMO_COUNT=0
if [ -f "/tmp/confirmed_demo_tables.txt" ]; then
    DEMO_COUNT=$(sort /tmp/confirmed_demo_tables.txt | uniq | wc -l)
fi

if [ $DEMO_COUNT -gt 0 ]; then
    echo "### 可以安全删除的Demo表" >> docs/database/demo-tables-analysis.md
    echo "" >> docs/database/demo-tables-analysis.md
    echo "发现 $DEMO_COUNT 个Demo/测试表，建议删除以清理数据库:" >> docs/database/demo-tables-analysis.md
    echo "" >> docs/database/demo-tables-analysis.md
    
    # 生成删除SQL
    echo '```sql' >> docs/database/demo-tables-analysis.md
    echo "-- =============================================" >> docs/database/demo-tables-analysis.md
    echo "-- 删除Demo和测试表" >> docs/database/demo-tables-analysis.md
    echo "-- 执行前请确认并备份数据库" >> docs/database/demo-tables-analysis.md
    echo "-- =============================================" >> docs/database/demo-tables-analysis.md
    echo "" >> docs/database/demo-tables-analysis.md
    echo "SET SQL_SAFE_UPDATES = 0;" >> docs/database/demo-tables-analysis.md
    echo "" >> docs/database/demo-tables-analysis.md
    
    sort /tmp/confirmed_demo_tables.txt | uniq | while read table_name; do
        if [ -n "$table_name" ]; then
            echo "DROP TABLE IF EXISTS $table_name;" >> docs/database/demo-tables-analysis.md
        fi
    done
    
    echo "" >> docs/database/demo-tables-analysis.md
    echo "SET SQL_SAFE_UPDATES = 1;" >> docs/database/demo-tables-analysis.md
    echo '```' >> docs/database/demo-tables-analysis.md
    echo "" >> docs/database/demo-tables-analysis.md
    
    # 生成单独的删除脚本
    cat > docs/database/delete-demo-tables.sql << 'EOF'
-- =============================================
-- 删除Demo和测试表的SQL脚本
-- 注意：执行前请备份数据库
-- =============================================

SET SQL_SAFE_UPDATES = 0;

EOF
    
    echo "-- Demo和测试表删除" >> docs/database/delete-demo-tables.sql
    sort /tmp/confirmed_demo_tables.txt | uniq | while read table_name; do
        if [ -n "$table_name" ]; then
            echo "DROP TABLE IF EXISTS $table_name;" >> docs/database/delete-demo-tables.sql
        fi
    done
    
    echo "" >> docs/database/delete-demo-tables.sql
    echo "SET SQL_SAFE_UPDATES = 1;" >> docs/database/delete-demo-tables.sql
    echo "" >> docs/database/delete-demo-tables.sql
    echo "SELECT 'Demo表删除完成' AS message;" >> docs/database/delete-demo-tables.sql
    
    log_success "删除脚本已生成: docs/database/delete-demo-tables.sql"
else
    echo "✅ 未发现需要删除的Demo表" >> docs/database/demo-tables-analysis.md
    echo "" >> docs/database/demo-tables-analysis.md
fi

# 添加注意事项
echo "## ⚠️ 注意事项" >> docs/database/demo-tables-analysis.md
echo "" >> docs/database/demo-tables-analysis.md
echo "1. **备份数据**: 删除表前务必备份数据库" >> docs/database/demo-tables-analysis.md
echo "2. **确认环境**: 确保在正确的环境中执行（开发/测试/生产）" >> docs/database/demo-tables-analysis.md
echo "3. **代码清理**: 删除表后，建议同时清理相关的Java代码" >> docs/database/demo-tables-analysis.md
echo "4. **测试验证**: 删除后测试系统功能是否正常" >> docs/database/demo-tables-analysis.md
echo "" >> docs/database/demo-tables-analysis.md

echo "## 📋 相关代码文件" >> docs/database/demo-tables-analysis.md
echo "" >> docs/database/demo-tables-analysis.md
echo "以下代码文件可能需要一并清理:" >> docs/database/demo-tables-analysis.md
echo "" >> docs/database/demo-tables-analysis.md

# 列出demo相关的代码文件
find . -name "*Demo*" -type f | grep -E "\.(java|xml|sql)$" | while read file; do
    echo "- $file" >> docs/database/demo-tables-analysis.md
done

echo "" >> docs/database/demo-tables-analysis.md
echo "---" >> docs/database/demo-tables-analysis.md
echo "**生成时间**: $(date)" >> docs/database/demo-tables-analysis.md

log_success "Demo表分析完成: docs/database/demo-tables-analysis.md"

# 清理临时文件
rm -f /tmp/demo_tables_from_code.txt /tmp/all_db_tables.txt /tmp/confirmed_demo_tables.txt

echo
echo "========================================"
echo "Demo表分析完成！"
echo "========================================"
echo "📋 docs/database/demo-tables-analysis.md - 详细分析报告"
if [ $DEMO_COUNT -gt 0 ]; then
    echo "🗑️ docs/database/delete-demo-tables.sql - 删除脚本"
    echo "📊 发现 $DEMO_COUNT 个Demo表可以删除"
fi
echo "========================================"
