# Football Lottery SaaS多租户改造方案

## 📋 改造概述

基于现有项目的多租户基础设施，进行全面的SaaS化改造，实现完整的租户隔离和数据安全。

## 🔍 现状分析

### 已有多租户支持
- ✅ **框架支持**: `mir-spring-boot-starter-biz-tenant` 多租户组件
- ✅ **基础类**: `TenantBaseDO` 提供租户字段支持
- ✅ **拦截器**: `TenantDatabaseInterceptor` 自动添加租户条件
- ✅ **上下文**: `TenantContextHolder` 租户上下文管理
- ✅ **配置**: 多租户开关和忽略表配置

### 存在的问题
- ❌ **多租户未启用**: `mir.tenant.enable: false`
- ❌ **实体类不统一**: 部分DO类继承`BaseDO`而非`TenantBaseDO`
- ❌ **数据库表缺失**: 部分表缺少`tenant_id`字段
- ❌ **接口未处理**: 大部分接口未处理租户隔离
- ❌ **前端未适配**: 前端未传递租户信息

## 🎯 改造目标

1. **完整租户隔离**: 所有业务数据按租户隔离
2. **统一租户管理**: 租户创建、配置、监控
3. **安全访问控制**: 防止跨租户数据访问
4. **灵活套餐配置**: 支持不同租户套餐
5. **前后端协同**: 前后端完整的租户支持

## 📊 改造范围分析

### 需要改造的实体类
| 模块 | 实体类 | 当前状态 | 改造动作 |
|------|--------|----------|----------|
| 会员模块 | MemberUserDO | 继承BaseDO | 改为TenantBaseDO |
| 会员模块 | MemberLevelDO | 继承BaseDO | 改为TenantBaseDO |
| 会员模块 | MemberLevelRecordDO | 继承BaseDO | 改为TenantBaseDO |
| 支付模块 | PayAppDO | 继承BaseDO | 改为TenantBaseDO |
| 支付模块 | PayOrderDO | 继承BaseDO | 改为TenantBaseDO |
| 支付模块 | PayRefundDO | 继承BaseDO | 改为TenantBaseDO |
| 支付模块 | PayWalletDO | 继承BaseDO | 改为TenantBaseDO |
| 业务模块 | ArticleDO | 继承BaseDO | 改为TenantBaseDO |
| 业务模块 | ArticleAppendDO | 继承BaseDO | 改为TenantBaseDO |
| 业务模块 | MatchTeamDO | 无继承 | 改为TenantBaseDO |

### 已支持多租户的实体类
| 模块 | 实体类 | 状态 |
|------|--------|------|
| 系统模块 | DeptDO | ✅ 继承TenantBaseDO |
| 支付模块 | PayChannelDO | ✅ 继承TenantBaseDO |
| 支付模块 | PayNotifyTaskDO | ✅ 继承TenantBaseDO |

## 🔧 详细改造方案

### 1. 数据库表结构改造

#### 1.1 添加tenant_id字段
```sql
-- 会员相关表
ALTER TABLE member_user ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE member_level ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE member_level_record ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE member_point_record ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE member_sign_in_record ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';

-- 支付相关表
ALTER TABLE pay_app ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE pay_order ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE pay_refund ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE pay_wallet ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE pay_wallet_transaction ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';

-- 业务相关表
ALTER TABLE article ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE author_article_append ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE match_team ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE banner ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
ALTER TABLE gold_order ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
```

#### 1.2 创建索引
```sql
-- 为tenant_id字段创建索引，提高查询性能
CREATE INDEX idx_member_user_tenant_id ON member_user(tenant_id);
CREATE INDEX idx_member_level_tenant_id ON member_level(tenant_id);
CREATE INDEX idx_pay_app_tenant_id ON pay_app(tenant_id);
CREATE INDEX idx_pay_order_tenant_id ON pay_order(tenant_id);
CREATE INDEX idx_pay_wallet_tenant_id ON pay_wallet(tenant_id);
CREATE INDEX idx_article_tenant_id ON article(tenant_id);

-- 创建复合索引
CREATE INDEX idx_member_user_tenant_mobile ON member_user(tenant_id, mobile);
CREATE INDEX idx_pay_order_tenant_status ON pay_order(tenant_id, status);
CREATE INDEX idx_article_tenant_status ON article(tenant_id, status);
```

### 2. 实体类改造

#### 2.1 会员模块实体类改造
```java
// MemberUserDO.java - 改造前后对比
// 改造前
public class MemberUserDO extends BaseDO {
    // 字段定义...
}

// 改造后
public class MemberUserDO extends TenantBaseDO {
    // 字段定义...
}
```

#### 2.2 支付模块实体类改造
```java
// PayAppDO.java
public class PayAppDO extends TenantBaseDO {
    // 原有字段保持不变
}

// PayOrderDO.java
public class PayOrderDO extends TenantBaseDO {
    // 原有字段保持不变
}

// PayWalletDO.java
public class PayWalletDO extends TenantBaseDO {
    // 原有字段保持不变
}
```

#### 2.3 业务模块实体类改造
```java
// ArticleDO.java
public class ArticleDO extends TenantBaseDO {
    // 原有字段保持不变
}

// MatchTeamDO.java - 需要添加基础字段
public class MatchTeamDO extends TenantBaseDO {
    // 原有字段保持不变
}
```

### 3. 配置文件改造

#### 3.1 启用多租户
```yaml
# application.yaml
mir:
  tenant:
    enable: true  # 启用多租户
    ignore-urls:
      - /admin-api/system/tenant/get-id-by-name
      - /admin-api/system/tenant/get-by-website
      - /admin-api/system/captcha/get
      - /admin-api/system/captcha/check
      - /admin-api/infra/file/*/get/**
      - /admin-api/system/sms/callback/*
      - /admin-api/pay/notify/**
      - /admin-api/mp/open/**
    ignore-tables:
      - system_tenant
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_access_token
      - system_oauth2_refresh_token
      - system_oauth2_code
      - infra_api_access_log
      - infra_api_error_log
      - infra_job
      - infra_job_log
      - infra_data_source_config
```

### 4. 接口层改造

#### 4.1 租户上下文处理
```java
// 在Controller基类中添加租户验证
@RestController
public abstract class BaseController {
    
    @Resource
    private TenantFrameworkService tenantFrameworkService;
    
    protected void validateTenant(Long tenantId) {
        tenantFrameworkService.validTenant(tenantId);
    }
    
    protected Long getCurrentTenantId() {
        return TenantContextHolder.getTenantId();
    }
}
```

#### 4.2 会员接口改造
```java
@RestController
@RequestMapping("/admin-api/member/user")
public class MemberUserController extends BaseController {
    
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('member:user:query')")
    public CommonResult<PageResult<MemberUserRespVO>> getMemberUserPage(
            @Valid MemberUserPageReqVO pageVO) {
        // 自动注入租户ID，无需手动处理
        PageResult<MemberUserDO> pageResult = memberUserService.getMemberUserPage(pageVO);
        return success(BeanUtils.toBean(pageResult, MemberUserRespVO.class));
    }
}
```

#### 4.3 支付接口改造
```java
@RestController
@RequestMapping("/admin-api/pay/app")
public class PayAppController extends BaseController {
    
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('pay:app:create')")
    public CommonResult<Long> createPayApp(@Valid @RequestBody PayAppSaveReqVO createReqVO) {
        // 自动注入租户ID
        return success(payAppService.createPayApp(createReqVO));
    }
}
```

### 5. 服务层改造

#### 5.1 租户数据初始化
```java
@Service
public class TenantInitService {
    
    @Resource
    private MemberLevelService memberLevelService;
    
    @Resource
    private PayAppService payAppService;
    
    /**
     * 初始化租户基础数据
     */
    @TenantIgnore // 忽略租户限制
    public void initTenantData(Long tenantId) {
        TenantContextHolder.setTenantId(tenantId);
        try {
            // 初始化会员等级
            initMemberLevels(tenantId);
            
            // 初始化支付应用
            initPayApp(tenantId);
            
            // 初始化其他基础数据...
        } finally {
            TenantContextHolder.clear();
        }
    }
    
    private void initMemberLevels(Long tenantId) {
        // 创建默认会员等级
        List<MemberLevelDO> levels = Arrays.asList(
            MemberLevelDO.builder()
                .name("青铜会员").level(1).experience(0)
                .discountPercent(100).status(1).build(),
            MemberLevelDO.builder()
                .name("白银会员").level(2).experience(1000)
                .discountPercent(95).status(1).build(),
            MemberLevelDO.builder()
                .name("黄金会员").level(3).experience(5000)
                .discountPercent(90).status(1).build()
        );
        
        levels.forEach(memberLevelService::createMemberLevel);
    }
}
```

### 6. 前端改造

#### 6.1 租户上下文管理
```typescript
// stores/tenant.ts
export const useTenantStore = defineStore('tenant', () => {
  const tenantId = ref<number | null>(null)
  const tenantInfo = ref<TenantInfo | null>(null)
  
  const setTenant = (id: number, info: TenantInfo) => {
    tenantId.value = id
    tenantInfo.value = info
    localStorage.setItem('tenantId', id.toString())
  }
  
  const clearTenant = () => {
    tenantId.value = null
    tenantInfo.value = null
    localStorage.removeItem('tenantId')
  }
  
  return {
    tenantId,
    tenantInfo,
    setTenant,
    clearTenant
  }
})
```

#### 6.2 请求拦截器改造
```typescript
// utils/request.ts
service.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加token
    const token = getToken()
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加租户ID
    const tenantStore = useTenantStore()
    if (tenantStore.tenantId && config.headers) {
      config.headers['X-Tenant-ID'] = tenantStore.tenantId.toString()
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)
```

#### 6.3 登录流程改造
```vue
<!-- views/login/index.vue -->
<template>
  <div class="login-container">
    <!-- 租户选择 -->
    <el-form-item prop="tenantName">
      <el-input
        v-model="loginForm.tenantName"
        placeholder="请输入租户名称"
        prefix-icon="OfficeBuilding"
      />
    </el-form-item>
    
    <!-- 用户名密码等其他字段 -->
  </div>
</template>

<script setup lang="ts">
const handleLogin = async () => {
  try {
    // 1. 根据租户名获取租户ID
    const tenantResponse = await getTenantIdByName(loginForm.tenantName)
    const tenantId = tenantResponse.data
    
    if (!tenantId) {
      ElMessage.error('租户不存在')
      return
    }
    
    // 2. 设置租户上下文
    const tenantStore = useTenantStore()
    tenantStore.setTenant(tenantId, { name: loginForm.tenantName })
    
    // 3. 执行登录
    await userStore.login({
      username: loginForm.username,
      password: loginForm.password,
      tenantId: tenantId
    })
    
    ElMessage.success('登录成功')
    router.push('/')
  } catch (error) {
    console.error('登录失败:', error)
  }
}
</script>
```

## 📈 数据迁移方案

### 1. 现有数据处理
```sql
-- 为现有数据设置默认租户ID（假设默认租户ID为1）
UPDATE member_user SET tenant_id = 1 WHERE tenant_id = 0;
UPDATE member_level SET tenant_id = 1 WHERE tenant_id = 0;
UPDATE pay_app SET tenant_id = 1 WHERE tenant_id = 0;
UPDATE pay_order SET tenant_id = 1 WHERE tenant_id = 0;
UPDATE article SET tenant_id = 1 WHERE tenant_id = 0;

-- 创建默认租户
INSERT INTO system_tenant (id, name, contact_name, contact_mobile, status, package_id, expire_time, account_count)
VALUES (1, '默认租户', '系统管理员', '***********', 0, 0, '2099-12-31 23:59:59', 100);
```

### 2. 数据验证脚本
```sql
-- 检查是否有遗漏的数据
SELECT 'member_user' as table_name, COUNT(*) as count FROM member_user WHERE tenant_id = 0
UNION ALL
SELECT 'pay_order' as table_name, COUNT(*) as count FROM pay_order WHERE tenant_id = 0
UNION ALL
SELECT 'article' as table_name, COUNT(*) as count FROM article WHERE tenant_id = 0;
```

## 🔒 安全加固

### 1. 租户隔离验证
```java
@Component
public class TenantSecurityAspect {
    
    @Around("@annotation(PreAuthorize)")
    public Object checkTenantAccess(ProceedingJoinPoint joinPoint) throws Throwable {
        Long currentTenantId = TenantContextHolder.getTenantId();
        if (currentTenantId == null) {
            throw new ServiceException(TENANT_NOT_EXISTS);
        }
        
        // 验证租户状态
        tenantFrameworkService.validTenant(currentTenantId);
        
        return joinPoint.proceed();
    }
}
```

### 2. 跨租户访问防护
```java
@Component
public class TenantDataPermissionHandler {
    
    public void checkDataPermission(Object data) {
        if (data instanceof TenantBaseDO) {
            TenantBaseDO tenantData = (TenantBaseDO) data;
            Long currentTenantId = TenantContextHolder.getTenantId();
            
            if (!Objects.equals(tenantData.getTenantId(), currentTenantId)) {
                throw new ServiceException(TENANT_DATA_ACCESS_DENIED);
            }
        }
    }
}
```

## 📋 改造检查清单

### 数据库改造
- [ ] 所有业务表添加tenant_id字段
- [ ] 创建必要的索引
- [ ] 现有数据迁移
- [ ] 数据完整性验证

### 代码改造
- [ ] 实体类继承TenantBaseDO
- [ ] 启用多租户配置
- [ ] Controller层租户验证
- [ ] Service层租户处理
- [ ] 前端租户上下文

### 功能验证
- [ ] 租户注册流程
- [ ] 租户登录隔离
- [ ] 数据查询隔离
- [ ] 跨租户访问防护
- [ ] 租户套餐限制

### 性能优化
- [ ] 租户数据索引优化
- [ ] 查询性能测试
- [ ] 缓存策略调整
- [ ] 监控指标配置

## 🚀 部署建议

### 1. 分阶段部署
1. **第一阶段**: 数据库结构改造，代码改造
2. **第二阶段**: 启用多租户，功能测试
3. **第三阶段**: 前端改造，端到端测试
4. **第四阶段**: 生产环境部署，监控优化

### 2. 回滚方案
- 保留原有数据结构备份
- 准备快速回滚脚本
- 监控关键业务指标
- 制定应急处理流程

## 🚀 快速执行

### 自动化改造脚本
我们提供了完整的自动化改造脚本，可以一键完成整个改造过程：

```bash
# 1. 配置数据库连接
cp config/database.conf.example config/database.conf
vim config/database.conf

# 2. 执行改造脚本
chmod +x scripts/saas-migration.sh
./scripts/saas-migration.sh

# 3. 如需回滚
chmod +x scripts/saas-rollback.sh
./scripts/saas-rollback.sh
```

### 手动改造步骤
如果需要手动执行改造，请按以下顺序：

1. **数据库备份**
   ```bash
   mysqldump -u root -p mir > backup/database_backup_$(date +%Y%m%d).sql
   ```

2. **执行SQL脚本**
   ```bash
   mysql -u root -p mir < sql/tenant-migration/01-add-tenant-fields.sql
   mysql -u root -p mir < sql/tenant-migration/02-create-indexes.sql
   mysql -u root -p mir < sql/tenant-migration/03-migrate-data.sql
   ```

3. **修改配置文件**
   ```yaml
   # mir-server/src/main/resources/application.yaml
   mir:
     tenant:
       enable: true
   ```

4. **重新编译项目**
   ```bash
   mvn clean compile
   ```

## 📋 改造后验证

### 1. 功能验证
- [ ] 租户创建和管理
- [ ] 租户数据隔离
- [ ] 用户登录租户选择
- [ ] 跨租户访问防护
- [ ] 租户套餐限制

### 2. 性能验证
- [ ] 查询性能测试
- [ ] 索引效果验证
- [ ] 并发访问测试
- [ ] 内存使用监控

### 3. 安全验证
- [ ] 租户数据隔离测试
- [ ] 权限边界测试
- [ ] SQL注入防护测试
- [ ] 跨租户访问测试

## 🔧 故障排除

### 常见问题

**Q1: 数据库连接失败**
```bash
# 检查数据库服务状态
systemctl status mysql

# 检查连接参数
mysql -h localhost -u root -p
```

**Q2: 编译错误**
```bash
# 清理并重新编译
mvn clean
mvn compile -DskipTests
```

**Q3: 租户ID为0的数据**
```sql
-- 检查未迁移的数据
SELECT table_name, COUNT(*)
FROM information_schema.tables t
JOIN (
  SELECT 'member_user' as table_name, COUNT(*) as count FROM member_user WHERE tenant_id = 0
  UNION ALL
  SELECT 'pay_order' as table_name, COUNT(*) as count FROM pay_order WHERE tenant_id = 0
) c ON t.table_name = c.table_name
WHERE c.count > 0;
```

**Q4: 前端租户选择问题**
- 检查租户API接口是否正常
- 验证前端租户上下文设置
- 确认请求头中包含租户ID

### 性能优化建议

1. **索引优化**
   ```sql
   -- 分析慢查询
   SHOW PROCESSLIST;

   -- 检查索引使用情况
   EXPLAIN SELECT * FROM member_user WHERE tenant_id = 1;
   ```

2. **缓存策略**
   ```java
   // 租户信息缓存
   @Cacheable(value = "tenant", key = "#tenantId")
   public TenantDO getTenant(Long tenantId) {
       return tenantMapper.selectById(tenantId);
   }
   ```

3. **连接池配置**
   ```yaml
   spring:
     datasource:
       hikari:
         maximum-pool-size: 20
         minimum-idle: 5
   ```

## 📈 监控和运维

### 1. 监控指标
- 租户数量和增长趋势
- 各租户数据量统计
- 查询性能指标
- 错误率和响应时间

### 2. 日志配置
```xml
<!-- logback-spring.xml -->
<appender name="TENANT" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>logs/tenant.log</file>
    <encoder>
        <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{tenantId}] %logger{36} - %msg%n</pattern>
    </encoder>
</appender>
```

### 3. 健康检查
```java
@Component
public class TenantHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        // 检查租户服务状态
        return Health.up()
            .withDetail("activeTenants", getActiveTenantCount())
            .withDetail("expiredTenants", getExpiredTenantCount())
            .build();
    }
}
```

## 🔄 升级和维护

### 版本升级
1. 备份当前数据
2. 测试新版本兼容性
3. 执行升级脚本
4. 验证功能正常

### 数据维护
```sql
-- 定期清理过期数据
DELETE FROM system_oauth2_access_token WHERE expire_time < NOW();

-- 统计租户数据量
SELECT tenant_id, COUNT(*) as record_count
FROM member_user
GROUP BY tenant_id
ORDER BY record_count DESC;
```

---

**改造完成后，系统将具备完整的SaaS多租户能力，支持租户隔离、数据安全、灵活配置等企业级特性。**
