package com.pinwan.mir.framework.common.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付渠道枚举
 */
@Getter
@AllArgsConstructor
public enum RechargeChannelEnum {

    GOLD(0, "鱼币", "gold","",""),
    ALIPAY(1, "支付宝", "alipay_csb","pay.alipay.native","ALIPAY"),
    WXPAY(2, "微信", "wechat_csb","pay.weixin.native","WEIXIN"),
    PRIVILEGE_NUMBER(3, "包次特权", "privilege_number","",""),
    PRIVILEGE_TIME(4, "包时特权", "privilege_time","",""),
    PRIVILEGE_REPAIR(5, "补单特权", "privilege_time","",""),
            ;


    /**
     * 渠道值
     */
    private final Integer type;
    /**
     * 渠道名
     */
    private final String name;
    /**
     * 交易类型
     */
    private final String tradeType;


    private final String sdcfPayService;

    private final String jiuKaPayService;

    /**
     * 根据渠道值获取渠道枚举信息
     * @param type 渠道值
     * @return 渠道枚举信息
     */
    public static RechargeChannelEnum getByType(Integer type){
        return ArrayUtil.firstMatch(o -> o.getType().equals(type), values());
    }

}
