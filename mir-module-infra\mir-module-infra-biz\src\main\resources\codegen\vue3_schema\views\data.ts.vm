import type { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { dateFormatter } from '@/utils/formatTime'

// 表单校验
export const rules = reactive({
#foreach ($column in $columns)
#if (($column.createOperation || $column.updateOperation) && !$column.nullable && !${column.primaryKey})## 创建或者更新操作 && 要求非空 && 非主键
#set($comment=$column.columnComment)
  $column.javaField: [required],
#end
#end
})

// CrudSchema https://doc.iocoder.cn/vue3/crud-schema/
const crudSchemas = reactive<CrudSchema[]>([
#foreach($column in $columns)
#if ($column.listOperation || $column.listOperationResult || $column.createOperation || $column.updateOperation)
#set ($dictType = $column.dictType)
#set ($javaField = $column.javaField)
#set ($javaType = $column.javaType)
  {
    label: '${column.columnComment}',
    field: '${column.javaField}',
## ========= 字典部分 =========
    #if ("" != $dictType)## 有数据字典
    dictType: DICT_TYPE.$dictType.toUpperCase(),
        #if ($javaType == "Integer" || $javaType == "Long" || $javaType == "Byte" || $javaType == "Short")
    dictClass: 'number',
        #elseif ($javaType == "String")
    dictClass: 'string',
        #elseif ($javaType == "Boolean")
    dictClass: 'boolean',
        #end
    #end
## ========= Table 表格部分 =========
    #if (!$column.listOperationResult)
    isTable: false,
    #else
      #if ($column.htmlType == "datetime")
    formatter: dateFormatter,
      #end
    #end
## ========= Search 表格部分 =========
    #if ($column.listOperation)
    isSearch: true,
        #if ($column.htmlType == "datetime")
    search: {
      component: 'DatePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        type: 'daterange',
        defaultTime: [new Date('1 00:00:00'), new Date('1 23:59:59')]
      }
    },
        #end
    #end
## ========= Form 表单部分 =========
    #if ((!$column.createOperation && !$column.updateOperation) || $column.primaryKey)
    isForm: false,
    #else
        #if($column.htmlType == "imageUpload")## 图片上传
    form: {
      component: 'UploadImg'
    },
        #elseif($column.htmlType == "fileUpload")## 文件上传
    form: {
      component: 'UploadFile'
    },
        #elseif($column.htmlType == "editor")## 文本编辑器
    form: {
      component: 'Editor',
      componentProps: {
        valueHtml: '',
        height: 200
      }
    },
        #elseif($column.htmlType == "select")## 下拉框
    form: {
      component: 'SelectV2'
    },
        #elseif($column.htmlType == "checkbox")## 多选框
    form: {
      component: 'Checkbox'
    },
        #elseif($column.htmlType == "radio")## 单选框
    form: {
      component: 'Radio'
    },
        #elseif($column.htmlType == "datetime")## 时间框
    form: {
      component: 'DatePicker',
      componentProps: {
        type: 'datetime',
        valueFormat: 'x'
      }
    },
        #elseif($column.htmlType == "textarea")## 文本框
    form: {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        rows: 4
      },
      colProps: {
        span: 24
      }
    },
        #elseif(${javaType.toLowerCase()} == "long" || ${javaType.toLowerCase()} == "integer")## 文本框
    form: {
      component: 'InputNumber',
      value: 0
    },
        #end
    #end
  },
#end
#end
  {
    label: '操作',
    field: 'action',
    isForm: false
  }
])
export const { allSchemas } = useCrudSchemas(crudSchemas)
