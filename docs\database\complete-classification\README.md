# 完整数据库重构文档

## 📁 文件结构

```
docs/database/complete-classification/
├── complete-table-classification.md     # 完整表分类报告
├── complete-restructure-summary.md      # 完整重构总结报告
└── README.md                            # 使用说明

sql/complete-restructure/
├── 01-framework-tables.sql              # 平台框架表
├── 02-football-business-tables.sql      # 足彩业务表
├── 03-member-complete-tables.sql        # 会员功能表
├── 04-quartz-scheduler-tables.sql       # 作业调度表
├── 05-payment-complete-tables.sql       # 支付功能表
├── 06-wechat-complete-tables.sql        # 微信功能表
├── 07-infrastructure-complete-tables.sql # 基础设施表
├── 08-content-management-tables.sql     # 内容管理表
├── 09-log-tables.sql                    # 日志表
├── 10-unclassified-tables.sql           # 未分类表
├── 99-basic-data-complete.sql           # 基础数据
└── install-complete.sql                 # 主安装脚本
```

## 🚀 快速开始

### 1. 完整安装
```bash
# 安装包含所有191个表的完整数据库
cd sql/complete-restructure
mysql -u root -p < install-complete.sql
```

### 2. 查看表分类
```bash
cat docs/database/complete-classification/complete-table-classification.md
```

### 3. 查看重构总结
```bash
cat docs/database/complete-classification/complete-restructure-summary.md
```

## 📊 默认配置

- **数据库名**: football_lottery_complete
- **管理员账号**: admin
- **管理员密码**: admin123
- **字符集**: utf8mb4

## 🔧 自定义配置

### 修改数据库名
编辑 `install-complete.sql` 第一行：
```sql
CREATE DATABASE IF NOT EXISTS `your_database_name` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 模块化安装
根据需要选择安装的模块：
```bash
# 只安装核心模块
mysql -u root -p < 01-framework-tables.sql
mysql -u root -p < 03-member-complete-tables.sql
mysql -u root -p < 05-payment-complete-tables.sql
mysql -u root -p < 99-basic-data-complete.sql
```

## ⚠️ 注意事项

1. 确保MySQL版本 >= 5.7
2. 确保字符集为 utf8mb4
3. 执行前请备份现有数据
4. 建议先在测试环境验证

## 🆘 问题排查

### 常见问题
1. **字符集问题**: 确保数据库和表都使用 utf8mb4
2. **权限问题**: 确保用户有创建数据库和表的权限
3. **外键约束**: 如果有外键错误，检查表的创建顺序

### 获取帮助
- 查看详细的分类报告
- 检查SQL脚本的注释
- 联系系统管理员
