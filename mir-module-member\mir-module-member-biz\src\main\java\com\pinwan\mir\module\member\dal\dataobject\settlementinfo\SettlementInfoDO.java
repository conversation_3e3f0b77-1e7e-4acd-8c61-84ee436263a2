package com.pinwan.mir.module.member.dal.dataobject.settlementinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;

/**
 * 用户提现账号信息 DO
 *
 * <AUTHOR>
 */
@TableName("member_settlement_info")
@KeySequence("member_settlement_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementInfoDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 提现方式：1:支付宝 2:银行卡
     */
    private Long settleType;
    /**
     * 提现账号
     */
    private String account;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号码
     */
    private String idNo;
    /**
     * 开户银行ID
     */
    private Long bankId;

}