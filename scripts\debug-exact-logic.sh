#!/bin/bash

# =============================================
# 精确复制fixed-table-analyzer.sh的逻辑进行调试
# =============================================

echo "========================================"
echo "精确复制fixed-table-analyzer.sh的逻辑"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

echo "数据库: $DB_NAME"

# 获取前5个表 - 完全相同的方法
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null | head -5 > ./debug_exact_tables.txt

TOTAL_TABLES=$(wc -l < ./debug_exact_tables.txt)
echo "处理 $TOTAL_TABLES 个表"

echo "表列表内容:"
cat ./debug_exact_tables.txt
echo

# 使用完全相同的for循环
table_count=0
for table_name in $(cat ./debug_exact_tables.txt); do
    table_count=$((table_count + 1))
    echo "=== 处理表 $table_count: $table_name ==="
    
    # 1. 测试表注释获取 - 完全相同的方法
    echo "步骤1: 获取表注释"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
        --batch --skip-column-names 2>/dev/null > ./debug_exact_comment_${table_name}.txt
    
    echo "  表注释文件大小: $(wc -c < ./debug_exact_comment_${table_name}.txt) 字节"
    
    if [ -s "./debug_exact_comment_${table_name}.txt" ]; then
        TABLE_COMMENT=$(cat ./debug_exact_comment_${table_name}.txt)
        echo "  ✅ 表注释: '$TABLE_COMMENT'"
    else
        echo "  ❌ 表注释: 无法获取"
    fi
    
    # 2. 测试字段获取 - 完全相同的方法
    echo "步骤2: 获取字段详情"
    temp_file="./debug_exact_columns_${table_name}.txt"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, IFNULL(COLUMN_KEY,''), IFNULL(COLUMN_DEFAULT,''), IFNULL(COLUMN_COMMENT,'') FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' ORDER BY ORDINAL_POSITION;" \
        --batch --skip-column-names 2>/dev/null > "$temp_file"
    
    echo "  字段文件: $temp_file"
    echo "  字段文件大小: $(wc -c < "$temp_file") 字节"
    echo "  字段文件行数: $(wc -l < "$temp_file") 行"
    
    if [ -s "$temp_file" ]; then
        echo "  ✅ 字段查询成功"
        echo "  前3行内容:"
        head -3 "$temp_file" | while read -r line; do
            echo "    $line"
        done
    else
        echo "  ❌ 字段查询失败"
        
        # 额外调试信息
        echo "  调试信息:"
        echo "    文件是否存在: $([ -f "$temp_file" ] && echo "是" || echo "否")"
        echo "    文件权限: $(ls -l "$temp_file" 2>/dev/null || echo "无法获取")"
        echo "    当前目录: $(pwd)"
        echo "    当前用户: $(whoami)"
        
        # 尝试简单查询
        echo "  尝试简单查询:"
        simple_result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        echo "    简单查询结果: '$simple_result'"
    fi
    
    echo
done

# 检查临时文件
echo "=== 检查生成的临时文件 ==="
ls -la ./debug_exact_* 2>/dev/null || echo "没有找到临时文件"

# 清理
echo
echo "是否清理临时文件? (y/n)"
read -r cleanup
if [ "$cleanup" = "y" ]; then
    rm -f ./debug_exact_*
    echo "临时文件已清理"
fi

echo
echo "========================================"
echo "调试完成！"
echo "========================================"
