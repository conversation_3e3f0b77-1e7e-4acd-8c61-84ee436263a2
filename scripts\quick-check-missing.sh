#!/bin/bash

# =============================================
# 快速检查遗漏表脚本
# 快速检查重构方案是否还有重要遗漏
# =============================================

echo "========================================"
echo "快速检查遗漏表工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi

echo "✅ 数据库连接成功"
echo

# 获取所有表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

TOTAL_TABLES=$(wc -l < /tmp/all_tables.txt)

echo "📊 数据库总表数: $TOTAL_TABLES"
echo

# 定义重构方案中已包含的表（更新后的完整列表）
INCLUDED_TABLES=(
    # 框架表
    "system_tenant" "system_users" "system_role" "system_menu" "system_user_role" "system_role_menu"
    "system_dict_type" "system_dict_data"
    
    # 后台前端表
    "system_dept" "system_post" "system_notice" "infra_file" "infra_file_config"
    
    # 应用功能表
    "member_user" "member_level" "pay_app" "pay_order" "pay_wallet" "author_article" "banner"
    
    # 微信表
    "mp_account" "mp_user" "mp_message" "mp_menu" "mp_template_config" "mp_tag"
    "mp_mini_user" "mp_material" "mp_auto_reply" "mp_click_logs" "mp_other_even_logs" "mp_pay_config_log"
    "wx_work_setting" "wx_external_contact" "wx_external_contact_way_config"
    
    # 重要遗漏表（新增）
    "system_mail_template" "system_notify_template" "system_sms_template"
    "system_mail_account" "system_sms_channel" "infra_config"
    "system_oauth2_client" "infra_job" "infra_codegen_table" "infra_codegen_column"
)

INCLUDED_COUNT=${#INCLUDED_TABLES[@]}
echo "📋 重构方案包含表数: $INCLUDED_COUNT"

# 检查遗漏的重要表
echo
echo "🔍 检查重要表类型的遗漏情况："

# 1. 模板表
echo
echo "📧 模板表检查："
TEMPLATE_TABLES=$(grep -E "template" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$TEMPLATE_TABLES" ]; then
    echo "$TEMPLATE_TABLES" | while read table; do
        if [[ " ${INCLUDED_TABLES[@]} " =~ " $table " ]]; then
            echo "  ✅ $table - 已包含"
        else
            echo "  ❌ $table - 遗漏"
        fi
    done
else
    echo "  ✅ 未发现模板表"
fi

# 2. 日志表
echo
echo "📝 日志表检查："
LOG_TABLES=$(grep -E "log|logs" /tmp/all_tables.txt | head -5 2>/dev/null || echo "")
if [ -n "$LOG_TABLES" ]; then
    echo "$LOG_TABLES" | while read table; do
        if [[ " ${INCLUDED_TABLES[@]} " =~ " $table " ]]; then
            echo "  ✅ $table - 已包含"
        else
            echo "  ⚠️ $table - 日志表（可选）"
        fi
    done
fi

# 3. 系统配置表
echo
echo "⚙️ 系统配置表检查："
CONFIG_TABLES=$(grep -E "^system_" /tmp/all_tables.txt | grep -E "config|account|channel|oauth2" 2>/dev/null || echo "")
if [ -n "$CONFIG_TABLES" ]; then
    echo "$CONFIG_TABLES" | while read table; do
        if [[ " ${INCLUDED_TABLES[@]} " =~ " $table " ]]; then
            echo "  ✅ $table - 已包含"
        else
            echo "  ❌ $table - 遗漏"
        fi
    done
fi

# 4. 基础设施表
echo
echo "🏗️ 基础设施表检查："
INFRA_TABLES=$(grep -E "^infra_" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$INFRA_TABLES" ]; then
    echo "$INFRA_TABLES" | while read table; do
        if [[ " ${INCLUDED_TABLES[@]} " =~ " $table " ]]; then
            echo "  ✅ $table - 已包含"
        else
            echo "  ❌ $table - 遗漏"
        fi
    done
fi

# 5. Demo表检查
echo
echo "🗑️ Demo表检查："
DEMO_TABLES=$(grep -iE "demo|test|yudao" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$DEMO_TABLES" ]; then
    echo "$DEMO_TABLES" | while read table; do
        echo "  🗑️ $table - 可删除"
    done
else
    echo "  ✅ 未发现Demo表"
fi

# 统计覆盖率
echo
echo "📊 覆盖率统计："
COVERAGE=$(( INCLUDED_COUNT * 100 / TOTAL_TABLES ))
echo "  - 总表数: $TOTAL_TABLES"
echo "  - 已包含: $INCLUDED_COUNT"
echo "  - 覆盖率: $COVERAGE%"

if [ $COVERAGE -ge 80 ]; then
    echo "  ✅ 覆盖率良好"
elif [ $COVERAGE -ge 60 ]; then
    echo "  ⚠️ 覆盖率一般，建议检查遗漏"
else
    echo "  ❌ 覆盖率较低，需要补充重要表"
fi

# 生成快速遗漏列表
echo
echo "📋 快速遗漏表列表："
> /tmp/quick_missing.txt

while read table_name; do
    if [ -n "$table_name" ]; then
        if [[ ! " ${INCLUDED_TABLES[@]} " =~ " $table_name " ]]; then
            echo "$table_name" >> /tmp/quick_missing.txt
        fi
    fi
done < /tmp/all_tables.txt

MISSING_COUNT=$(wc -l < /tmp/quick_missing.txt 2>/dev/null || echo "0")
echo "遗漏表数: $MISSING_COUNT"

if [ $MISSING_COUNT -gt 0 ] && [ $MISSING_COUNT -le 20 ]; then
    echo
    echo "遗漏的表："
    head -20 /tmp/quick_missing.txt | while read table; do
        echo "  - $table"
    done
fi

# 清理临时文件
rm -f /tmp/all_tables.txt /tmp/quick_missing.txt

echo
echo "========================================"
echo "快速检查完成！"
echo "========================================"
echo "💡 建议："
echo "1. 如果覆盖率 < 80%，运行完整分析: ./scripts/analyze-missing-tables.sh"
echo "2. 检查遗漏的模板表和配置表是否需要补充"
echo "3. Demo表可以安全删除"
echo "========================================"
