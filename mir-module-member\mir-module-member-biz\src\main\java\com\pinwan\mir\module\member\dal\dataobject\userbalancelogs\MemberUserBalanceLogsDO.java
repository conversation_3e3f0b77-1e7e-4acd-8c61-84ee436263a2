package com.pinwan.mir.module.member.dal.dataobject.userbalancelogs;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 用户余额变更记录 DO
 *
 * <AUTHOR>
 */
@TableName("member_user_balance_logs")
@KeySequence("member_user_balance_logs_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberUserBalanceLogsDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 修改前余额
     */
    private BigDecimal beforeAmount;
    /**
     * 修改的余额
     */
    private BigDecimal amount;
    /**
     * 修改后余额
     */
    private BigDecimal afterAmount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 分类
     */
    private Integer classify;
}