#!/bin/bash

# =============================================
# 直接表结构生成器
# 使用与调试脚本完全相同的方法
# =============================================

echo "========================================"
echo "直接表结构生成器"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

# 测试表名
TEST_TABLE="account_statistic"

echo "数据库: $DB_NAME"
echo "测试表: $TEST_TABLE"

# 创建输出目录
mkdir -p direct-analysis

# 开始生成文件
cat > direct-analysis/table-structure-direct.md << EOF
# 数据库表结构详情

## 📊 数据库: $DB_NAME

**生成时间**: $(date)

EOF

echo "### 表: $TEST_TABLE" >> direct-analysis/table-structure-direct.md
echo "" >> direct-analysis/table-structure-direct.md

# 1. 获取表注释
echo "=== 获取表注释 ==="
TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$TEST_TABLE';" \
    --batch --skip-column-names 2>/dev/null)

echo "表注释: '$TABLE_COMMENT'"
echo "**表说明**: $TABLE_COMMENT" >> direct-analysis/table-structure-direct.md
echo "" >> direct-analysis/table-structure-direct.md

# 2. 生成表头
echo "| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |" >> direct-analysis/table-structure-direct.md
echo "|--------|------|----------|-----|--------|------|" >> direct-analysis/table-structure-direct.md

# 3. 获取字段信息（使用与调试脚本完全相同的方法）
echo "=== 获取字段信息 ==="
temp_file="./temp_columns_direct.txt"
echo "临时文件: $temp_file"

mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT 
        COLUMN_NAME,
        COLUMN_TYPE,
        IS_NULLABLE,
        IFNULL(COLUMN_KEY, ''),
        IFNULL(COLUMN_DEFAULT, ''),
        IFNULL(COLUMN_COMMENT, '')
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$TEST_TABLE' 
    ORDER BY ORDINAL_POSITION;" \
    --batch --skip-column-names 2>/dev/null > "$temp_file"

echo "临时文件大小: $(wc -c < "$temp_file") 字节"
echo "临时文件行数: $(wc -l < "$temp_file") 行"

if [ -s "$temp_file" ]; then
    echo "✅ 临时文件有内容，开始解析..."
    
    # 4. 解析字段信息
    line_num=0
    while IFS=$'\t' read -r col_name col_type is_null col_key col_default col_comment; do
        line_num=$((line_num + 1))
        
        # 跳过空行
        if [ -z "$col_name" ]; then
            echo "跳过空行: $line_num"
            continue
        fi
        
        echo "处理字段 $line_num: $col_name"
        
        # 处理特殊字符
        col_default=$(echo "$col_default" | sed 's/|/\\|/g' | sed 's/NULL//')
        col_comment=$(echo "$col_comment" | sed 's/|/\\|/g' | sed 's/NULL//')
        
        # 格式化键类型
        case "$col_key" in
            "PRI") col_key="🔑 PK" ;;
            "UNI") col_key="🔒 UK" ;;
            "MUL") col_key="📇 FK" ;;
            *) col_key="" ;;
        esac
        
        # 格式化是否为空
        case "$is_null" in
            "NO") is_null="❌" ;;
            "YES") is_null="✅" ;;
        esac
        
        # 写入文件
        echo "| $col_name | $col_type | $is_null | $col_key | $col_default | $col_comment |" >> direct-analysis/table-structure-direct.md
        
    done < "$temp_file"
    
    echo "✅ 字段解析完成"
else
    echo "❌ 临时文件为空"
    echo "| - | 无法获取字段信息 | - | - | - | 请检查数据库权限 |" >> direct-analysis/table-structure-direct.md
fi

# 清理
rm -f "$temp_file"

echo "" >> direct-analysis/table-structure-direct.md
echo "---" >> direct-analysis/table-structure-direct.md

echo
echo "========================================"
echo "直接表结构生成完成！"
echo "========================================"
echo "📊 表结构详情: direct-analysis/table-structure-direct.md"
echo "========================================"
