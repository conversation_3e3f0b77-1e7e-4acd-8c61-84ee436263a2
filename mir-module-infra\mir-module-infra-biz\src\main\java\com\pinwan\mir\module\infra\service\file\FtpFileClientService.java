package com.pinwan.mir.module.infra.service.file;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class FtpFileClientService {

    @Value("${mir.ftp.host}")
    private String host;
    @Value("${mir.ftp.port:21}")
    private Integer port;
    @Value("${mir.ftp.username}")
    private String username;
    @Value("${mir.ftp.password}")
    private String password;
    @Value("${mir.ftp.url}")
    private String url;


    public String uploadFile(String targetPath, String fileName, InputStream inputStream) throws IOException {
        FTPClient ftpClient = new FTPClient();
        try {
            ftpClient.connect(host, port);
            ftpClient.login(username, password);
            ftpClient.enterLocalPassiveMode();
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            // 转换文件名编码
            fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            targetPath = new String(targetPath.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            // 切换到工作目录
            ftpClient.changeWorkingDirectory(targetPath);

            // 上传文件
            boolean uploaded = ftpClient.storeFile(fileName, inputStream);
            if (uploaded) {
                log.info("File uploaded successfully.");
                return url+targetPath+"/"+fileName;
            } else {
                log.error("Failed to upload file.");
            }
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("Failed",e);
            }
            if (ftpClient.isConnected()) {
                ftpClient.logout();
                ftpClient.disconnect();
            }
        }
        return null;
    }

}
