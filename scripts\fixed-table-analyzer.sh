#!/bin/bash

# =============================================
# 修复版表分析器
# 避免while循环中的变量作用域问题
# =============================================

echo "========================================"
echo "修复版表分析器"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p fixed-analysis

echo "[INFO] 分析数据库: $DB_NAME"

# 获取前5个表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null | head -5 > ./temp_tables_fixed.txt

TOTAL_TABLES=$(wc -l < ./temp_tables_fixed.txt)
echo "[INFO] 处理 $TOTAL_TABLES 个表"

# 生成表结构详情
echo "[INFO] 生成表结构详情..."

cat > fixed-analysis/table-structure-fixed.md << EOF
# 数据库表结构详情

## 📊 数据库: $DB_NAME

**生成时间**: $(date)
**处理表数**: $TOTAL_TABLES

EOF

# 使用for循环而不是while循环，避免管道问题
# 清理表名中的回车符
sed -i 's/\r$//' ./temp_tables_fixed.txt 2>/dev/null || sed -i '' 's/\r$//' ./temp_tables_fixed.txt 2>/dev/null || true

table_count=0
for table_name in $(cat ./temp_tables_fixed.txt); do
    # 清理表名中可能的回车符和空白字符
    table_name=$(echo "$table_name" | tr -d '\r\n' | xargs)
    table_count=$((table_count + 1))
    echo "处理表 $table_count: $table_name"
    
    echo "### 表: $table_name" >> fixed-analysis/table-structure-fixed.md
    echo "" >> fixed-analysis/table-structure-fixed.md
    
    # 获取表注释 - 直接写入临时文件避免变量问题
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
        --batch --skip-column-names 2>/dev/null > ./temp_comment_${table_name}.txt
    
    if [ -s "./temp_comment_${table_name}.txt" ]; then
        TABLE_COMMENT=$(cat ./temp_comment_${table_name}.txt)
        echo "  表注释: $TABLE_COMMENT"
        echo "**表说明**: $TABLE_COMMENT" >> fixed-analysis/table-structure-fixed.md
    else
        echo "  表注释: 无法获取"
        echo "**表说明**: 无法获取" >> fixed-analysis/table-structure-fixed.md
    fi
    echo "" >> fixed-analysis/table-structure-fixed.md
    
    # 生成字段信息表格
    echo "| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |" >> fixed-analysis/table-structure-fixed.md
    echo "|--------|------|----------|-----|--------|------|" >> fixed-analysis/table-structure-fixed.md
    
    # 获取字段详情
    temp_file="./temp_columns_fixed_${table_name}.txt"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, IFNULL(COLUMN_KEY,''), IFNULL(COLUMN_DEFAULT,''), IFNULL(COLUMN_COMMENT,'') FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' ORDER BY ORDINAL_POSITION;" \
        --batch --skip-column-names 2>/dev/null > "$temp_file"
    
    echo "  临时文件大小: $(wc -c < "$temp_file") 字节"
    echo "  临时文件行数: $(wc -l < "$temp_file") 行"
    
    if [ -s "$temp_file" ]; then
        field_count=0
        # 使用简化的解析方法
        while read -r line; do
            [ -z "$line" ] && continue
            
            field_count=$((field_count + 1))
            
            # 分割字段（使用tab分隔）
            col_name=$(echo "$line" | cut -f1)
            col_type=$(echo "$line" | cut -f2)
            is_null=$(echo "$line" | cut -f3)
            col_key=$(echo "$line" | cut -f4)
            col_default=$(echo "$line" | cut -f5)
            col_comment=$(echo "$line" | cut -f6)
            
            # 处理空值
            [ -z "$col_key" ] && col_key=""
            [ -z "$col_default" ] && col_default=""
            [ -z "$col_comment" ] && col_comment=""
            
            # 处理特殊字符
            col_default=$(echo "$col_default" | sed 's/|/\\|/g' | sed 's/NULL//')
            col_comment=$(echo "$col_comment" | sed 's/|/\\|/g' | sed 's/NULL//')
            
            # 格式化键类型
            case "$col_key" in
                "PRI") col_key="🔑 PK" ;;
                "UNI") col_key="🔒 UK" ;;
                "MUL") col_key="📇 FK" ;;
                *) col_key="" ;;
            esac
            
            # 格式化是否为空
            case "$is_null" in
                "NO") is_null="❌" ;;
                "YES") is_null="✅" ;;
            esac
            
            echo "| $col_name | $col_type | $is_null | $col_key | $col_default | $col_comment |" >> fixed-analysis/table-structure-fixed.md
        done < "$temp_file"
        echo "  处理了 $field_count 个字段"
    else
        echo "  ❌ 无法获取字段信息"
        echo "| - | 无法获取字段信息 | - | - | - | 请检查数据库权限 |" >> fixed-analysis/table-structure-fixed.md
    fi
    
    rm -f "$temp_file" "./temp_comment_${table_name}.txt"
    
    echo "" >> fixed-analysis/table-structure-fixed.md
    echo "---" >> fixed-analysis/table-structure-fixed.md
    echo "" >> fixed-analysis/table-structure-fixed.md
done

echo "[SUCCESS] 表结构详情已生成: fixed-analysis/table-structure-fixed.md"

# 生成数据字典
echo "[INFO] 生成数据字典..."

cat > fixed-analysis/data-dictionary-fixed.md << EOF
# 数据字典

## 📊 数据库: $DB_NAME

**生成时间**: $(date)

## 📋 表概览

| 序号 | 表名 | 注释 | 字段数 | 记录数 |
|------|------|------|--------|--------|
EOF

# 生成表概览
table_num=1
for table_name in $(cat ./temp_tables_fixed.txt); do
    # 清理表名中可能的回车符和空白字符
    table_name=$(echo "$table_name" | tr -d '\r\n' | xargs)
    echo "处理表概览 $table_num: $table_name"
    
    # 获取表信息
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
        --batch --skip-column-names 2>/dev/null > ./temp_comment_${table_name}.txt
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
        --batch --skip-column-names 2>/dev/null > ./temp_count_${table_name}.txt
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT IFNULL(TABLE_ROWS, 0) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
        --batch --skip-column-names 2>/dev/null > ./temp_rows_${table_name}.txt
    
    # 读取结果
    TABLE_COMMENT=$(cat ./temp_comment_${table_name}.txt 2>/dev/null || echo "无注释")
    COLUMN_COUNT=$(cat ./temp_count_${table_name}.txt 2>/dev/null || echo "0")
    ROW_COUNT=$(cat ./temp_rows_${table_name}.txt 2>/dev/null || echo "0")
    
    echo "| $table_num | $table_name | $TABLE_COMMENT | $COLUMN_COUNT | $ROW_COUNT |" >> fixed-analysis/data-dictionary-fixed.md
    
    rm -f ./temp_comment_${table_name}.txt ./temp_count_${table_name}.txt ./temp_rows_${table_name}.txt
    table_num=$((table_num + 1))
done

echo "[SUCCESS] 数据字典已生成: fixed-analysis/data-dictionary-fixed.md"

# 清理临时文件
rm -f ./temp_tables_fixed.txt ./temp_*.txt

echo
echo "========================================"
echo "修复版分析完成！"
echo "========================================"
echo "📊 表结构详情: fixed-analysis/table-structure-fixed.md"
echo "📖 数据字典: fixed-analysis/data-dictionary-fixed.md"
echo "📊 处理表数: $TOTAL_TABLES"
echo "========================================"
