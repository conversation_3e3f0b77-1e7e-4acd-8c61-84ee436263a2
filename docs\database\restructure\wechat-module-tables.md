# 微信模块表详细说明

## 📋 概览

微信模块是足球彩票系统的重要组成部分，包含微信公众号、小程序、企业微信等功能。本文档详细说明了所有微信相关的数据库表。

## 🏗️ 表分类

### 1. 核心微信表（包含在应用功能表中）

#### 1.1 微信账号管理
- **mp_account** - 微信公众号账号配置
  - 存储公众号基本信息：appid、secret、token等
  - 支持多租户
  - 包含公众号类型、状态等配置

#### 1.2 微信用户管理
- **mp_user** - 微信用户信息
  - 存储关注用户的基本信息
  - 包含openid、昵称、头像、地理位置等
  - 支持用户标签和备注
  - 记录关注/取消关注时间

#### 1.3 微信消息管理
- **mp_message** - 微信消息记录
  - 存储收发的所有消息
  - 支持文本、图片、语音、视频等消息类型
  - 记录消息来源和发送时间

#### 1.4 微信菜单管理
- **mp_menu** - 微信自定义菜单
  - 支持多级菜单结构
  - 支持多种菜单类型：点击、跳转、小程序等
  - 包含回复消息配置

#### 1.5 微信模板配置
- **mp_template_config** - 微信模板消息配置 ⭐
  - 存储模板消息的配置信息
  - 支持不同业务类型的模板
  - 包含模板字段映射配置

#### 1.6 微信标签管理
- **mp_tag** - 微信用户标签
  - 用户分组和标签管理
  - 记录标签下的用户数量

### 2. 扩展微信表（微信扩展表文件）

#### 2.1 小程序相关
- **mp_mini_user** - 微信小程序用户
  - 存储小程序用户信息
  - 支持与公众号用户关联
  - 包含unionid统一标识

#### 2.2 素材管理
- **mp_material** - 微信素材库
  - 存储图片、语音、视频等素材
  - 支持临时和永久素材
  - 记录素材的微信media_id

#### 2.3 自动回复
- **mp_auto_reply** - 微信自动回复
  - 支持关键词自动回复
  - 支持关注/取消关注自动回复
  - 支持多种回复消息类型

#### 2.4 日志记录
- **mp_click_logs** - 微信点击日志
  - 记录菜单点击、二维码扫描等事件
  - 用于数据分析和用户行为追踪

- **mp_other_even_logs** - 微信其他事件日志
  - 记录各种微信事件
  - 包含事件类型和详细数据

- **mp_pay_config_log** - 微信支付配置日志
  - 记录支付相关配置的变更
  - 用于审计和问题排查

### 3. 企业微信表

#### 3.1 企业微信设置
- **wx_work_setting** - 企业微信配置
  - 存储企业微信号的基本信息
  - 包含二维码、获客链接等
  - 支持启用/停用状态

#### 3.2 外部联系人
- **wx_external_contact** - 企业微信外部联系人
  - 存储客户联系人信息
  - 支持unionid关联
  - 用于客户关系管理

#### 3.3 联系方式配置
- **wx_external_contact_way_config** - 企业微信联系方式配置
  - 配置客服联系二维码
  - 关联作者和企微号
  - 用于客户获取和转化

## 🎯 前端使用情况

### 后台管理前端使用的微信表

#### 🔴 核心表（不能删除）
- `mp_account` - 微信账号配置管理
- `mp_template_config` - 模板消息配置

#### 🟡 重要表（谨慎删除）
- `mp_user` - 微信用户管理和查询
- `mp_menu` - 自定义菜单配置
- `mp_material` - 素材库管理
- `mp_auto_reply` - 自动回复配置

#### 🟢 一般表（可考虑删除）
- `mp_message` - 消息记录查询
- `mp_tag` - 用户标签管理
- `mp_mini_user` - 小程序用户查询
- `mp_click_logs` - 点击数据分析
- `mp_other_even_logs` - 事件日志查询
- `mp_pay_config_log` - 配置变更日志

#### ⚪ 企业微信表（根据业务需要）
- `wx_work_setting` - 企业微信配置
- `wx_external_contact` - 客户管理
- `wx_external_contact_way_config` - 获客配置

## 📊 表关系图

```
mp_account (微信账号)
    ├── mp_user (微信用户) [1:N]
    ├── mp_message (微信消息) [1:N]
    ├── mp_menu (微信菜单) [1:N]
    ├── mp_tag (微信标签) [1:N]
    ├── mp_material (微信素材) [1:N]
    ├── mp_auto_reply (自动回复) [1:N]
    ├── mp_click_logs (点击日志) [1:N]
    └── mp_other_even_logs (事件日志) [1:N]

mp_template_config (模板配置)
    └── 通过appid关联mp_account

wx_work_setting (企业微信)
    ├── wx_external_contact (外部联系人) [1:N]
    └── wx_external_contact_way_config (联系方式) [1:N]

member_user (会员用户)
    ├── mp_user (通过correlation_id关联)
    └── wx_external_contact (通过union_id关联)
```

## 🔧 重构建议

### 保留的表
**核心业务表**：
- `mp_account` - 微信账号配置
- `mp_user` - 微信用户信息
- `mp_template_config` - 模板配置（您提到的表）
- `mp_menu` - 自定义菜单

**重要功能表**：
- `mp_message` - 消息记录
- `mp_material` - 素材管理
- `mp_auto_reply` - 自动回复

### 可选的表
**根据业务需要保留**：
- `mp_mini_user` - 如果有小程序业务
- `wx_work_setting` - 如果使用企业微信
- `wx_external_contact` - 如果需要客户管理

**可考虑删除的表**：
- `mp_click_logs` - 如果不需要详细的点击分析
- `mp_other_even_logs` - 如果不需要事件日志
- `mp_pay_config_log` - 如果不需要配置审计

## ⚠️ 注意事项

1. **mp_template_config表很重要**：这个表存储了模板消息的配置，是微信消息推送的核心配置，不能删除。

2. **多租户支持**：所有微信表都支持多租户，通过tenant_id字段隔离数据。

3. **关联关系**：微信用户可以通过correlation_id与会员用户关联，实现统一用户体系。

4. **企业微信独立性**：企业微信相关表相对独立，如果不使用企业微信功能可以考虑不创建。

5. **日志表的价值**：虽然日志表不是核心业务，但对于数据分析和问题排查很有价值。

## 📈 使用建议

1. **最小化部署**：只保留核心的6个表（account、user、message、menu、template_config、tag）
2. **标准部署**：增加素材和自动回复功能（+material、auto_reply）
3. **完整部署**：包含所有微信功能表
4. **企业版部署**：增加企业微信相关表

根据您的业务需求选择合适的部署方案。`mp_template_config`表作为模板消息配置的核心表，建议在所有方案中都保留。
