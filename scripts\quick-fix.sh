#!/bin/bash

# =============================================
# 足球彩票系统多租户改造 - 快速修复脚本
# 用途：修复部分执行失败的改造，支持断点续传
# =============================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 读取数据库配置
read_config() {
    if [ -f "config/database.conf" ]; then
        source config/database.conf
    else
        DB_HOST=${DB_HOST:-localhost}
        DB_PORT=${DB_PORT:-3306}
        DB_NAME=${DB_NAME:-mir}
        DB_USER=${DB_USER:-root}
        
        read -s -p "请输入数据库密码: " DB_PASSWORD
        echo
    fi
    
    # 验证连接
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
        log_error "数据库连接失败"
        exit 1
    fi
    
    log_success "数据库连接成功"
}

# 检查当前状态
check_status() {
    log_info "检查当前改造状态..."

    # 使用简单的检查脚本
    log_info "执行状态检查..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < sql/tenant-migration/00-simple-check.sql 2>/dev/null || {
        log_warning "详细检查失败，使用基础检查方式"
    }

    # 检查tenant_id字段
    TENANT_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'tenant_id';" \
        --batch --skip-column-names 2>/dev/null || echo "0")

    log_info "已添加tenant_id字段的表数量: $TENANT_TABLES"

    # 检查具体哪些表已添加
    echo "已添加tenant_id字段的表："
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'tenant_id' ORDER BY TABLE_NAME;" \
        --batch --skip-column-names 2>/dev/null | while read table; do
        echo "  ✅ $table"
    done
    
    # 检查需要添加但还没添加的表
    echo
    echo "需要检查的表："
    
    REQUIRED_TABLES=(
        "member_user"
        "member_level"
        "pay_app"
        "pay_order"
        "pay_wallet"
        "author_article"
    )
    
    MISSING_TABLES=()
    
    for table in "${REQUIRED_TABLES[@]}"; do
        # 检查表是否存在
        TABLE_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names)
        
        if [ "$TABLE_EXISTS" -eq "1" ]; then
            # 检查是否有tenant_id字段
            TENANT_FIELD_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table' AND COLUMN_NAME = 'tenant_id';" \
                --batch --skip-column-names)
            
            if [ "$TENANT_FIELD_EXISTS" -eq "1" ]; then
                echo "  ✅ $table (已添加tenant_id字段)"
            else
                echo "  ❌ $table (缺少tenant_id字段)"
                MISSING_TABLES+=("$table")
            fi
        else
            echo "  ⚠️  $table (表不存在)"
        fi
    done
    
    echo
    if [ ${#MISSING_TABLES[@]} -eq 0 ]; then
        log_success "所有必要的表都已添加tenant_id字段"
        return 0
    else
        log_warning "还有 ${#MISSING_TABLES[@]} 个表需要添加tenant_id字段"
        return 1
    fi
}

# 修复缺失的字段
fix_missing_fields() {
    log_info "修复缺失的tenant_id字段..."
    
    # 创建临时SQL文件
    TEMP_SQL="/tmp/fix_tenant_fields_$(date +%s).sql"
    
    cat > "$TEMP_SQL" << 'EOF'
-- 临时修复脚本
SET SQL_SAFE_UPDATES = 0;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建安全添加字段的存储过程
DELIMITER $$
DROP PROCEDURE IF EXISTS SafeAddTenantId$$
CREATE PROCEDURE SafeAddTenantId(IN table_name VARCHAR(64))
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    DECLARE table_exists INT DEFAULT 0;
    
    -- 检查表是否存在
    SELECT COUNT(*) INTO table_exists
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = table_name;
    
    IF table_exists > 0 THEN
        -- 检查字段是否已存在
        SELECT COUNT(*) INTO column_exists
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = table_name
          AND COLUMN_NAME = 'tenant_id';
        
        -- 如果字段不存在，则添加
        IF column_exists = 0 THEN
            SET @sql = CONCAT('ALTER TABLE ', table_name, 
                             ' ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT ''租户编号'' AFTER id');
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            SELECT CONCAT('✅ 表 ', table_name, ' 已添加 tenant_id 字段') AS result;
        ELSE
            SELECT CONCAT('⚠️  表 ', table_name, ' 的 tenant_id 字段已存在') AS result;
        END IF;
    ELSE
        SELECT CONCAT('❌ 表 ', table_name, ' 不存在') AS result;
    END IF;
END$$
DELIMITER ;

-- 修复必要的表
CALL SafeAddTenantId('member_user');
CALL SafeAddTenantId('member_level');
CALL SafeAddTenantId('member_level_record');
CALL SafeAddTenantId('member_point_record');
CALL SafeAddTenantId('member_sign_in_record');

CALL SafeAddTenantId('pay_app');
CALL SafeAddTenantId('pay_order');
CALL SafeAddTenantId('pay_refund');
CALL SafeAddTenantId('pay_wallet');
CALL SafeAddTenantId('pay_wallet_transaction');

CALL SafeAddTenantId('article');
CALL SafeAddTenantId('author_article_append');
CALL SafeAddTenantId('match_team');

CALL SafeAddTenantId('infra_file');
CALL SafeAddTenantId('infra_file_config');

-- 清理
DROP PROCEDURE SafeAddTenantId;

SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

SELECT '修复完成' AS message;
EOF

    # 执行修复脚本
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$TEMP_SQL"
    
    if [ $? -eq 0 ]; then
        log_success "字段修复完成"
        rm -f "$TEMP_SQL"
    else
        log_error "字段修复失败，临时脚本保存在: $TEMP_SQL"
        exit 1
    fi
}

# 创建基础索引
create_basic_indexes() {
    log_info "创建基础索引..."
    
    TEMP_SQL="/tmp/create_basic_indexes_$(date +%s).sql"
    
    cat > "$TEMP_SQL" << 'EOF'
-- 创建基础索引
SET SQL_SAFE_UPDATES = 0;

-- 创建安全创建索引的存储过程
DELIMITER $$
DROP PROCEDURE IF EXISTS SafeCreateIndex$$
CREATE PROCEDURE SafeCreateIndex(
    IN index_name VARCHAR(64),
    IN table_name VARCHAR(64),
    IN columns TEXT
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;
    DECLARE table_exists INT DEFAULT 0;
    
    -- 检查表是否存在
    SELECT COUNT(*) INTO table_exists
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = table_name;
    
    IF table_exists > 0 THEN
        -- 检查索引是否已存在
        SELECT COUNT(*) INTO index_exists
        FROM INFORMATION_SCHEMA.STATISTICS 
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = table_name
          AND INDEX_NAME = index_name;
        
        -- 如果索引不存在，则创建
        IF index_exists = 0 THEN
            SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, '(', columns, ')');
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            SELECT CONCAT('✅ 索引 ', index_name, ' 已创建') AS result;
        ELSE
            SELECT CONCAT('⚠️  索引 ', index_name, ' 已存在') AS result;
        END IF;
    ELSE
        SELECT CONCAT('❌ 表 ', table_name, ' 不存在，跳过索引创建') AS result;
    END IF;
END$$
DELIMITER ;

-- 创建基础索引
CALL SafeCreateIndex('idx_member_user_tenant_id', 'member_user', 'tenant_id');
CALL SafeCreateIndex('idx_member_level_tenant_id', 'member_level', 'tenant_id');
CALL SafeCreateIndex('idx_pay_app_tenant_id', 'pay_app', 'tenant_id');
CALL SafeCreateIndex('idx_pay_order_tenant_id', 'pay_order', 'tenant_id');
CALL SafeCreateIndex('idx_pay_wallet_tenant_id', 'pay_wallet', 'tenant_id');
CALL SafeCreateIndex('idx_article_tenant_id', 'article', 'tenant_id');

-- 清理
DROP PROCEDURE SafeCreateIndex;

SELECT '索引创建完成' AS message;
EOF

    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$TEMP_SQL"
    
    if [ $? -eq 0 ]; then
        log_success "基础索引创建完成"
        rm -f "$TEMP_SQL"
    else
        log_error "索引创建失败，临时脚本保存在: $TEMP_SQL"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "足球彩票系统多租户改造 - 快速修复工具"
    echo "========================================"
    
    # 检查是否在正确的目录
    if [ ! -f "pom.xml" ]; then
        log_error "请在项目根目录执行此脚本"
        exit 1
    fi
    
    # 创建必要目录
    mkdir -p logs
    
    # 读取配置
    read_config
    
    # 检查状态
    if check_status; then
        echo
        log_info "所有必要的字段都已添加，是否要创建索引？"
        read -p "创建基础索引？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            create_basic_indexes
        fi
    else
        echo
        log_warning "发现缺失的tenant_id字段"
        read -p "是否修复缺失的字段？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            fix_missing_fields
            
            echo
            log_info "字段修复完成，是否创建基础索引？"
            read -p "创建基础索引？(y/N): " -n 1 -r
            echo
            
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                create_basic_indexes
            fi
        fi
    fi
    
    echo
    echo "========================================"
    log_success "快速修复完成！"
    echo "========================================"
    echo
    log_info "下一步："
    echo "1. 如果还没有执行数据迁移，请运行: mysql < sql/tenant-migration/03-migrate-data.sql"
    echo "2. 修改应用配置启用多租户: mir.tenant.enable=true"
    echo "3. 重启应用服务"
}

main "$@"
