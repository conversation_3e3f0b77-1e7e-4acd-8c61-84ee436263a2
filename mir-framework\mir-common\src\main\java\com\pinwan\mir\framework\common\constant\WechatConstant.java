package com.pinwan.mir.framework.common.constant;

/**
 * 微信相关常量类
 */
public class WechatConstant {
    /**
     * 系统公众号ID
     */
    public static final Long SYS_ACCOUNT_ID = 1L;


    /** 二维码场景描述 - 登录 */
    public static final String SCENE_DESC_MEMBER_LOGIN = "MemberLogin";

    /** 二维码场景描述 - 作者文章详情 */
    public static final String SCENE_DESC_AUTHOR_ARTICLE = "AuthorArticle";

    /** 二维码场景描述 -- 关注推送公众号 */
    public static final String SCENE_DESC_PUSH_ACCOUNT = "PUSH";


    /** 微信API - 发送模板消息 */
    public static final String SEND_TEMPLATE_MSG_URL = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s";

    public static final String [] IGNORE_MSG_EVENTS = {"TEMPLATESENDJOBFINISH", "SCAN"};


}
