#!/bin/bash

# =============================================
# 简单ER图生成脚本
# 避免复杂SQL，分步骤获取数据库信息
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================"
echo "简单ER图生成工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    log_error "数据库连接失败"
    exit 1
fi
log_success "数据库连接成功"

# 创建输出目录
mkdir -p docs/database

log_info "获取数据库表列表..."

# 获取所有表名
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/tables.txt

if [ $? -ne 0 ]; then
    log_error "获取表列表失败"
    exit 1
fi

log_info "获取表结构信息..."

# 为每个表获取结构信息
> /tmp/table_structures.txt
while read table_name; do
    if [ -n "$table_name" ]; then
        log_info "处理表: $table_name"
        
        # 获取表注释
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        # 获取字段信息
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "DESCRIBE $table_name;" --batch --skip-column-names 2>/dev/null | while read field type null key default extra; do
            echo "TABLE:$table_name|$TABLE_COMMENT|$field|$type|$key" >> /tmp/table_structures.txt
        done
    fi
done < /tmp/tables.txt

log_info "生成简化版ER图..."

# 生成简化版ER图（只包含主要业务表）
cat > docs/database/er-diagram-simple.mmd << 'EOF'
erDiagram
    %% 足球彩票系统核心业务表ER图
    %% 基于实际数据库结构生成
    
EOF

# 定义要包含的主要表
MAIN_TABLES=("member_user" "member_level" "pay_order" "pay_wallet" "pay_app" "author_article" "system_tenant" "system_users")

# 为主要表生成ER图定义
for table in "${MAIN_TABLES[@]}"; do
    # 检查表是否存在
    if grep -q "^TABLE:$table|" /tmp/table_structures.txt; then
        log_info "添加表到ER图: $table"
        
        # 获取表注释
        table_comment=$(grep "^TABLE:$table|" /tmp/table_structures.txt | head -1 | cut -d'|' -f2)
        
        echo "    $table {" >> docs/database/er-diagram-simple.mmd
        
        # 添加主要字段（限制数量）
        grep "^TABLE:$table|" /tmp/table_structures.txt | head -8 | while IFS='|' read prefix table_name comment field_name field_type key_type; do
            # 确定键类型
            key_indicator=""
            case "$key_type" in
                "PRI") key_indicator=" PK" ;;
                "UNI") key_indicator=" UK" ;;
                "MUL") key_indicator=" FK" ;;
            esac
            
            # 简化数据类型显示
            simple_type=$(echo "$field_type" | sed 's/([^)]*)//g' | tr '[:upper:]' '[:lower:]')
            
            echo "        $simple_type $field_name$key_indicator" >> docs/database/er-diagram-simple.mmd
        done
        
        echo "    }" >> docs/database/er-diagram-simple.mmd
        echo "" >> docs/database/er-diagram-simple.mmd
    fi
done

# 添加主要关系
cat >> docs/database/er-diagram-simple.mmd << 'EOF'
    %% 主要表关系
    system_tenant ||--o{ member_user : "tenant_id"
    system_tenant ||--o{ pay_order : "tenant_id"
    system_tenant ||--o{ pay_wallet : "tenant_id"
    system_tenant ||--o{ author_article : "tenant_id"
    system_tenant ||--o{ pay_app : "tenant_id"
    system_tenant ||--o{ system_users : "tenant_id"
    
    member_user ||--|| pay_wallet : "user_id"
    member_user ||--o{ pay_order : "user_id"
    member_user ||--o{ author_article : "author_id"
    
    pay_app ||--o{ pay_order : "app_id"
EOF

log_success "简化版ER图已生成: docs/database/er-diagram-simple.mmd"

# 生成表统计信息
log_info "生成表统计信息..."

cat > docs/database/table-summary.txt << 'EOF'
=== 数据库表统计摘要 ===

EOF

# 统计各模块表数量
echo "模块表数量统计:" >> docs/database/table-summary.txt
echo "会员模块: $(grep -c '^member_' /tmp/tables.txt) 个表" >> docs/database/table-summary.txt
echo "支付模块: $(grep -c '^pay_' /tmp/tables.txt) 个表" >> docs/database/table-summary.txt
echo "系统模块: $(grep -c '^system_' /tmp/tables.txt) 个表" >> docs/database/table-summary.txt
echo "基础设施: $(grep -c '^infra_' /tmp/tables.txt) 个表" >> docs/database/table-summary.txt
echo "微信模块: $(grep -c '^mp_' /tmp/tables.txt) 个表" >> docs/database/table-summary.txt
echo "业务模块: $(grep -c '^author_\|^match_\|^banner\|^gold_' /tmp/tables.txt) 个表" >> docs/database/table-summary.txt
echo "" >> docs/database/table-summary.txt

# 列出所有表
echo "所有表列表:" >> docs/database/table-summary.txt
cat /tmp/tables.txt | sort >> docs/database/table-summary.txt

log_success "表统计信息已生成: docs/database/table-summary.txt"

# 检查多租户字段覆盖情况
log_info "检查多租户字段覆盖情况..."

echo "" >> docs/database/table-summary.txt
echo "=== 多租户字段检查 ===" >> docs/database/table-summary.txt

# 检查哪些表有tenant_id字段
> /tmp/tenant_tables.txt
while read table_name; do
    if [ -n "$table_name" ]; then
        HAS_TENANT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SHOW COLUMNS FROM $table_name LIKE 'tenant_id';" --batch --skip-column-names 2>/dev/null | wc -l)
        
        if [ "$HAS_TENANT" -gt 0 ]; then
            echo "$table_name" >> /tmp/tenant_tables.txt
        fi
    fi
done < /tmp/tables.txt

TOTAL_TABLES=$(wc -l < /tmp/tables.txt)
TENANT_TABLES=$(wc -l < /tmp/tenant_tables.txt)

echo "总表数: $TOTAL_TABLES" >> docs/database/table-summary.txt
echo "已添加tenant_id字段的表数: $TENANT_TABLES" >> docs/database/table-summary.txt
echo "" >> docs/database/table-summary.txt
echo "已添加tenant_id字段的表:" >> docs/database/table-summary.txt
cat /tmp/tenant_tables.txt | sort >> docs/database/table-summary.txt

# 清理临时文件
rm -f /tmp/tables.txt /tmp/table_structures.txt /tmp/tenant_tables.txt

echo
echo "========================================"
echo "ER图生成完成！"
echo "========================================"
echo "🎯 docs/database/er-diagram-simple.mmd - 简化版ER图"
echo "📊 docs/database/table-summary.txt - 表统计摘要"
echo "========================================"
echo
echo "查看ER图的方法："
echo "1. 访问 https://mermaid.live/ 粘贴内容"
echo "2. 在VSCode中安装Mermaid Preview插件"
echo "3. 使用支持Mermaid的Markdown编辑器"

log_success "简单ER图生成完成！"
