#!/bin/bash

# =============================================
# 足球彩票系统SaaS多租户改造回滚脚本
# 作者：足球彩票团队
# 版本：1.0.0
# 说明：回滚多租户改造，恢复到改造前状态
# =============================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 读取配置
read_config() {
    log_info "读取数据库配置..."
    
    # 从配置文件读取数据库连接信息
    if [ -f "config/database.conf" ]; then
        source config/database.conf
    else
        # 默认配置
        DB_HOST=${DB_HOST:-localhost}
        DB_PORT=${DB_PORT:-3306}
        DB_NAME=${DB_NAME:-mir}
        DB_USER=${DB_USER:-root}
        
        # 提示输入密码
        read -s -p "请输入数据库密码: " DB_PASSWORD
        echo
    fi
    
    # 验证数据库连接
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
        log_error "数据库连接失败，请检查配置"
        exit 1
    fi
    
    log_success "数据库连接验证成功"
}

# 选择备份文件
select_backup() {
    log_info "选择要恢复的备份文件..."
    
    # 查找备份文件
    BACKUP_DIRS=($(find backup -name "backup_info.txt" -type f | xargs dirname | sort -r))
    
    if [ ${#BACKUP_DIRS[@]} -eq 0 ]; then
        log_error "未找到备份文件"
        exit 1
    fi
    
    echo "可用的备份文件："
    for i in "${!BACKUP_DIRS[@]}"; do
        BACKUP_TIME=$(basename "${BACKUP_DIRS[$i]}")
        BACKUP_FILE=$(cat "${BACKUP_DIRS[$i]}/backup_info.txt")
        BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
        echo "$((i+1)). $BACKUP_TIME (大小: $BACKUP_SIZE)"
    done
    
    echo
    read -p "请选择要恢复的备份 (1-${#BACKUP_DIRS[@]}): " BACKUP_CHOICE
    
    if [[ ! "$BACKUP_CHOICE" =~ ^[0-9]+$ ]] || [ "$BACKUP_CHOICE" -lt 1 ] || [ "$BACKUP_CHOICE" -gt ${#BACKUP_DIRS[@]} ]; then
        log_error "无效的选择"
        exit 1
    fi
    
    SELECTED_BACKUP_DIR="${BACKUP_DIRS[$((BACKUP_CHOICE-1))]}"
    SELECTED_BACKUP_FILE=$(cat "$SELECTED_BACKUP_DIR/backup_info.txt")
    
    if [ ! -f "$SELECTED_BACKUP_FILE" ]; then
        log_error "备份文件不存在: $SELECTED_BACKUP_FILE"
        exit 1
    fi
    
    log_success "选择的备份文件: $SELECTED_BACKUP_FILE"
}

# 创建当前状态备份
create_current_backup() {
    log_info "创建当前状态备份..."
    
    CURRENT_BACKUP_DIR="backup/rollback_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$CURRENT_BACKUP_DIR"
    
    CURRENT_BACKUP_FILE="$CURRENT_BACKUP_DIR/database_backup.sql"
    
    mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        --single-transaction --routines --triggers "$DB_NAME" > "$CURRENT_BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "当前状态备份完成: $CURRENT_BACKUP_FILE"
        echo "$CURRENT_BACKUP_FILE" > "$CURRENT_BACKUP_DIR/backup_info.txt"
    else
        log_error "当前状态备份失败"
        exit 1
    fi
}

# 恢复数据库
restore_database() {
    log_info "恢复数据库..."
    
    log_warning "即将删除当前数据库并恢复到备份状态"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消操作"
        exit 0
    fi
    
    # 删除当前数据库
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "DROP DATABASE IF EXISTS $DB_NAME;"
    
    # 重新创建数据库
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    # 恢复备份
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$SELECTED_BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "数据库恢复完成"
    else
        log_error "数据库恢复失败"
        exit 1
    fi
}

# 回滚代码修改
rollback_code() {
    log_info "回滚代码修改..."
    
    # 检查Git状态
    if [ -d ".git" ]; then
        log_info "检测到Git仓库，尝试回滚代码修改..."
        
        # 显示修改的文件
        echo "已修改的文件："
        git status --porcelain
        
        echo
        read -p "是否回滚所有代码修改？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 回滚所有修改
            git checkout -- .
            log_success "代码修改已回滚"
        else
            log_info "跳过代码回滚"
        fi
    else
        log_warning "未检测到Git仓库，需要手动回滚代码修改"
        echo
        echo "需要手动回滚的修改："
        echo "1. 将实体类的继承关系从 TenantBaseDO 改回 BaseDO"
        echo "2. 将多租户配置 mir.tenant.enable 设置为 false"
        echo "3. 恢复相关的import语句"
        echo
        read -p "按回车键继续..." -r
    fi
}

# 验证回滚结果
verify_rollback() {
    log_info "验证回滚结果..."
    
    # 检查多租户相关表是否存在tenant_id字段
    TABLES_TO_CHECK=(
        "member_user"
        "member_level"
        "pay_app"
        "pay_order"
        "pay_wallet"
        "article"
    )
    
    for table in "${TABLES_TO_CHECK[@]}"; do
        RESULT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SHOW COLUMNS FROM $table LIKE 'tenant_id';" --batch --skip-column-names 2>/dev/null)
        
        if [ -z "$RESULT" ]; then
            log_success "表 $table 的 tenant_id 字段已移除"
        else
            log_warning "表 $table 仍然存在 tenant_id 字段"
        fi
    done
    
    # 检查租户表
    TENANT_TABLE_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SHOW TABLES LIKE 'system_tenant';" --batch --skip-column-names 2>/dev/null)
    
    if [ -n "$TENANT_TABLE_EXISTS" ]; then
        log_info "system_tenant 表仍然存在（这是正常的）"
    fi
    
    log_success "回滚结果验证完成"
}

# 生成回滚报告
generate_rollback_report() {
    log_info "生成回滚报告..."
    
    REPORT_FILE="reports/saas_rollback_report_$(date +%Y%m%d_%H%M%S).md"
    mkdir -p "reports"
    
    cat > "$REPORT_FILE" << EOF
# SaaS多租户改造回滚报告

## 回滚时间
- 开始时间: $START_TIME
- 结束时间: $(date '+%Y-%m-%d %H:%M:%S')

## 回滚内容

### 1. 数据库回滚
- ✅ 恢复数据库备份
- ✅ 移除多租户相关字段

### 2. 代码回滚
- ✅ 恢复实体类继承关系
- ✅ 恢复多租户配置

### 3. 验证结果
EOF

    # 添加表检查结果
    echo "#### 数据库表检查" >> "$REPORT_FILE"
    for table in "${TABLES_TO_CHECK[@]}"; do
        RESULT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SHOW COLUMNS FROM $table LIKE 'tenant_id';" --batch --skip-column-names 2>/dev/null)
        
        if [ -z "$RESULT" ]; then
            echo "- ✅ $table: tenant_id字段已移除" >> "$REPORT_FILE"
        else
            echo "- ❌ $table: tenant_id字段仍然存在" >> "$REPORT_FILE"
        fi
    done
    
    cat >> "$REPORT_FILE" << EOF

## 备份信息
- 原始备份文件: $SELECTED_BACKUP_FILE
- 回滚前备份: $CURRENT_BACKUP_FILE

## 注意事项
- 系统已回滚到多租户改造前的状态
- 如需重新进行多租户改造，请重新执行改造脚本
- 建议保留所有备份文件
EOF

    log_success "回滚报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "========================================"
    echo "足球彩票系统SaaS多租户改造回滚脚本"
    echo "开始时间: $START_TIME"
    echo "========================================"
    
    # 检查是否在正确的目录
    if [ ! -f "pom.xml" ]; then
        log_error "请在项目根目录执行此脚本"
        exit 1
    fi
    
    # 创建必要的目录
    mkdir -p reports logs
    
    # 执行回滚步骤
    read_config
    select_backup
    
    # 最终确认
    echo
    log_warning "即将开始回滚SaaS多租户改造"
    log_warning "这将删除当前数据库并恢复到选择的备份状态"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消操作"
        exit 0
    fi
    
    # 执行回滚
    create_current_backup
    restore_database
    rollback_code
    verify_rollback
    generate_rollback_report
    
    echo
    echo "========================================"
    log_success "SaaS多租户改造回滚完成！"
    echo "========================================"
    echo
    log_info "下一步操作："
    echo "1. 重启应用服务"
    echo "2. 验证系统功能正常"
    echo "3. 查看回滚报告: reports/saas_rollback_report_*.md"
    echo
}

# 执行主函数
main "$@"
