# Football-Lottery-Frontend 使用表记录

## 📋 项目信息

- **项目名称**: football-lottery-frontend
- **项目类型**: 后台管理前端
- **技术栈**: Vue3 + Element Plus
- **对应后端**: football-lottery (mir项目)

## 🎯 前端功能模块与数据库表对应关系

### 1. 系统管理模块

#### 1.1 用户管理
**功能**: 管理员账号管理、角色分配、权限控制
**使用表**:
- `system_users` - 管理员用户表 🔴 **核心**
- `system_role` - 角色表 🔴 **核心**
- `system_user_role` - 用户角色关联表 🔴 **核心**
- `system_dept` - 部门表 🟡 **重要**
- `system_post` - 岗位表 🟡 **重要**

#### 1.2 权限管理
**功能**: 菜单配置、权限分配、角色权限
**使用表**:
- `system_menu` - 菜单权限表 🔴 **核心**
- `system_role_menu` - 角色菜单关联表 🔴 **核心**

#### 1.3 租户管理
**功能**: 多租户配置、租户信息管理
**使用表**:
- `system_tenant` - 租户表 🔴 **核心**
- `system_tenant_package` - 租户套餐表 🟡 **重要**

#### 1.4 字典管理
**功能**: 数据字典配置、下拉选项管理
**使用表**:
- `system_dict_type` - 字典类型表 🟡 **重要**
- `system_dict_data` - 字典数据表 🟡 **重要**

#### 1.5 通知公告
**功能**: 系统通知、公告发布
**使用表**:
- `system_notice` - 通知公告表 🟢 **一般**

### 2. 会员管理模块

#### 2.1 会员信息管理
**功能**: 会员列表、会员详情、会员状态管理
**使用表**:
- `member_user` - 会员用户表 🔴 **核心**
- `member_group` - 用户分组表 🟡 **重要**

#### 2.2 会员等级管理
**功能**: 等级配置、等级权益、升级规则
**使用表**:
- `member_level` - 会员等级表 🔴 **核心**
- `member_level_record` - 会员等级记录表 🟡 **重要**

#### 2.3 积分管理
**功能**: 积分记录查询、积分规则配置
**使用表**:
- `member_point_record` - 积分记录表 🟡 **重要**

#### 2.4 签到管理
**功能**: 签到记录查询、签到规则配置
**使用表**:
- `member_sign_in_record` - 签到记录表 🟢 **一般**

### 3. 支付管理模块

#### 3.1 支付配置
**功能**: 支付渠道配置、支付参数设置
**使用表**:
- `pay_app` - 支付应用表 🔴 **核心**
- `pay_channel` - 支付渠道表 🟡 **重要**

#### 3.2 订单管理
**功能**: 支付订单查询、订单状态管理、订单统计
**使用表**:
- `pay_order` - 支付订单表 🔴 **核心**
- `pay_order_extension` - 支付订单扩展表 🟡 **重要**

#### 3.3 退款管理
**功能**: 退款申请处理、退款记录查询
**使用表**:
- `pay_refund` - 退款订单表 🟡 **重要**

#### 3.4 钱包管理
**功能**: 用户钱包查询、余额管理、交易记录
**使用表**:
- `pay_wallet` - 用户钱包表 🔴 **核心**
- `pay_wallet_transaction` - 钱包交易记录表 🟡 **重要**

### 4. 业务管理模块

#### 4.1 文章管理
**功能**: 文章发布、文章编辑、文章审核
**使用表**:
- `author_article` - 文章表 🔴 **核心**
- `author_article_append` - 文章追加表 🟡 **重要**

#### 4.2 球队管理
**功能**: 球队信息管理、球队数据维护
**使用表**:
- `match_team` - 球队信息表 🟡 **重要**

#### 4.3 营销管理
**功能**: 轮播图管理、活动配置
**使用表**:
- `banner` - 轮播图表 🟡 **重要**

#### 4.4 虚拟货币管理
**功能**: 鱼币充值管理、充值记录查询
**使用表**:
- `gold_order` - 鱼币充值订单表 🟡 **重要**

### 5. 基础设施模块

#### 5.1 文件管理
**功能**: 文件上传、文件管理、存储配置
**使用表**:
- `infra_file` - 文件表 🟡 **重要**
- `infra_file_config` - 文件配置表 🟡 **重要**

#### 5.2 代码生成
**功能**: 代码生成工具、表结构管理
**使用表**:
- `infra_codegen_table` - 代码生成表 🟢 **一般**
- `infra_codegen_column` - 代码生成字段表 🟢 **一般**

### 6. 微信管理模块

#### 6.1 微信账号管理
**功能**: 微信公众号配置、账号管理
**使用表**:
- `mp_account` - 微信账号表 🟡 **重要**

#### 6.2 微信用户管理
**功能**: 微信用户查询、用户标签管理
**使用表**:
- `mp_user` - 微信用户表 🟡 **重要**
- `mp_tag` - 微信标签表 🟢 **一般**

#### 6.3 微信菜单管理
**功能**: 自定义菜单配置、菜单发布
**使用表**:
- `mp_menu` - 微信菜单表 🟡 **重要**

#### 6.4 微信消息管理
**功能**: 消息记录查询、自动回复配置
**使用表**:
- `mp_message` - 微信消息表 🟢 **一般**
- `mp_auto_reply` - 微信自动回复表 🟢 **一般**

## 🔴 前端核心表清单（不能删除）

以下表是前端管理后台正常运行的基础，**绝对不能删除**：

### 系统核心表
```
system_users          # 管理员登录
system_role           # 角色管理
system_menu           # 菜单权限
system_tenant         # 租户管理
system_user_role      # 用户角色关联
system_role_menu      # 角色菜单关联
```

### 业务核心表
```
member_user           # 会员管理
member_level          # 会员等级
pay_app              # 支付配置
pay_order            # 订单管理
pay_wallet           # 钱包管理
author_article       # 文章管理
```

### 配置核心表
```
system_dict_type     # 字典类型
system_dict_data     # 字典数据
infra_file           # 文件管理
infra_file_config    # 文件配置
```

## 🗑️ 前端不使用的表（可考虑删除）

### Demo/测试表（优先删除）
```
mir_demo01_contact      # Demo联系人表
mir_demo02_category     # Demo分类表
mir_demo03_student      # Demo学生表
mir_demo03_course       # Demo课程表
mir_demo03_grade        # Demo班级表
pay_demo_transfer       # Demo转账表
```

### 日志表（可考虑删除）
```
infra_api_access_log    # API访问日志
infra_api_error_log     # API错误日志
infra_job_log          # 定时任务日志
```

### 其他可能不使用的表
```
system_sms_channel     # 短信渠道（如果不用短信功能）
system_sms_template    # 短信模板
system_sms_log         # 短信日志
system_oauth2_*        # OAuth2相关（如果不用第三方登录）
```

## 📊 统计信息

- **前端核心表**: 约15-20个
- **前端重要表**: 约20-25个  
- **前端一般表**: 约10-15个
- **可删除表**: 约10-20个

## ⚠️ 重要提醒

1. **核心表保护**: 🔴标记的表是前端核心功能必需的，删除会导致系统无法正常使用
2. **重要表谨慎**: 🟡标记的表影响重要功能，删除前需要确认业务影响
3. **一般表确认**: 🟢标记的表可能影响部分功能，删除前需要测试
4. **Demo表安全**: Demo表可以安全删除，不影响业务功能

## 🔄 维护建议

1. **定期检查**: 每月检查是否有新的无用表产生
2. **版本升级**: 升级框架版本时注意新增的Demo表
3. **功能下线**: 业务功能下线时及时清理相关表
4. **测试验证**: 删除表后在测试环境充分验证前端功能

---

**最后更新**: 2025-01-10  
**维护人员**: 系统管理员  
**备注**: 此记录基于当前系统功能分析，如有业务变更请及时更新
