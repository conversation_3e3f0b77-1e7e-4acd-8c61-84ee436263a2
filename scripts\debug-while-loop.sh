#!/bin/bash

# =============================================
# 调试while循环中的MySQL查询问题
# =============================================

echo "========================================"
echo "调试while循环中的MySQL查询问题"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

echo "数据库: $DB_NAME"

# 1. 直接测试（不在while循环中）
echo
echo "=== 测试1: 直接查询（成功的方法） ==="
TEST_TABLE="account_statistic"

echo "测试表: $TEST_TABLE"

# 表注释
TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$TEST_TABLE';" \
    --batch --skip-column-names 2>/dev/null)
echo "表注释: '$TABLE_COMMENT'"

# 字段数量
COLUMN_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$TEST_TABLE';" \
    --batch --skip-column-names 2>/dev/null)
echo "字段数量: '$COLUMN_COUNT'"

# 2. 在while循环中测试
echo
echo "=== 测试2: 在while循环中查询 ==="

# 创建测试表列表
echo "account_statistic" > ./test_tables.txt
echo "account_users" >> ./test_tables.txt

while read table_name; do
    if [ -n "$table_name" ]; then
        echo "处理表: $table_name"
        
        # 在while循环中查询表注释
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        echo "  表注释: '$TABLE_COMMENT'"
        
        # 在while循环中查询字段数量
        COLUMN_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        echo "  字段数量: '$COLUMN_COUNT'"
        
        # 在while循环中查询字段详情
        temp_file="./test_columns_${table_name}.txt"
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COLUMN_NAME, COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' LIMIT 3;" \
            --batch --skip-column-names 2>/dev/null > "$temp_file"
        
        echo "  临时文件大小: $(wc -c < "$temp_file") 字节"
        echo "  临时文件行数: $(wc -l < "$temp_file") 行"
        
        if [ -s "$temp_file" ]; then
            echo "  ✅ 字段查询成功"
            echo "  前3个字段:"
            cat "$temp_file"
        else
            echo "  ❌ 字段查询失败"
        fi
        
        rm -f "$temp_file"
        echo
    fi
done < ./test_tables.txt

# 3. 测试环境变量
echo "=== 测试3: 检查环境变量 ==="
echo "在while循环外:"
echo "  DB_HOST: $DB_HOST"
echo "  DB_NAME: $DB_NAME"

echo "account_statistic" | while read table_name; do
    echo "在while循环内:"
    echo "  DB_HOST: $DB_HOST"
    echo "  DB_NAME: $DB_NAME"
    echo "  table_name: $table_name"
done

# 清理
rm -f ./test_tables.txt

echo
echo "========================================"
echo "调试完成！"
echo "========================================"
