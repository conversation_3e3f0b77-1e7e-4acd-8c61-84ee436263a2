#!/bin/bash

# =============================================
# 新建库脚本生成器
# 按模块分类生成数据库创建脚本
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "========================================"
echo "新建库脚本生成器"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p sql/restructure

log_info "生成新建库脚本..."

# 定义表分类
FRAMEWORK_TABLES=("system_tenant" "system_users" "system_role" "system_menu" "system_user_role" "system_role_menu" "system_dept" "system_post" "system_dict_type" "system_dict_data" "system_notice" "infra_file" "infra_file_config" "infra_codegen_table" "infra_codegen_column")

ADMIN_TABLES=("system_users" "system_role" "system_menu" "system_user_role" "system_role_menu" "system_dept" "system_post" "system_dict_type" "system_dict_data" "system_notice")

BUSINESS_TABLES=("member_user" "member_level" "member_level_record" "member_point_record" "member_sign_in_record" "member_group" "pay_app" "pay_order" "pay_refund" "pay_wallet" "pay_wallet_transaction" "author_article" "author_article_append" "match_team" "banner" "gold_order" "mp_account" "mp_user" "mp_message" "mp_tag" "mp_menu" "mp_auto_reply")

# 1. 生成平台框架脚本
log_info "生成平台框架脚本..."

cat > sql/restructure/01-framework-tables.sql << 'EOF'
-- =============================================
-- 足球彩票系统 - 平台框架表
-- 系统核心框架功能，所有应用共享
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 租户管理
-- =============================================

-- 租户表
CREATE TABLE IF NOT EXISTS `system_tenant` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '租户名',
    `contact_user_id` bigint DEFAULT NULL COMMENT '联系人的用户编号',
    `contact_name` varchar(30) NOT NULL COMMENT '联系人',
    `contact_mobile` varchar(500) DEFAULT NULL COMMENT '联系手机',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
    `website` varchar(256) DEFAULT '' COMMENT '绑定域名',
    `package_id` bigint NOT NULL COMMENT '租户套餐编号',
    `expire_time` datetime NOT NULL COMMENT '过期时间',
    `account_count` int NOT NULL COMMENT '账号数量',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';

-- =============================================
-- 用户权限管理
-- =============================================

-- 用户信息表
CREATE TABLE IF NOT EXISTS `system_users` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `username` varchar(30) NOT NULL COMMENT '用户账号',
    `password` varchar(100) DEFAULT '' COMMENT '密码',
    `nickname` varchar(30) NOT NULL COMMENT '用户昵称',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
    `post_ids` varchar(255) DEFAULT NULL COMMENT '岗位编号数组',
    `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
    `mobile` varchar(11) DEFAULT '' COMMENT '手机号码',
    `sex` tinyint DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
    `avatar` varchar(512) DEFAULT '' COMMENT '头像地址',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
    `login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
    `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`,`update_time`,`tenant_id`),
    KEY `idx_mobile` (`mobile`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 角色信息表
CREATE TABLE IF NOT EXISTS `system_role` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '角色名称',
    `code` varchar(100) NOT NULL COMMENT '角色权限字符串',
    `sort` int NOT NULL COMMENT '显示顺序',
    `data_scope` tinyint NOT NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
    `data_scope_dept_ids` varchar(500) DEFAULT '' COMMENT '数据范围(指定部门数组)',
    `status` tinyint NOT NULL COMMENT '角色状态（0正常 1停用）',
    `type` tinyint NOT NULL COMMENT '角色类型',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色信息表';

-- 菜单权限表
CREATE TABLE IF NOT EXISTS `system_menu` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `name` varchar(50) NOT NULL COMMENT '菜单名称',
    `permission` varchar(100) DEFAULT '' COMMENT '权限标识',
    `type` tinyint NOT NULL COMMENT '菜单类型（1目录 2菜单 3按钮）',
    `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
    `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父菜单ID',
    `path` varchar(200) DEFAULT '' COMMENT '路由地址',
    `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
    `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
    `component_name` varchar(255) DEFAULT NULL COMMENT '组件名',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
    `visible` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否可见',
    `keep_alive` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否缓存',
    `always_show` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否总是显示',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单权限表';

-- 用户和角色关联表
CREATE TABLE IF NOT EXISTS `system_user_role` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户和角色关联表';

-- 角色和菜单关联表
CREATE TABLE IF NOT EXISTS `system_role_menu` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `menu_id` bigint NOT NULL COMMENT '菜单ID',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_menu_id` (`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和菜单关联表';

-- =============================================
-- 字典管理
-- =============================================

-- 字典类型表
CREATE TABLE IF NOT EXISTS `system_dict_type` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
    `name` varchar(100) DEFAULT '' COMMENT '字典名称',
    `type` varchar(100) DEFAULT '' COMMENT '字典类型',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典类型表';

-- 字典数据表
CREATE TABLE IF NOT EXISTS `system_dict_data` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
    `sort` int NOT NULL DEFAULT '0' COMMENT '字典排序',
    `label` varchar(100) DEFAULT '' COMMENT '字典标签',
    `value` varchar(100) DEFAULT '' COMMENT '字典键值',
    `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `color_type` varchar(100) DEFAULT '' COMMENT '颜色类型',
    `css_class` varchar(100) DEFAULT '' COMMENT 'css 样式',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_dict_type` (`dict_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典数据表';

SET FOREIGN_KEY_CHECKS = 1;
EOF

log_success "平台框架脚本已生成: sql/restructure/01-framework-tables.sql"

# 2. 生成后台前端脚本
log_info "生成后台前端脚本..."

cat > sql/restructure/02-admin-frontend-tables.sql << 'EOF'
-- =============================================
-- 足球彩票系统 - 后台前端表
-- 后台管理系统专用表和配置
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 组织架构管理
-- =============================================

-- 部门表
CREATE TABLE IF NOT EXISTS `system_dept` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL DEFAULT '' COMMENT '部门名称',
    `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父部门id',
    `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
    `leader_user_id` bigint DEFAULT NULL COMMENT '负责人',
    `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
    `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
    `status` tinyint NOT NULL COMMENT '部门状态（0正常 1停用）',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 岗位信息表
CREATE TABLE IF NOT EXISTS `system_post` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `code` varchar(64) NOT NULL COMMENT '岗位编码',
    `name` varchar(50) NOT NULL COMMENT '岗位名称',
    `sort` int NOT NULL COMMENT '显示顺序',
    `status` tinyint NOT NULL COMMENT '状态（0正常 1停用）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位信息表';

-- 通知公告表
CREATE TABLE IF NOT EXISTS `system_notice` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '公告ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `title` varchar(50) NOT NULL COMMENT '公告标题',
    `content` text NOT NULL COMMENT '公告内容',
    `type` tinyint NOT NULL COMMENT '公告类型（1通知 2公告）',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知公告表';

-- =============================================
-- 文件管理
-- =============================================

-- 文件表
CREATE TABLE IF NOT EXISTS `infra_file` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `config_id` bigint DEFAULT NULL COMMENT '配置编号',
    `name` varchar(256) DEFAULT NULL COMMENT '文件名',
    `path` varchar(512) NOT NULL COMMENT '文件路径',
    `url` varchar(1024) NOT NULL COMMENT '文件 URL',
    `type` varchar(128) DEFAULT NULL COMMENT '文件类型',
    `size` int NOT NULL COMMENT '文件大小',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件表';

-- 文件配置表
CREATE TABLE IF NOT EXISTS `infra_file_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(63) NOT NULL COMMENT '配置名',
    `storage` tinyint NOT NULL COMMENT '存储器',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `master` bit(1) NOT NULL COMMENT '是否为主配置',
    `config` varchar(4096) NOT NULL COMMENT '存储配置',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件配置表';

SET FOREIGN_KEY_CHECKS = 1;
EOF

log_success "后台前端脚本已生成: sql/restructure/02-admin-frontend-tables.sql"

# 3. 生成应用功能表脚本
log_info "生成应用功能表脚本..."

cat > sql/restructure/03-application-tables.sql << 'EOF'
-- =============================================
-- 足球彩票系统 - 应用功能表
-- 核心业务功能表
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 会员模块
-- =============================================

-- 会员用户表
CREATE TABLE IF NOT EXISTS `member_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `username` varchar(30) NOT NULL COMMENT '用户名',
    `password` varchar(100) DEFAULT '' COMMENT '密码',
    `nickname` varchar(30) DEFAULT '' COMMENT '用户昵称',
    `avatar` varchar(512) DEFAULT '' COMMENT '头像',
    `sex` tinyint DEFAULT '0' COMMENT '性别',
    `area_id` bigint DEFAULT NULL COMMENT '地区编号',
    `mobile` varchar(11) NOT NULL COMMENT '手机号',
    `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
    `birthday` date DEFAULT NULL COMMENT '出生日期',
    `mark` varchar(255) DEFAULT '' COMMENT '会员备注',
    `point` int NOT NULL DEFAULT '0' COMMENT '积分',
    `tag_ids` varchar(255) DEFAULT '' COMMENT '标签编号列表，以逗号分隔',
    `level_id` bigint DEFAULT NULL COMMENT '等级编号',
    `experience` int NOT NULL DEFAULT '0' COMMENT '经验',
    `gold` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '鱼币',
    `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
    `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_mobile` (`mobile`,`update_time`,`tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员用户';

-- 会员等级表
CREATE TABLE IF NOT EXISTS `member_level` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '等级名称',
    `experience` int NOT NULL COMMENT '升级经验',
    `level` int NOT NULL COMMENT '等级',
    `discount_percent` tinyint NOT NULL COMMENT '享受折扣',
    `icon` varchar(255) DEFAULT '' COMMENT '等级图标',
    `bg_color` varchar(20) DEFAULT '' COMMENT '等级背景色',
    `status` tinyint NOT NULL COMMENT '状态（字典值：0开启，1关闭）',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员等级';

-- =============================================
-- 支付模块
-- =============================================

-- 支付应用信息
CREATE TABLE IF NOT EXISTS `pay_app` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '应用编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(64) NOT NULL COMMENT '应用名',
    `status` tinyint NOT NULL COMMENT '开启状态',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `pay_notify_url` varchar(1024) NOT NULL COMMENT '支付结果的回调地址',
    `refund_notify_url` varchar(1024) NOT NULL COMMENT '退款结果的回调地址',
    `merchant_name` varchar(64) NOT NULL COMMENT '商户名称',
    `merchant_short_name` varchar(32) NOT NULL COMMENT '商户简称',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付应用信息';

-- 支付订单
CREATE TABLE IF NOT EXISTS `pay_order` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `app_id` bigint NOT NULL COMMENT '应用编号',
    `channel_id` bigint DEFAULT NULL COMMENT '渠道编号',
    `channel_code` varchar(32) DEFAULT NULL COMMENT '渠道编码',
    `merchant_order_id` varchar(64) NOT NULL COMMENT '商户订单编号',
    `subject` varchar(32) NOT NULL COMMENT '商品标题',
    `body` varchar(128) NOT NULL COMMENT '商品描述',
    `notify_url` varchar(1024) NOT NULL COMMENT '异步通知地址',
    `notify_status` tinyint NOT NULL DEFAULT '0' COMMENT '通知状态',
    `price` int NOT NULL COMMENT '支付金额，单位：分',
    `channel_fee_rate` double DEFAULT NULL COMMENT '渠道手续费，单位：百分比',
    `channel_fee_price` int DEFAULT NULL COMMENT '渠道手续费，单位：分',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '支付状态',
    `user_ip` varchar(50) DEFAULT NULL COMMENT '用户 IP',
    `expire_time` datetime DEFAULT NULL COMMENT '订单失效时间',
    `success_time` datetime DEFAULT NULL COMMENT '订单支付时间',
    `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
    `success_extension_id` bigint DEFAULT NULL COMMENT '支付成功的订单拓展单编号',
    `refund_status` tinyint NOT NULL DEFAULT '0' COMMENT '退款状态',
    `refund_times` tinyint NOT NULL DEFAULT '0' COMMENT '退款次数',
    `refund_price` int NOT NULL DEFAULT '0' COMMENT '退款总金额，单位：分',
    `channel_user_id` varchar(255) DEFAULT NULL COMMENT '渠道用户编号',
    `channel_order_no` varchar(64) DEFAULT NULL COMMENT '渠道订单号',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_app_id` (`app_id`),
    KEY `idx_merchant_order_id` (`merchant_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付订单';

-- 会员钱包
CREATE TABLE IF NOT EXISTS `pay_wallet` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `user_id` bigint NOT NULL COMMENT '用户编号',
    `user_type` tinyint NOT NULL DEFAULT '0' COMMENT '用户类型',
    `balance` int NOT NULL DEFAULT '0' COMMENT '余额，单位分',
    `total_expense` int NOT NULL DEFAULT '0' COMMENT '累计支出，单位分',
    `total_recharge` int NOT NULL DEFAULT '0' COMMENT '累计充值，单位分',
    `freeze_price` int NOT NULL DEFAULT '0' COMMENT '冻结金额，单位分',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user` (`user_id`,`user_type`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员钱包';

-- =============================================
-- 业务模块
-- =============================================

-- 文章表
CREATE TABLE IF NOT EXISTS `author_article` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `author_id` bigint NOT NULL COMMENT '作者编号',
    `title` varchar(255) NOT NULL COMMENT '标题',
    `intro` text COMMENT '简介',
    `pic_url` varchar(255) DEFAULT '' COMMENT '封面图片地址',
    `free_contents` longtext COMMENT '免费内容',
    `contents` longtext COMMENT '付费内容',
    `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
    `read_count` int NOT NULL DEFAULT '0' COMMENT '阅读次数',
    `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞次数',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_author_id` (`author_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';

-- 轮播图
CREATE TABLE IF NOT EXISTS `banner` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `title` varchar(64) NOT NULL COMMENT '标题',
    `pic_url` varchar(255) NOT NULL COMMENT '图片地址',
    `url` varchar(255) DEFAULT '' COMMENT '跳转地址',
    `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `memo` varchar(255) DEFAULT '' COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='轮播图';

SET FOREIGN_KEY_CHECKS = 1;
EOF

log_success "应用功能表脚本已生成: sql/restructure/03-application-tables.sql"

# 4. 生成基础数据脚本
log_info "生成基础数据脚本..."

cat > sql/restructure/04-basic-data.sql << 'EOF'
-- =============================================
-- 足球彩票系统 - 基础数据
-- 系统初始化数据和配置
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 默认租户数据
-- =============================================

-- 插入默认租户
INSERT IGNORE INTO `system_tenant` (`id`, `name`, `contact_name`, `contact_mobile`, `status`, `package_id`, `expire_time`, `account_count`, `creator`, `create_time`) VALUES
(1, '足球彩票平台', '系统管理员', '***********', 0, 1, '2099-12-31 23:59:59', 9999, '1', NOW());

-- =============================================
-- 系统菜单数据
-- =============================================

-- 插入系统菜单
INSERT IGNORE INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`) VALUES
(1, '系统管理', '', 1, 10, 0, '/system', 'ep:tools', NULL, 0),
(2, '用户管理', 'system:user:list', 2, 1, 1, 'user', 'ep:avatar', 'system/user/index', 0),
(3, '角色管理', 'system:role:list', 2, 2, 1, 'role', 'ep:user', 'system/role/index', 0),
(4, '菜单管理', 'system:menu:list', 2, 3, 1, 'menu', 'ep:menu', 'system/menu/index', 0),
(5, '租户管理', 'system:tenant:list', 2, 4, 1, 'tenant', 'ep:house', 'system/tenant/index', 0),

(100, '会员管理', '', 1, 20, 0, '/member', 'ep:user-filled', NULL, 0),
(101, '会员用户', 'member:user:list', 2, 1, 100, 'user', 'ep:avatar', 'member/user/index', 0),
(102, '会员等级', 'member:level:list', 2, 2, 100, 'level', 'ep:trophy', 'member/level/index', 0),

(200, '支付管理', '', 1, 30, 0, '/pay', 'ep:money', NULL, 0),
(201, '支付应用', 'pay:app:list', 2, 1, 200, 'app', 'ep:postcard', 'pay/app/index', 0),
(202, '支付订单', 'pay:order:list', 2, 2, 200, 'order', 'ep:list', 'pay/order/index', 0),
(203, '用户钱包', 'pay:wallet:list', 2, 3, 200, 'wallet', 'ep:wallet', 'pay/wallet/index', 0),

(300, '内容管理', '', 1, 40, 0, '/content', 'ep:document', NULL, 0),
(301, '文章管理', 'content:article:list', 2, 1, 300, 'article', 'ep:document', 'content/article/index', 0),
(302, '轮播图', 'content:banner:list', 2, 2, 300, 'banner', 'ep:picture', 'content/banner/index', 0);

-- =============================================
-- 系统角色数据
-- =============================================

-- 插入系统角色
INSERT IGNORE INTO `system_role` (`id`, `tenant_id`, `name`, `code`, `sort`, `data_scope`, `status`, `type`, `remark`, `creator`, `create_time`) VALUES
(1, 1, '超级管理员', 'super_admin', 1, 1, 0, 1, '超级管理员', '1', NOW()),
(2, 1, '普通角色', 'common', 2, 2, 0, 1, '普通角色', '1', NOW());

-- =============================================
-- 系统用户数据
-- =============================================

-- 插入系统用户 (密码: admin123)
INSERT IGNORE INTO `system_users` (`id`, `tenant_id`, `username`, `password`, `nickname`, `remark`, `email`, `mobile`, `sex`, `status`, `creator`, `create_time`) VALUES
(1, 1, 'admin', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH.ulu33dHOiBE/Q/V9DdqoCm2', '系统管理员', '管理员', '<EMAIL>', '***********', 1, 0, 'admin', NOW());

-- 用户角色关联
INSERT IGNORE INTO `system_user_role` (`user_id`, `role_id`, `tenant_id`, `creator`, `create_time`) VALUES
(1, 1, 1, '1', NOW());

-- 角色菜单关联
INSERT IGNORE INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `creator`, `create_time`) VALUES
(1, 1, 1, '1', NOW()),
(1, 2, 1, '1', NOW()),
(1, 3, 1, '1', NOW()),
(1, 4, 1, '1', NOW()),
(1, 5, 1, '1', NOW()),
(1, 100, 1, '1', NOW()),
(1, 101, 1, '1', NOW()),
(1, 102, 1, '1', NOW()),
(1, 200, 1, '1', NOW()),
(1, 201, 1, '1', NOW()),
(1, 202, 1, '1', NOW()),
(1, 203, 1, '1', NOW()),
(1, 300, 1, '1', NOW()),
(1, 301, 1, '1', NOW()),
(1, 302, 1, '1', NOW());

-- =============================================
-- 字典数据
-- =============================================

-- 字典类型
INSERT IGNORE INTO `system_dict_type` (`id`, `name`, `type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '用户性别', 'system_user_sex', 0, '用户性别列表', 'admin', NOW()),
(2, '系统状态', 'common_status', 0, '登录状态列表', 'admin', NOW()),
(3, '会员状态', 'member_status', 0, '会员状态列表', 'admin', NOW()),
(4, '支付状态', 'pay_order_status', 0, '支付订单状态', 'admin', NOW());

-- 字典数据
INSERT IGNORE INTO `system_dict_data` (`id`, `sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, 1, '男', '1', 'system_user_sex', 0, '性别男', 'admin', NOW()),
(2, 2, '女', '2', 'system_user_sex', 0, '性别女', 'admin', NOW()),
(3, 1, '正常', '0', 'common_status', 0, '正常状态', 'admin', NOW()),
(4, 2, '停用', '1', 'common_status', 0, '停用状态', 'admin', NOW()),
(5, 1, '正常', '0', 'member_status', 0, '正常状态', 'admin', NOW()),
(6, 2, '禁用', '1', 'member_status', 0, '禁用状态', 'admin', NOW()),
(7, 1, '待支付', '0', 'pay_order_status', 0, '待支付', 'admin', NOW()),
(8, 2, '已支付', '10', 'pay_order_status', 0, '已支付', 'admin', NOW()),
(9, 3, '已关闭', '20', 'pay_order_status', 0, '已关闭', 'admin', NOW());

-- =============================================
-- 默认会员等级
-- =============================================

INSERT IGNORE INTO `member_level` (`id`, `tenant_id`, `name`, `experience`, `level`, `discount_percent`, `status`, `creator`, `create_time`) VALUES
(1, 1, '青铜会员', 0, 1, 100, 0, 'admin', NOW()),
(2, 1, '白银会员', 1000, 2, 95, 0, 'admin', NOW()),
(3, 1, '黄金会员', 3000, 3, 90, 0, 'admin', NOW()),
(4, 1, '钻石会员', 10000, 4, 85, 0, 'admin', NOW());

-- =============================================
-- 默认支付应用
-- =============================================

INSERT IGNORE INTO `pay_app` (`id`, `tenant_id`, `name`, `status`, `remark`, `pay_notify_url`, `refund_notify_url`, `merchant_name`, `merchant_short_name`, `creator`, `create_time`) VALUES
(1, 1, '足球彩票支付', 0, '默认支付应用', 'http://localhost:8080/api/pay/notify', 'http://localhost:8080/api/pay/refund-notify', '足球彩票平台', '足彩平台', 'admin', NOW());

SET FOREIGN_KEY_CHECKS = 1;
EOF

log_success "基础数据脚本已生成: sql/restructure/04-basic-data.sql"

# 5. 生成主安装脚本
log_info "生成主安装脚本..."

cat > sql/restructure/install.sql << 'EOF'
-- =============================================
-- 足球彩票系统 - 数据库安装脚本
-- 执行顺序：框架表 -> 后台表 -> 应用表 -> 基础数据
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `football_lottery` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `football_lottery`;

-- 执行框架表
SOURCE 01-framework-tables.sql;

-- 执行后台前端表
SOURCE 02-admin-frontend-tables.sql;

-- 执行应用功能表
SOURCE 03-application-tables.sql;

-- 执行基础数据
SOURCE 04-basic-data.sql;

-- 完成提示
SELECT '数据库安装完成！' AS message;
SELECT '默认管理员账号: admin' AS admin_info;
SELECT '默认管理员密码: admin123' AS password_info;
EOF

log_success "主安装脚本已生成: sql/restructure/install.sql"

echo
echo "========================================"
echo "新建库脚本生成完成！"
echo "========================================"
echo "📄 sql/restructure/01-framework-tables.sql - 平台框架表"
echo "📄 sql/restructure/02-admin-frontend-tables.sql - 后台前端表"
echo "📄 sql/restructure/03-application-tables.sql - 应用功能表"
echo "📄 sql/restructure/04-basic-data.sql - 基础数据"
echo "📄 sql/restructure/install.sql - 主安装脚本"
echo "========================================"
echo
echo "安装方法："
echo "1. cd sql/restructure"
echo "2. mysql -u root -p < install.sql"
