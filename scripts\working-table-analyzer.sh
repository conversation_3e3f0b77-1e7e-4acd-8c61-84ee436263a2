#!/bin/bash

# =============================================
# 基于成功测试脚本的完整表分析器
# 使用与direct-table-structure.sh完全相同的方法
# =============================================

echo "========================================"
echo "基于成功测试脚本的完整表分析器"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p working-analysis

echo "[INFO] 分析数据库: $DB_NAME"

# 获取所有表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > ./temp_all_tables_working.txt

TOTAL_TABLES=$(wc -l < ./temp_all_tables_working.txt)
echo "[INFO] 发现 $TOTAL_TABLES 个表"

# 1. 生成表结构详情
echo "[INFO] 生成表结构详情..."

cat > working-analysis/table-structure-working.md << EOF
# 数据库表结构详情

## 📊 数据库: $DB_NAME

**生成时间**: $(date)
**总表数**: $TOTAL_TABLES

EOF

# 只处理前5个表，使用与测试脚本完全相同的方法
table_count=0
while read table_name; do
    if [ -n "$table_name" ] && [ $table_count -lt 5 ]; then
        table_count=$((table_count + 1))
        echo "处理表 $table_count: $table_name"
        
        echo "### 表: $table_name" >> working-analysis/table-structure-working.md
        echo "" >> working-analysis/table-structure-working.md
        
        # 获取表注释 - 使用与测试脚本完全相同的方法
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        [ -z "$TABLE_COMMENT" ] && TABLE_COMMENT="无注释"
        echo "  表注释: $TABLE_COMMENT"
        echo "**表说明**: $TABLE_COMMENT" >> working-analysis/table-structure-working.md
        echo "" >> working-analysis/table-structure-working.md
        
        # 生成字段信息表格
        echo "| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |" >> working-analysis/table-structure-working.md
        echo "|--------|------|----------|-----|--------|------|" >> working-analysis/table-structure-working.md
        
        # 获取字段详情 - 使用与调试脚本完全相同的方法
        temp_file="./temp_columns_working_${table_name}.txt"
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, IFNULL(COLUMN_KEY,''), IFNULL(COLUMN_DEFAULT,''), IFNULL(COLUMN_COMMENT,'') FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' ORDER BY ORDINAL_POSITION;" \
            --batch --skip-column-names 2>/dev/null > "$temp_file"
        
        echo "  临时文件大小: $(wc -c < "$temp_file") 字节"
        echo "  临时文件行数: $(wc -l < "$temp_file") 行"

        if [ -s "$temp_file" ]; then
            field_count=0
            # 使用简化的解析方法
            while read -r line; do
                [ -z "$line" ] && continue

                field_count=$((field_count + 1))

                # 分割字段（使用tab分隔）
                col_name=$(echo "$line" | cut -f1)
                col_type=$(echo "$line" | cut -f2)
                is_null=$(echo "$line" | cut -f3)
                col_key=$(echo "$line" | cut -f4)
                col_default=$(echo "$line" | cut -f5)
                col_comment=$(echo "$line" | cut -f6)

                # 处理空值
                [ -z "$col_key" ] && col_key=""
                [ -z "$col_default" ] && col_default=""
                [ -z "$col_comment" ] && col_comment=""

                # 处理特殊字符
                col_default=$(echo "$col_default" | sed 's/|/\\|/g' | sed 's/NULL//')
                col_comment=$(echo "$col_comment" | sed 's/|/\\|/g' | sed 's/NULL//')

                # 格式化键类型
                case "$col_key" in
                    "PRI") col_key="🔑 PK" ;;
                    "UNI") col_key="🔒 UK" ;;
                    "MUL") col_key="📇 FK" ;;
                    *) col_key="" ;;
                esac

                # 格式化是否为空
                case "$is_null" in
                    "NO") is_null="❌" ;;
                    "YES") is_null="✅" ;;
                esac

                echo "| $col_name | $col_type | $is_null | $col_key | $col_default | $col_comment |" >> working-analysis/table-structure-working.md
            done < "$temp_file"
            echo "  处理了 $field_count 个字段"
        else
            echo "  ❌ 无法获取字段信息"
            echo "| - | 无法获取字段信息 | - | - | - | 请检查数据库权限 |" >> working-analysis/table-structure-working.md
        fi
        
        rm -f "$temp_file"
        
        echo "" >> working-analysis/table-structure-working.md
        echo "---" >> working-analysis/table-structure-working.md
        echo "" >> working-analysis/table-structure-working.md
    elif [ $table_count -ge 5 ]; then
        break
    fi
done < ./temp_all_tables_working.txt

echo "[SUCCESS] 表结构详情已生成: working-analysis/table-structure-working.md"

# 2. 生成数据字典
echo "[INFO] 生成数据字典..."

cat > working-analysis/data-dictionary-working.md << EOF
# 数据字典

## 📊 数据库: $DB_NAME

**生成时间**: $(date)

## 📋 表概览

| 序号 | 表名 | 注释 | 字段数 | 记录数 |
|------|------|------|--------|--------|
EOF

# 生成表概览 - 使用相同的方法
table_num=1
table_count=0
while read table_name; do
    if [ -n "$table_name" ] && [ $table_count -lt 5 ]; then
        table_count=$((table_count + 1))
        echo "处理表概览 $table_count: $table_name"
        
        # 获取表信息
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        COLUMN_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        ROW_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_ROWS, 0) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        # 处理空值
        [ -z "$TABLE_COMMENT" ] && TABLE_COMMENT="无注释"
        [ -z "$COLUMN_COUNT" ] && COLUMN_COUNT="0"
        [ -z "$ROW_COUNT" ] && ROW_COUNT="0"
        
        echo "| $table_num | $table_name | $TABLE_COMMENT | $COLUMN_COUNT | $ROW_COUNT |" >> working-analysis/data-dictionary-working.md
        table_num=$((table_num + 1))
    elif [ $table_count -ge 5 ]; then
        break
    fi
done < ./temp_all_tables_working.txt

echo "" >> working-analysis/data-dictionary-working.md
echo "## 📝 字段详情" >> working-analysis/data-dictionary-working.md
echo "" >> working-analysis/data-dictionary-working.md

# 按表生成字段详情
table_count=0
while read table_name; do
    if [ -n "$table_name" ] && [ $table_count -lt 5 ]; then
        table_count=$((table_count + 1))
        echo "处理字段详情 $table_count: $table_name"
        
        echo "### $table_name" >> working-analysis/data-dictionary-working.md
        echo "" >> working-analysis/data-dictionary-working.md
        
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        [ -z "$TABLE_COMMENT" ] && TABLE_COMMENT="无注释"
        echo "**表说明**: $TABLE_COMMENT" >> working-analysis/data-dictionary-working.md
        echo "" >> working-analysis/data-dictionary-working.md
        
        echo "| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |" >> working-analysis/data-dictionary-working.md
        echo "|------|------|------|-----|--------|------|" >> working-analysis/data-dictionary-working.md
        
        # 获取字段详情
        temp_file="./temp_dict_working_${table_name}.txt"
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT 
                COLUMN_NAME,
                COLUMN_TYPE,
                IF(IS_NULLABLE='YES', 'Y', 'N'),
                IFNULL(COLUMN_KEY, ''),
                IFNULL(COLUMN_DEFAULT, ''),
                IFNULL(COLUMN_COMMENT, '')
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' 
            ORDER BY ORDINAL_POSITION;" \
            --batch --skip-column-names 2>/dev/null > "$temp_file"
        
        if [ -s "$temp_file" ]; then
            while IFS=$'\t' read -r col_name col_type is_null col_key col_default col_comment; do
                [ -z "$col_name" ] && continue
                
                # 处理特殊字符
                col_default=$(echo "$col_default" | sed 's/NULL//')
                col_comment=$(echo "$col_comment" | sed 's/NULL//')
                
                echo "| $col_name | $col_type | $is_null | $col_key | $col_default | $col_comment |" >> working-analysis/data-dictionary-working.md
            done < "$temp_file"
        fi
        
        rm -f "$temp_file"
        echo "" >> working-analysis/data-dictionary-working.md
    elif [ $table_count -ge 5 ]; then
        break
    fi
done < ./temp_all_tables_working.txt

echo "[SUCCESS] 数据字典已生成: working-analysis/data-dictionary-working.md"

# 清理临时文件
rm -f ./temp_*_working*.txt

echo
echo "========================================"
echo "基于成功测试脚本的分析完成！"
echo "========================================"
echo "📊 表结构详情: working-analysis/table-structure-working.md"
echo "📖 数据字典: working-analysis/data-dictionary-working.md"
echo "📊 处理表数: 前5个表"
echo "========================================"
