# 数据库ER图生成指南

## 🎯 目标

连接实际数据库，分析表结构和关系，生成准确的ER图，并更新项目文档以保持与数据库结构的一致性。

## 🛠️ 工具准备

### 1. 数据库连接工具
确保您有以下工具之一：
- MySQL客户端
- phpMyAdmin
- Navicat
- DBeaver
- 或其他数据库管理工具

### 2. 脚本工具
项目提供了以下脚本：
- `scripts/quick-er-diagram.sh` - 快速生成ER图
- `scripts/generate-er-diagram.sh` - 完整ER图生成
- `scripts/analyze-database-structure.sql` - 数据库结构分析
- `scripts/update-database-docs.sh` - 更新文档

## 📋 执行步骤

### 步骤1: 配置数据库连接

创建数据库配置文件：

```bash
# 创建配置文件
cp config/database.conf.example config/database.conf

# 编辑配置
vim config/database.conf
```

配置内容示例：
```bash
DB_HOST=localhost
DB_PORT=3306
DB_NAME=mir
DB_USER=root
DB_PASSWORD=your_password
```

### 步骤2: 快速生成ER图

```bash
# 给脚本执行权限
chmod +x scripts/quick-er-diagram.sh

# 执行快速ER图生成
./scripts/quick-er-diagram.sh
```

这将生成：
- `docs/database/er-diagram.mmd` - 完整ER图
- `docs/database/er-diagram-simple.mmd` - 简化版ER图
- `docs/database/table-statistics.txt` - 表统计信息

### 步骤3: 查看ER图

有多种方式查看生成的Mermaid ER图：

#### 方法1: 在线查看
1. 访问 [Mermaid Live Editor](https://mermaid.live/)
2. 复制 `docs/database/er-diagram-simple.mmd` 的内容
3. 粘贴到编辑器中查看

#### 方法2: VSCode插件
1. 安装 "Mermaid Preview" 插件
2. 打开 `.mmd` 文件
3. 使用 `Ctrl+Shift+P` 搜索 "Mermaid Preview"

#### 方法3: 使用项目内置渲染
脚本已经在上面展示了核心业务表的ER图。

### 步骤4: 完整数据库分析

如果需要更详细的分析：

```bash
# 执行完整分析
chmod +x scripts/update-database-docs.sh
./scripts/update-database-docs.sh
```

这将生成：
- 完整的数据库结构分析
- 更新的技术文档
- 详细的表关系图

## 📊 生成的文档结构

```
docs/
├── database/
│   ├── er-diagram.mmd              # 完整ER图
│   ├── er-diagram-simple.mmd       # 简化ER图
│   ├── table-statistics.txt        # 表统计
│   ├── structure-analysis.txt      # 结构分析
│   ├── tables_info.txt            # 表信息
│   ├── columns_info.txt           # 字段信息
│   ├── foreign_keys.txt           # 外键关系
│   └── indexes_info.txt           # 索引信息
├── 数据库设计文档.md                # 数据库设计文档
└── 技术文档.md                     # 更新后的技术文档
```

## 🔍 ER图解读

### 核心业务表关系

上面展示的ER图包含了系统的核心业务表：

1. **租户中心**: `system_tenant` 作为多租户的核心
2. **用户体系**: `member_user` 连接各个业务模块
3. **支付体系**: `pay_*` 表群处理支付相关业务
4. **内容体系**: `author_article` 处理文章内容

### 关系类型说明

- `||--o{` : 一对多关系
- `||--||` : 一对一关系
- `}o--||` : 多对一关系

### 字段类型说明

- `PK` : 主键
- `FK` : 外键
- `UK` : 唯一键

## 🔧 自定义分析

### 分析特定模块

如果只想分析特定模块的表：

```sql
-- 分析会员模块
SELECT TABLE_NAME, TABLE_COMMENT 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME LIKE 'member_%';

-- 分析支付模块
SELECT TABLE_NAME, TABLE_COMMENT 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME LIKE 'pay_%';
```

### 检查多租户覆盖

```sql
-- 检查哪些表已添加tenant_id字段
SELECT 
    t.TABLE_NAME,
    CASE WHEN c.COLUMN_NAME IS NOT NULL THEN '✅ 已添加' ELSE '❌ 未添加' END as tenant_id_status
FROM INFORMATION_SCHEMA.TABLES t
LEFT JOIN INFORMATION_SCHEMA.COLUMNS c 
    ON t.TABLE_NAME = c.TABLE_NAME 
    AND c.COLUMN_NAME = 'tenant_id'
WHERE t.TABLE_SCHEMA = DATABASE()
  AND t.TABLE_TYPE = 'BASE TABLE'
ORDER BY t.TABLE_NAME;
```

## 📝 文档更新流程

### 1. 自动更新
运行脚本后，以下文档会自动更新：
- `docs/数据库设计文档.md`
- `docs/技术文档.md` 的数据库部分

### 2. 手动调整
根据业务需要，可能需要手动调整：
- 表关系的业务含义说明
- 特殊字段的业务规则
- 性能优化建议

### 3. 版本控制
建议将生成的文档纳入版本控制：
```bash
git add docs/
git commit -m "更新数据库设计文档和ER图"
```

## ⚠️ 注意事项

### 数据库权限
确保数据库用户有以下权限：
- `SELECT` 权限查询表结构
- 访问 `INFORMATION_SCHEMA` 的权限

### 安全考虑
- 不要在脚本中硬编码密码
- 使用配置文件管理数据库连接信息
- 生产环境建议使用只读用户

### 性能影响
- 结构分析查询对数据库性能影响很小
- 建议在业务低峰期执行完整分析

## 🚀 高级用法

### 定制ER图样式
可以修改生成的Mermaid代码来定制样式：

```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#ff0000'}}}%%
erDiagram
    %% 您的ER图内容
```

### 导出为图片
使用Mermaid CLI工具：
```bash
# 安装Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 导出为PNG
mmdc -i docs/database/er-diagram-simple.mmd -o docs/database/er-diagram.png
```

---

通过以上步骤，您可以获得与实际数据库结构完全一致的ER图和文档，确保项目文档的准确性和时效性。
