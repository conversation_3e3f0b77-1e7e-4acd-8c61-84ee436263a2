-- =============================================
-- 足球彩票系统 - 完整数据库安装脚本
-- 包含所有功能模块，不做裁剪
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `football_lottery_complete` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `football_lottery_complete`;

-- 执行各模块脚本
SOURCE 01-framework-tables.sql;
SOURCE 02-football-business-tables.sql;
SOURCE 03-member-complete-tables.sql;
SOURCE 04-quartz-scheduler-tables.sql;
SOURCE 05-payment-complete-tables.sql;
SOURCE 06-wechat-complete-tables.sql;
SOURCE 07-infrastructure-complete-tables.sql;
SOURCE 08-content-management-tables.sql;
SOURCE 09-log-tables.sql;
SOURCE 10-unclassified-tables.sql;
SOURCE 99-basic-data-complete.sql;

-- 完成提示
SELECT '完整数据库安装完成！' AS message;
SELECT '包含所有191个表的完整功能' AS info;
SELECT '默认管理员账号: admin' AS admin_info;
SELECT '默认管理员密码: admin123' AS password_info;
