# 数据库重构分析报告

## 📊 概览

本报告分析现有数据库结构，按功能模块分类表，识别未使用的表，并提供数据库重构方案。

## 🎯 重构目标

1. **模块化分离**: 按平台框架/后台前端/应用功能分类
2. **清理冗余**: 移除未使用的Demo表和测试表
3. **优化结构**: 重新组织表结构和关系
4. **标准化**: 统一命名规范和字段标准

## 📋 现有数据库分析

### 数据库基本信息

- **数据库名称**: sports_gaming
- **总表数**: 191
- **数据库大小**: 391.13MB
- **分析时间**: Thu Jul 10 15:41:10     2025

## 🏗️ 表分类分析

### 1. 平台框架表（Framework Tables）

系统核心框架功能，所有应用共享：

- **infra_api_access_log**: API (访问日志表行)
- **infra_api_error_log**: 系统异常日志 (1254行)
- **infra_codegen_column**: 代码生成表字段定义 (396行)
- **infra_codegen_table**: 代码生成表定义 (38行)
- **infra_config**: 参数配置表 (7行)
- **infra_data_source_config**: 数据源配置表 (0行)
- **infra_file**: 文件表 (16行)
文件配置表 (3行)
文件表 (0行)
- **infra_file_config**: 文件配置表 (3行)
- **infra_file_content**: 文件表 (0行)
- **infra_job**: 定时任务表 (24行)
定时任务日志表 (243412行)
- **infra_job_log**: 定时任务日志表 (243412行)
- **system_command**: 关键词与接口关联表 (0行)
- **system_dept**: 部门表 (17行)
- **system_dict_data**: 字典数据表 (451行)
- **system_dict_type**: 字典类型表 (101行)
- **system_login_log**: 系统访问记录 (828行)
- **system_mail_account**: 邮箱账号表 (4行)
- **system_mail_log**: 邮件日志表 (0行)
- **system_mail_template**: 邮件模版表 (3行)
- **system_menu**: 菜单权限表 (887行)
- **system_notice**: 通知公告表 (3行)
- **system_notify_message**: 站内信消息表 (9行)
- **system_notify_template**: 站内信模板表 (0行)
- **system_oauth2_access_token**: OAuth2 (访问令牌行)
- **system_oauth2_approve**: OAuth2 (批准表行)
- **system_oauth2_client**: OAuth2 (客户端表行)
- **system_oauth2_code**: OAuth2 (授权码表行)
- **system_oauth2_refresh_token**: OAuth2 (刷新令牌行)
- **system_operate_log**: 操作日志记录 (V2行)
- **system_post**: 岗位信息表 (5行)
- **system_role**: 角色信息表 (10行)
角色和菜单关联表 (1510行)
- **system_role_menu**: 角色和菜单关联表 (1510行)
- **system_sensitive_word**: 敏感词 (0行)
- **system_sms_channel**: 短信渠道 (0行)
- **system_sms_code**: 手机验证码 (11行)
- **system_sms_log**: 短信日志 (5行)
- **system_sms_template**: 短信模板 (13行)
- **system_social_client**: 社交客户端表 (5行)
- **system_social_user**: 社交用户表 (0行)
社交绑定表 (0行)
- **system_social_user_bind**: 社交绑定表 (0行)
- **system_tenant**: 租户表 (3行)
租户套餐表 (3行)
- **system_tenant_package**: 租户套餐表 (3行)
- **system_users**: 用户信息表 (22行)
- **system_user_post**: 用户岗位表 (14行)
- **system_user_role**: 用户和角色关联表 (24行)

### 2. 后台前端表（Admin Frontend Tables）

后台管理系统专用表：

- **system_users**: 用户信息表 (22行)
- **system_role**: 角色信息表 (10行)
角色和菜单关联表 (1510行)
- **system_menu**: 菜单权限表 (887行)
- **system_user_role**: 用户和角色关联表 (24行)
- **system_role_menu**: 角色和菜单关联表 (1510行)
- **system_dept**: 部门表 (17行)
- **system_post**: 岗位信息表 (5行)
- **system_dict_type**: 字典类型表 (101行)
- **system_dict_data**: 字典数据表 (451行)
- **system_notice**: 通知公告表 (3行)

### 3. 应用功能表（Application Tables）

#### 3.1 会员模块
- **member_address**: 0 (2025-03-21行)
- **member_attention**: 用户关注表 (63行)
- **member_author_privilege**: 用户作者特权 (49行)
- **member_bind_record**: 会员绑定记录表 (0行)
- **member_config**: 0 (2025-03-21行)
- **member_experience_record**: 0 (2025-03-21行)
- **member_group**: 0 (2025-07-10行)
- **member_level**: 0 (2025-07-10行)
0 (2025-07-10行)
- **member_level_record**: 0 (2025-07-10行)
- **member_match_attention**: 用户关注比赛信息 (14行)
- **member_match_chat**: 比赛场次聊天记录 (3行)
- **member_match_vote**: 比赛场次投票 (0行)
- **member_point_record**: 0 (2025-07-10行)
- **member_privilege_log**: 用户特权使用记录 (11行)
- **member_settlement_info**: 用户提现账号信息 (10行)
- **member_sign_in_config**: 0 (2025-03-21行)
- **member_sign_in_record**: 0 (2025-07-10行)
- **member_tag**: 0 (2025-03-21行)
- **member_user**: 用户端-用户表 (45行)
0 (2025-03-21行)
用户余额变更记录 (303行)
用户推广余额变更记录 (27行)
用户金币变更记录 (345行)
- **member_user_bak**: 0 (2025-03-21行)
- **member_user_balance_logs**: 用户余额变更记录 (303行)
- **member_user_ex_balance_logs**: 用户推广余额变更记录 (27行)
- **member_user_gold_logs**: 用户金币变更记录 (345行)

#### 3.2 支付模块
- **pay_app**: 0 (2025-07-10行)
- **pay_bank_info**: 银行信息 (4行)
- **pay_channel**: 0 (2025-03-21行)
- **pay_notify_log**: 0 (2025-03-21行)
- **pay_notify_task**: 0 (2025-03-21行)
- **pay_order**: 0 (2025-07-10行)
0 (2025-03-21行)
- **pay_order_extension**: 0 (2025-03-21行)
- **pay_refund**: 0 (2025-07-10行)
- **pay_transfer**: 0 (2025-07-10行)
- **pay_wallet**: 0 (2025-07-10行)
0 (2025-03-21行)
2 (2025-03-21行)
0 (2025-07-10行)
- **pay_wallet_recharge**: 0 (2025-03-21行)
2 (2025-03-21行)
- **pay_wallet_recharge_package**: 2 (2025-03-21行)
- **pay_wallet_transaction**: 0 (2025-07-10行)

#### 3.3 业务模块
- **author_accomplishment**: 作者战绩 (29行)
- **author_article**: 文章 (600行)
文章 (15行)
文章PV记录 (11268行)
- **author_article_append**: 文章 (15行)
- **author_article_pv_logs**: 文章PV记录 (11268行)
- **author_audit**: 作者审核列表 (18行)
作者审核列表 (9行)
- **author_audit_copy1**: 作者审核列表 (9行)
- **author_commission_rate**: 作者分成提现设置表 (3行)
- **author_day_report**: 作者日报 (2654行)
- **author_hit_rate**: 作者命中率记录表 (33行)
- **author_info**: 5 (2025-03-21行)
- **author_privilege_set**: 作者特权设置 (63行)
- **author_withdraw_logs**: 作者提现表 (33行)
- **gold_order**: 金币订单表 (183行)
- **match_category**: 赛事分类 (8行)
- **match_coach**: 教练 (127695行)
- **match_competition**: 赛事信息 (2325行)
- **match_country**: 国家 (212行)
- **match_future_record**: 未来赛程 (6032行)
- **match_history**: 比赛记录及盘口信息 (9138行)
- **match_lineup_detail**: 赛事阵容详情 (2517行)
- **match_list**: 比赛场次信息 (61651行)
- **match_live_info**: 比赛直播信息 (100行)
- **match_odds**: 比赛开盘信息 (42行)
- **match_player**: 球员 (0行)
比赛球队球员信息 (159行)
球队转会信息 (2928行)
- **match_player_info**: 比赛球队球员信息 (159行)
- **match_player_transfer**: 球队转会信息 (2928行)
- **match_point_rank**: 联赛积分排名 (4032行)
- **match_referee**: 裁判表 (4144行)
- **match_season**: 赛季信息 (0行)
- **match_stage**: 赛事阶段表 (38753行)
- **match_stats**: 比赛数据统计表 (7084行)
- **match_team**: 球队信息 (70924行)

#### 3.4 微信模块
- **mp_account**: 公众号账号表 (7行)
- **mp_auto_reply**: 公众号消息自动回复表 (0行)
- **mp_click_logs**: 公众号关注点击记录了表 (6行)
- **mp_material**: 公众号素材表 (0行)
- **mp_menu**: 公众号菜单表 (4行)
- **mp_message**: 公众号消息表 (23行)
- **mp_mini_user**: 小程序用户表 (7行)
- **mp_other_even_logs**: 1w1公众扫码记录 (0行)
- **mp_pay_config_log**: 微信配置记录日志 (4行)
- **mp_tag**: 公众号标签表 (0行)
- **mp_template_config**: 微信模板配置表 (9行)
- **mp_user**: 公众号粉丝表 (25行)

### 4. 未使用的表（Unused Tables）

#### 4.1 Demo/测试表
- **mp_template_config**: 微信模板配置表 (9行) ❌ 可删除
- **pay_demo_order**: 0 (2025-03-21行) ❌ 可删除
- **pay_demo_transfer**: 0 (2025-03-21行) ❌ 可删除
- **system_mail_template**: 邮件模版表 (3行) ❌ 可删除
- **system_notify_template**: 站内信模板表 (0行) ❌ 可删除
- **system_sms_template**: 短信模板 (13行) ❌ 可删除
- **yudao_demo01_contact**: 示例联系人表 (0行) ❌ 可删除
- **yudao_demo02_category**: 示例分类表 (6行) ❌ 可删除
- **yudao_demo03_course**: 学生课程表 (10行) ❌ 可删除
- **yudao_demo03_grade**: 学生班级表 (3行) ❌ 可删除
- **yudao_demo03_student**: 学生表 (3行) ❌ 可删除

#### 4.2 日志表
- **author_article_pv_logs**: 文章PV记录 (11268行) ⚠️ 可考虑删除
- **author_withdraw_logs**: 作者提现表 (33行) ⚠️ 可考虑删除
- **infra_api_access_log**: API (访问日志表行) ⚠️ 可考虑删除
- **infra_api_error_log**: 系统异常日志 (1254行) ⚠️ 可考虑删除
- **infra_job_log**: 定时任务日志表 (243412行) ⚠️ 可考虑删除
- **member_privilege_log**: 用户特权使用记录 (11行) ⚠️ 可考虑删除
- **member_user_balance_logs**: 用户余额变更记录 (303行) ⚠️ 可考虑删除
- **member_user_ex_balance_logs**: 用户推广余额变更记录 (27行) ⚠️ 可考虑删除
- **member_user_gold_logs**: 用户金币变更记录 (345行) ⚠️ 可考虑删除
- **mp_click_logs**: 公众号关注点击记录了表 (6行) ⚠️ 可考虑删除
- **mp_other_even_logs**: 1w1公众扫码记录 (0行) ⚠️ 可考虑删除
- **mp_pay_config_log**: 微信配置记录日志 (4行) ⚠️ 可考虑删除
- **partner_invite_logs**: 合作伙伴邀请记录表 (3行) ⚠️ 可考虑删除
- **partner_withdraw_logs**: 作者提现表 (9行) ⚠️ 可考虑删除
- **pay_notify_log**: 0 (2025-03-21行) ⚠️ 可考虑删除
- **qrtz_job_details**: 13 (2025-03-21行) ⚠️ 可考虑删除
- **recommend_user_register_logs**: 文章推荐用户注册记录 (0行) ⚠️ 可考虑删除
- **system_login_log**: 系统访问记录 (828行) ⚠️ 可考虑删除
- **system_mail_log**: 邮件日志表 (0行) ⚠️ 可考虑删除
- **system_operate_log**: 操作日志记录 (V2行) ⚠️ 可考虑删除
- **system_sms_log**: 短信日志 (5行) ⚠️ 可考虑删除
