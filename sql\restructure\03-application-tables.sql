-- =============================================
-- 足球彩票系统 - 应用功能表（完整版）
-- 包含所有业务功能表，不做裁剪
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 会员模块
-- =============================================

-- 会员用户表
CREATE TABLE IF NOT EXISTS `member_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `username` varchar(30) NOT NULL COMMENT '用户名',
    `password` varchar(100) DEFAULT '' COMMENT '密码',
    `nickname` varchar(30) DEFAULT '' COMMENT '用户昵称',
    `avatar` varchar(512) DEFAULT '' COMMENT '头像',
    `sex` tinyint DEFAULT '0' COMMENT '性别',
    `area_id` bigint DEFAULT NULL COMMENT '地区编号',
    `mobile` varchar(11) NOT NULL COMMENT '手机号',
    `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
    `birthday` date DEFAULT NULL COMMENT '出生日期',
    `mark` varchar(255) DEFAULT '' COMMENT '会员备注',
    `point` int NOT NULL DEFAULT '0' COMMENT '积分',
    `tag_ids` varchar(255) DEFAULT '' COMMENT '标签编号列表，以逗号分隔',
    `level_id` bigint DEFAULT NULL COMMENT '等级编号',
    `experience` int NOT NULL DEFAULT '0' COMMENT '经验',
    `gold` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '鱼币',
    `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
    `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_mobile` (`mobile`,`update_time`,`tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员用户';

-- 会员等级表
CREATE TABLE IF NOT EXISTS `member_level` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '等级名称',
    `experience` int NOT NULL COMMENT '升级经验',
    `level` int NOT NULL COMMENT '等级',
    `discount_percent` tinyint NOT NULL COMMENT '享受折扣',
    `icon` varchar(255) DEFAULT '' COMMENT '等级图标',
    `bg_color` varchar(20) DEFAULT '' COMMENT '等级背景色',
    `status` tinyint NOT NULL COMMENT '状态（字典值：0开启，1关闭）',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员等级';

-- =============================================
-- 支付模块
-- =============================================

-- 支付应用信息
CREATE TABLE IF NOT EXISTS `pay_app` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '应用编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(64) NOT NULL COMMENT '应用名',
    `status` tinyint NOT NULL COMMENT '开启状态',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `pay_notify_url` varchar(1024) NOT NULL COMMENT '支付结果的回调地址',
    `refund_notify_url` varchar(1024) NOT NULL COMMENT '退款结果的回调地址',
    `merchant_name` varchar(64) NOT NULL COMMENT '商户名称',
    `merchant_short_name` varchar(32) NOT NULL COMMENT '商户简称',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付应用信息';

-- 支付订单
CREATE TABLE IF NOT EXISTS `pay_order` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `app_id` bigint NOT NULL COMMENT '应用编号',
    `channel_id` bigint DEFAULT NULL COMMENT '渠道编号',
    `channel_code` varchar(32) DEFAULT NULL COMMENT '渠道编码',
    `merchant_order_id` varchar(64) NOT NULL COMMENT '商户订单编号',
    `subject` varchar(32) NOT NULL COMMENT '商品标题',
    `body` varchar(128) NOT NULL COMMENT '商品描述',
    `notify_url` varchar(1024) NOT NULL COMMENT '异步通知地址',
    `notify_status` tinyint NOT NULL DEFAULT '0' COMMENT '通知状态',
    `price` int NOT NULL COMMENT '支付金额，单位：分',
    `channel_fee_rate` double DEFAULT NULL COMMENT '渠道手续费，单位：百分比',
    `channel_fee_price` int DEFAULT NULL COMMENT '渠道手续费，单位：分',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '支付状态',
    `user_ip` varchar(50) DEFAULT NULL COMMENT '用户 IP',
    `expire_time` datetime DEFAULT NULL COMMENT '订单失效时间',
    `success_time` datetime DEFAULT NULL COMMENT '订单支付时间',
    `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
    `success_extension_id` bigint DEFAULT NULL COMMENT '支付成功的订单拓展单编号',
    `refund_status` tinyint NOT NULL DEFAULT '0' COMMENT '退款状态',
    `refund_times` tinyint NOT NULL DEFAULT '0' COMMENT '退款次数',
    `refund_price` int NOT NULL DEFAULT '0' COMMENT '退款总金额，单位：分',
    `channel_user_id` varchar(255) DEFAULT NULL COMMENT '渠道用户编号',
    `channel_order_no` varchar(64) DEFAULT NULL COMMENT '渠道订单号',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_app_id` (`app_id`),
    KEY `idx_merchant_order_id` (`merchant_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付订单';

-- 会员钱包
CREATE TABLE IF NOT EXISTS `pay_wallet` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `user_id` bigint NOT NULL COMMENT '用户编号',
    `user_type` tinyint NOT NULL DEFAULT '0' COMMENT '用户类型',
    `balance` int NOT NULL DEFAULT '0' COMMENT '余额，单位分',
    `total_expense` int NOT NULL DEFAULT '0' COMMENT '累计支出，单位分',
    `total_recharge` int NOT NULL DEFAULT '0' COMMENT '累计充值，单位分',
    `freeze_price` int NOT NULL DEFAULT '0' COMMENT '冻结金额，单位分',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user` (`user_id`,`user_type`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员钱包';

-- =============================================
-- 业务模块
-- =============================================

-- 文章表
CREATE TABLE IF NOT EXISTS `author_article` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `author_id` bigint NOT NULL COMMENT '作者编号',
    `title` varchar(255) NOT NULL COMMENT '标题',
    `intro` text COMMENT '简介',
    `pic_url` varchar(255) DEFAULT '' COMMENT '封面图片地址',
    `free_contents` longtext COMMENT '免费内容',
    `contents` longtext COMMENT '付费内容',
    `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
    `read_count` int NOT NULL DEFAULT '0' COMMENT '阅读次数',
    `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞次数',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_author_id` (`author_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';

-- 轮播图
CREATE TABLE IF NOT EXISTS `banner` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `title` varchar(64) NOT NULL COMMENT '标题',
    `pic_url` varchar(255) NOT NULL COMMENT '图片地址',
    `url` varchar(255) DEFAULT '' COMMENT '跳转地址',
    `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `memo` varchar(255) DEFAULT '' COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='轮播图';

-- =============================================
-- 微信模块
-- =============================================

-- 微信公众号账号
CREATE TABLE IF NOT EXISTS `mp_account` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(100) NOT NULL COMMENT '公众号名称',
    `account` varchar(100) NOT NULL COMMENT '公众号账号',
    `app_id` varchar(100) NOT NULL COMMENT '公众号appid',
    `app_secret` varchar(100) NOT NULL COMMENT '公众号密钥',
    `token` varchar(100) NOT NULL COMMENT '公众号token',
    `aes_key` varchar(100) DEFAULT NULL COMMENT '消息加解密密钥',
    `qr_code_url` varchar(255) DEFAULT NULL COMMENT '二维码图片URL',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `type` tinyint NOT NULL DEFAULT '0' COMMENT '公众号类型',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信公众号账号';

-- 微信用户
CREATE TABLE IF NOT EXISTS `mp_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `account_id` bigint NOT NULL COMMENT '微信账号编号',
    `app_id` varchar(128) NOT NULL COMMENT '微信AppID',
    `openid` varchar(100) NOT NULL COMMENT '微信openid',
    `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
    `head_image_url` varchar(1024) DEFAULT NULL COMMENT '头像地址',
    `language` varchar(30) DEFAULT NULL COMMENT '语言',
    `city` varchar(30) DEFAULT NULL COMMENT '城市',
    `province` varchar(30) DEFAULT NULL COMMENT '省份',
    `country` varchar(30) DEFAULT NULL COMMENT '国家',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `tag_ids` varchar(255) DEFAULT NULL COMMENT '标签编号数组',
    `subscribe_status` tinyint NOT NULL COMMENT '关注状态',
    `subscribe_time` datetime DEFAULT NULL COMMENT '关注时间',
    `unsubscribe_time` datetime DEFAULT NULL COMMENT '取消关注时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_app_openid` (`app_id`,`openid`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信用户';

-- 微信消息
CREATE TABLE IF NOT EXISTS `mp_message` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `account_id` bigint NOT NULL COMMENT '微信账号编号',
    `app_id` varchar(128) NOT NULL COMMENT '微信AppID',
    `openid` varchar(100) NOT NULL COMMENT '微信openid',
    `type` varchar(30) NOT NULL COMMENT '消息类型',
    `content` text COMMENT '消息内容',
    `send_from` tinyint NOT NULL COMMENT '消息来源',
    `send_time` datetime NOT NULL COMMENT '发送时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_account_id` (`account_id`),
    KEY `idx_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信消息';

-- 微信菜单
CREATE TABLE IF NOT EXISTS `mp_menu` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `account_id` bigint NOT NULL COMMENT '微信账号编号',
    `app_id` varchar(128) NOT NULL COMMENT '微信AppID',
    `name` varchar(100) NOT NULL COMMENT '菜单名称',
    `menu_key` varchar(200) DEFAULT NULL COMMENT '菜单标识',
    `parent_id` bigint DEFAULT NULL COMMENT '父菜单编号',
    `type` varchar(20) NOT NULL COMMENT '菜单类型',
    `url` varchar(500) DEFAULT NULL COMMENT '网页链接',
    `mini_program_app_id` varchar(100) DEFAULT NULL COMMENT '小程序appid',
    `mini_program_page_path` varchar(200) DEFAULT NULL COMMENT '小程序页面路径',
    `article_id` varchar(200) DEFAULT NULL COMMENT '图文消息的媒体编号',
    `reply_message_type` varchar(30) DEFAULT NULL COMMENT '回复消息类型',
    `reply_content` varchar(1024) DEFAULT NULL COMMENT '回复消息内容',
    `reply_media_id` varchar(200) DEFAULT NULL COMMENT '回复媒体文件编号',
    `reply_media_url` varchar(1024) DEFAULT NULL COMMENT '回复媒体文件地址',
    `reply_title` varchar(100) DEFAULT NULL COMMENT '回复标题',
    `reply_description` varchar(256) DEFAULT NULL COMMENT '回复描述',
    `reply_thumb_media_id` varchar(200) DEFAULT NULL COMMENT '回复缩略图媒体编号',
    `reply_articles` varchar(1024) DEFAULT NULL COMMENT '回复图文消息',
    `reply_music_url` varchar(1024) DEFAULT NULL COMMENT '回复音乐链接',
    `reply_hq_music_url` varchar(1024) DEFAULT NULL COMMENT '回复高质量音乐链接',
    `reply_thumb_media_url` varchar(1024) DEFAULT NULL COMMENT '回复缩略图链接',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信菜单';

-- 微信模板配置
CREATE TABLE IF NOT EXISTS `mp_template_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `appid` varchar(100) NOT NULL COMMENT '微信AppID',
    `template_type` varchar(50) NOT NULL COMMENT '模板业务类型',
    `template_id` varchar(100) NOT NULL COMMENT '模板ID',
    `template_filed` text COMMENT '模板字段配置',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_appid` (`appid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信模板配置';

-- 微信标签
CREATE TABLE IF NOT EXISTS `mp_tag` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `account_id` bigint NOT NULL COMMENT '微信账号编号',
    `tag_id` bigint NOT NULL COMMENT '标签编号',
    `name` varchar(100) NOT NULL COMMENT '标签名称',
    `count` int NOT NULL DEFAULT '0' COMMENT '粉丝数量',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信标签';

SET FOREIGN_KEY_CHECKS = 1;
