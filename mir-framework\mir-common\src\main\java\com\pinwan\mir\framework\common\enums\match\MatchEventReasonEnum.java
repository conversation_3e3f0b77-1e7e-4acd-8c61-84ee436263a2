package com.pinwan.mir.framework.common.enums.match;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MatchEventReasonEnum {

    /**
     * 事件原因
     */
    FOUL(1, "犯规"),
    PERSONAL_FOUL(2, "个人犯规"),
    INFRINGEMENT_OR_SUBSTITUTION(3, "侵犯对手(罚牌事件)/受伤换人(换人事件)"),
    TACTICAL_FOUL_OR_SUBSTITUTION(4, "战术犯规(罚牌事件)/战术换人(换人事件)"),
    ATTACKING_FOUL(5, "进攻犯规"),
    OFF_THE_BALL_FOUL(6, "无球犯规"),
    CONTINUOUS_FOUL(7, "持续犯规"),
    CONTINUOUS_INFRINGEMENT(8, "持续侵犯"),
    VIOLENT_BEHAVIOR(9, "暴力行为"),
    DANGEROUS_ACTION(10, "危险动作"),
    HANDBALL(11, "手球犯规"),
    SERIOUS_FOUL(12, "严重犯规"),
    INTENTIONAL_FOUL_LAST_DEFENDER(13, "故意犯规（防守球员为最后一名防守人时）"),
    BLOCKING_GOAL_CHANCE(14, "阻挡进球机会"),
    TIME_WASTING(15, "拖延时间"),
    VIDEO_REVIEW_DECISION(16, "视频回看裁定"),
    PENALTY_CANCELLATION(17, "判罚取消"),
    ARGUMENT(18, "争论"),
    OBJECTION_TO_PENALTY(19, "对判罚表达异议"),
    FOUL_AND_ABUSIVE_LANGUAGE(20, "犯规和攻击言语"),
    EXCESSIVE_CELEBRATION(21, "过度庆祝"),
    NOT_RETREATING_TO_REQUIRE_DISTANCE(22, "没有回退到要求的距离"),
    FIGHTING(23, "打架"),
    ASSISTANT_REFEREE_DECISION(24, "辅助判罚"),
    BENCH_REASON(25, "替补席"),
    POST_MATCH_BEHAVIOR(26, "赛后行为"),
    OTHER_REASONS(27, "其他原因"),
    NOT_ALLOWED_ON_FIELD(28, "未被允许进入场地"),
    ENTERING_FIELD(29, "进入比赛场地"),
    LEAVING_FIELD(30, "离开比赛赛场"),
    UNSPORTSMANLIKE_BEHAVIOR(31, "非体育道德行为"),
    UNINTENDED_MALICIOUS_FOUL(32, "非主观意愿的恶意犯规"),
    DIVING(33, "假摔"),
    INTERFERING_WITH_VAR_REVIEW(34, "干预VAR复审"),
    ENTERING_REFEREE_REVIEW_ZONE(35, "进入裁判评审区"),
    SPITTING(36, "吐口水（向球员或裁判）"),
    VIRUS(37, "病毒"),
    UNKNOWN(0, "未知");

    private Integer value;

    private String desc;

}
