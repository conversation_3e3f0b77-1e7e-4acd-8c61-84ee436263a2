# 足球彩票系统数据库设计文档

## 📊 概览

本文档基于实际数据库结构生成，详细描述了足球彩票系统的数据库设计。

### 基本信息
- **数据库名称**: mir
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB
- **架构模式**: 多租户SaaS架构

## 🏗️ 模块化设计

系统采用模块化设计，按业务功能划分表结构：

### 1. 会员模块 (member_*)

**核心功能**: 用户管理、等级体系、积分系统、签到管理

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `member_user` | 会员用户表 | id, tenant_id, username, mobile, nickname, gold, balance |
| `member_level` | 会员等级表 | id, tenant_id, name, level, discount_percent |
| `member_level_record` | 等级记录表 | id, tenant_id, user_id, level_id, reason |
| `member_point_record` | 积分记录表 | id, tenant_id, user_id, point, description |
| `member_sign_in_record` | 签到记录表 | id, tenant_id, user_id, day, point |
| `member_group` | 用户分组表 | id, tenant_id, name, remark, status |

### 2. 支付模块 (pay_*)

**核心功能**: 支付处理、钱包管理、退款处理

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `pay_app` | 支付应用表 | id, tenant_id, name, pay_notify_url, refund_notify_url |
| `pay_order` | 支付订单表 | id, tenant_id, app_id, merchant_order_id, price, status |
| `pay_refund` | 退款订单表 | id, tenant_id, order_id, refund_price, status |
| `pay_wallet` | 用户钱包表 | id, tenant_id, user_id, balance, total_expense |
| `pay_wallet_transaction` | 钱包交易表 | id, tenant_id, wallet_id, price, balance, title |

### 3. 业务模块 (author_*, match_*, etc.)

**核心功能**: 文章管理、球队信息、营销活动

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `author_article` | 文章表 | id, tenant_id, author_id, title, price, status |
| `author_article_append` | 文章追加表 | id, tenant_id, article_id, content |
| `match_team` | 球队信息表 | id, tenant_id, name, logo, description |
| `banner` | 轮播图表 | id, tenant_id, title, pic_url, url, sort |
| `gold_order` | 鱼币充值表 | id, tenant_id, user_id, gold_num, pay_price |

### 4. 系统模块 (system_*)

**核心功能**: 用户权限、租户管理、组织架构

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `system_users` | 管理员用户表 | id, tenant_id, username, nickname, status |
| `system_role` | 角色表 | id, tenant_id, name, code, type |
| `system_menu` | 菜单权限表 | id, name, permission, type, sort |
| `system_tenant` | 租户表 | id, name, contact_name, status, expire_time |
| `system_dept` | 部门表 | id, tenant_id, name, parent_id, sort |

### 5. 基础设施模块 (infra_*)

**核心功能**: 文件管理、代码生成、系统配置

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `infra_file` | 文件表 | id, tenant_id, config_id, name, path, url |
| `infra_file_config` | 文件配置表 | id, tenant_id, name, storage, remark |
| `infra_codegen_table` | 代码生成表 | id, table_name, table_comment, module_name |
| `infra_codegen_column` | 生成字段表 | id, table_id, column_name, data_type |

### 6. 微信模块 (mp_*)

**核心功能**: 微信公众号、用户管理、消息处理

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `mp_account` | 微信账号表 | id, tenant_id, name, account, app_id, app_secret |
| `mp_message` | 微信消息表 | id, tenant_id, account_id, type, content |
| `mp_user` | 微信用户表 | id, tenant_id, account_id, openid, nickname |
| `mp_tag` | 微信标签表 | id, tenant_id, account_id, name, count |
| `mp_menu` | 微信菜单表 | id, tenant_id, account_id, name, type |

## 🔗 表关系设计

### 核心关系图

```
system_tenant (租户)
    ├── member_user (会员用户)
    │   ├── pay_wallet (钱包) [1:1]
    │   ├── pay_order (支付订单) [1:N]
    │   ├── author_article (文章) [1:N]
    │   └── member_level_record (等级记录) [1:N]
    ├── pay_app (支付应用)
    │   └── pay_order (支付订单) [1:N]
    └── author_article (文章)
        └── author_article_append (文章追加) [1:N]
```

### 主要外键关系

| 主表 | 外键表 | 关系字段 | 关系类型 |
|------|--------|----------|----------|
| system_tenant | member_user | tenant_id | 1:N |
| system_tenant | pay_order | tenant_id | 1:N |
| member_user | pay_wallet | user_id | 1:1 |
| member_user | pay_order | user_id | 1:N |
| pay_app | pay_order | app_id | 1:N |
| author_article | author_article_append | article_id | 1:N |

## 🏢 多租户设计

### 隔离策略
- **架构模式**: 共享数据库、共享Schema、行级隔离
- **租户标识**: tenant_id字段
- **数据隔离**: 基于tenant_id的行级过滤
- **权限控制**: 应用层租户上下文控制

### 租户字段覆盖

**需要租户隔离的表** (包含tenant_id):
- ✅ 所有 `member_*` 表
- ✅ 所有 `pay_*` 表
- ✅ 所有 `author_*` 表
- ✅ 所有 `mp_*` 表
- ✅ 部分 `system_*` 表 (users, dept等)
- ✅ 部分 `infra_*` 表 (file, codegen等)

**不需要租户隔离的表**:
- 🔒 `system_tenant` - 租户主表
- 🔒 `system_menu` - 系统菜单
- 🔒 `system_dict_*` - 数据字典
- 📊 `infra_api_*` - API日志
- 📊 `infra_job_*` - 任务日志

## 📋 字段标准化

### 基础字段规范

所有业务表都包含以下标准字段：

```sql
-- 主键
id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID'

-- 多租户 (业务表)
tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号'

-- 审计字段
creator VARCHAR(64) DEFAULT '' COMMENT '创建者'
create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
updater VARCHAR(64) DEFAULT '' COMMENT '更新者'
update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

-- 软删除
deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除'
```

### 命名规范

- **表名**: `模块前缀_业务名称` (如: `member_user`)
- **字段名**: 下划线命名法 (如: `create_time`)
- **索引名**: `idx_表名简写_字段名` (如: `idx_member_user_mobile`)
- **约束名**: `fk_表名_字段名` (如: `fk_pay_order_user_id`)

## 🚀 性能优化

### 索引策略

1. **主键索引**: 所有表自增主键
2. **租户索引**: 多租户表的 `tenant_id` 索引
3. **业务索引**: 常用查询字段的复合索引
4. **唯一索引**: 业务唯一性约束

### 关键索引示例

```sql
-- 会员用户表
CREATE INDEX idx_member_user_tenant_id ON member_user(tenant_id);
CREATE INDEX idx_member_user_tenant_mobile ON member_user(tenant_id, mobile);
CREATE UNIQUE INDEX uk_member_user_mobile ON member_user(mobile);

-- 支付订单表
CREATE INDEX idx_pay_order_tenant_id ON pay_order(tenant_id);
CREATE INDEX idx_pay_order_tenant_status ON pay_order(tenant_id, status);
CREATE INDEX idx_pay_order_tenant_create_time ON pay_order(tenant_id, create_time);
```

## 🔧 数据库特性

### 支持的数据库
- ✅ MySQL 5.7+ (主要支持)
- ✅ PostgreSQL 10+
- ✅ Oracle 11g+
- ✅ SQL Server 2012+

### 特殊配置
- **字符集**: utf8mb4 (支持emoji)
- **时区**: 使用应用服务器时区
- **连接池**: HikariCP
- **ORM框架**: MyBatis Plus

## 📈 扩展性设计

### 水平扩展
- 支持读写分离
- 支持按租户分库分表
- 租户数据可独立迁移

### 垂直扩展
- 模块化设计便于微服务拆分
- 表结构支持字段扩展
- 预留业务扩展空间

---

**文档版本**: v1.0  
**更新时间**: 2025-01-10  
**维护说明**: 本文档基于实际数据库结构生成，如有变更请及时更新
