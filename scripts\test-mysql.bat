@echo off
echo Testing MySQL connection and queries...
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=sports_gaming
set DB_USER=root
set DB_PASSWORD=123456

echo Database: %DB_NAME%
echo Host: %DB_HOST%:%DB_PORT%
echo User: %DB_USER%
echo.

echo === Test 1: Basic Connection ===
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" 2>nul
if %errorlevel% equ 0 (
    echo ✓ Connection successful
) else (
    echo ✗ Connection failed
    exit /b 1
)

echo.
echo === Test 2: Show Tables ===
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SHOW TABLES;" --batch --skip-column-names 2>nul > temp_tables.txt
if %errorlevel% equ 0 (
    for /f %%i in ('type temp_tables.txt ^| find /c /v ""') do set table_count=%%i
    echo ✓ Found !table_count! tables
    echo First 5 tables:
    for /f "tokens=* skip=0" %%a in (temp_tables.txt) do (
        echo   - %%a
        set /a counter+=1
        if !counter! geq 5 goto :next
    )
    :next
) else (
    echo ✗ Failed to get tables
    exit /b 1
)

echo.
echo === Test 3: Table Structure ===
for /f "tokens=* skip=0" %%a in (temp_tables.txt) do (
    set first_table=%%a
    goto :got_first_table
)
:got_first_table

echo Testing table: %first_table%
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "DESCRIBE %first_table%;" 2>nul
if %errorlevel% equ 0 (
    echo ✓ DESCRIBE query successful
) else (
    echo ✗ DESCRIBE query failed
)

echo.
echo === Test 4: INFORMATION_SCHEMA Query ===
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, COLUMN_KEY, COLUMN_DEFAULT, COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '%DB_NAME%' AND TABLE_NAME = '%first_table%' ORDER BY ORDINAL_POSITION;" --batch --skip-column-names 2>nul > temp_columns.txt
if %errorlevel% equ 0 (
    for /f %%i in ('type temp_columns.txt ^| find /c /v ""') do set column_count=%%i
    echo ✓ Found !column_count! columns
    echo First 3 columns:
    set counter=0
    for /f "tokens=1,2,* delims=	" %%a in (temp_columns.txt) do (
        echo   - Field: %%a, Type: %%b
        set /a counter+=1
        if !counter! geq 3 goto :done_columns
    )
    :done_columns
) else (
    echo ✗ INFORMATION_SCHEMA query failed
)

echo.
echo === Cleanup ===
del temp_tables.txt 2>nul
del temp_columns.txt 2>nul

echo.
echo === Test Complete ===
