package com.pinwan.mir.framework.common.enums.match;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MatchScoreEnum {

    REGULAR_SCORE(0, "比分（常规时间）"),
    HALF_TIME_SCORE(1, "半场比分"),
    RED_CARDS(2, "红牌"),
    YELLOW_CARDS(3, "黄牌"),
    CORNER_KICKS(4, "角球"),
    EXTRA_TIME_SCORE(5, "加时比分"),
    PENALTY_SHOOTOUT_SCORE(6, "点球大战比分");

    private Integer index;

    private String desc;

}
