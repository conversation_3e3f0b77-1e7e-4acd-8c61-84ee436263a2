# SaaS多租户改造脚本使用说明

## 🚨 问题解决

### 遇到 "Duplicate column name 'tenant_id'" 错误

这个错误表示您之前已经部分执行过改造脚本，某些表已经添加了`tenant_id`字段。我们提供了几种解决方案：

## 🔧 解决方案

### 方案一：使用快速修复脚本（推荐）

```bash
# 1. 给脚本执行权限
chmod +x scripts/quick-fix.sh

# 2. 运行快速修复脚本
./scripts/quick-fix.sh
```

这个脚本会：
- ✅ 自动检查当前改造状态
- ✅ 只添加缺失的`tenant_id`字段
- ✅ 跳过已存在的字段
- ✅ 创建必要的索引
- ✅ 提供详细的状态报告

### 方案二：使用更新后的SQL脚本

我们已经更新了SQL脚本，现在支持重复执行：

```bash
# 1. 检查当前状态
mysql -u root -p mir < sql/tenant-migration/00-check-and-fix.sql

# 2. 重新执行字段添加（现在支持重复执行）
mysql -u root -p mir < sql/tenant-migration/01-add-tenant-fields.sql

# 3. 创建索引
mysql -u root -p mir < sql/tenant-migration/02-create-indexes.sql

# 4. 迁移数据
mysql -u root -p mir < sql/tenant-migration/03-migrate-data.sql
```

### 方案三：使用完整的自动化脚本

```bash
# 1. 配置数据库连接
cp config/database.conf.example config/database.conf
vim config/database.conf

# 2. 运行完整的改造脚本（现在支持断点续传）
chmod +x scripts/saas-migration.sh
./scripts/saas-migration.sh
```

## 📋 脚本功能对比

| 脚本 | 用途 | 特点 | 适用场景 |
|------|------|------|----------|
| `quick-fix.sh` | 快速修复 | 轻量、快速、安全 | 解决部分执行失败的问题 |
| `00-check-and-fix.sql` | 状态检查 | 详细的状态报告 | 了解当前改造进度 |
| `01-add-tenant-fields.sql` | 添加字段 | 支持重复执行 | 添加租户字段 |
| `saas-migration.sh` | 完整改造 | 全自动化 | 完整的改造流程 |

## 🔍 检查改造状态

### 检查哪些表已添加tenant_id字段

```sql
SELECT 
    TABLE_NAME as '表名',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY 
    TABLE_NAME;
```

### 检查数据迁移状态

```sql
-- 检查各表中tenant_id为0的记录数量
SELECT 'member_user' as table_name, COUNT(*) as zero_tenant_count 
FROM member_user WHERE tenant_id = 0
UNION ALL
SELECT 'pay_order' as table_name, COUNT(*) as zero_tenant_count 
FROM pay_order WHERE tenant_id = 0
UNION ALL
SELECT 'article' as table_name, COUNT(*) as zero_tenant_count 
FROM article WHERE tenant_id = 0;
```

## 🛠️ 手动修复步骤

如果您更喜欢手动操作，可以按以下步骤进行：

### 1. 检查哪些表缺少tenant_id字段

```sql
-- 查看需要添加但还没有tenant_id字段的表
SELECT t.table_name
FROM (
    SELECT 'member_user' as table_name
    UNION SELECT 'member_level'
    UNION SELECT 'pay_app'
    UNION SELECT 'pay_order'
    UNION SELECT 'pay_wallet'
    UNION SELECT 'article'
) t
LEFT JOIN INFORMATION_SCHEMA.COLUMNS c 
    ON c.TABLE_NAME = t.table_name 
    AND c.TABLE_SCHEMA = DATABASE() 
    AND c.COLUMN_NAME = 'tenant_id'
WHERE c.COLUMN_NAME IS NULL;
```

### 2. 手动添加缺失的字段

```sql
-- 只对缺少字段的表执行
-- 例如，如果member_user表缺少tenant_id字段：
ALTER TABLE member_user 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 对其他缺少的表重复此操作
```

### 3. 创建索引

```sql
-- 为已添加tenant_id字段的表创建索引
CREATE INDEX idx_member_user_tenant_id ON member_user(tenant_id);
CREATE INDEX idx_pay_order_tenant_id ON pay_order(tenant_id);
-- ... 其他表的索引
```

## ⚠️ 注意事项

### 执行前的准备

1. **备份数据库**
   ```bash
   mysqldump -u root -p mir > backup_$(date +%Y%m%d).sql
   ```

2. **在测试环境先验证**
   - 建议先在测试环境执行完整流程
   - 确认无误后再在生产环境执行

3. **业务低峰期执行**
   - 索引创建可能需要较长时间
   - 建议在业务低峰期执行

### 常见问题

**Q: 执行脚本时提示权限不足**
```bash
# 给脚本执行权限
chmod +x scripts/quick-fix.sh
chmod +x scripts/saas-migration.sh
```

**Q: 数据库连接失败**
```bash
# 检查数据库服务状态
systemctl status mysql

# 测试连接
mysql -h localhost -u root -p
```

**Q: 某些表不存在**
- 这是正常的，脚本会自动跳过不存在的表
- 只会处理实际存在的表

**Q: 如何回滚改造**
```bash
# 使用回滚脚本
./scripts/saas-rollback.sh
```

## 📈 执行后验证

### 1. 验证字段添加

```sql
-- 应该看到所有业务表都有tenant_id字段
SELECT COUNT(*) as '已添加tenant_id字段的表数量'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'tenant_id';
```

### 2. 验证索引创建

```sql
-- 查看tenant_id相关的索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as columns
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() AND INDEX_NAME LIKE '%tenant%'
GROUP BY TABLE_NAME, INDEX_NAME;
```

### 3. 验证数据迁移

```sql
-- 检查是否还有tenant_id为0的数据
SELECT 
    'member_user' as table_name, 
    COUNT(*) as zero_tenant_records 
FROM member_user WHERE tenant_id = 0
HAVING zero_tenant_records > 0;
```

## 🎯 下一步操作

改造完成后：

1. **启用多租户配置**
   ```yaml
   # application.yaml
   mir:
     tenant:
       enable: true
   ```

2. **重启应用服务**
   ```bash
   # 重启Spring Boot应用
   ```

3. **测试多租户功能**
   - 创建测试租户
   - 验证数据隔离
   - 测试权限控制

4. **前端适配**
   - 更新前端租户选择功能
   - 配置租户上下文传递

---

**如果遇到其他问题，请查看日志文件或联系技术支持。**
