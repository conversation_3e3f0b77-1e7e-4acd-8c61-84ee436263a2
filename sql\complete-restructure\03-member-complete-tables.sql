-- =============================================
-- 足球彩票系统 - 会员功能完整表
-- 包含所有member_*相关的会员功能表
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 会员核心表
-- =============================================

-- 会员用户表
CREATE TABLE IF NOT EXISTS `member_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `username` varchar(30) NOT NULL COMMENT '用户名',
    `password` varchar(100) DEFAULT '' COMMENT '密码',
    `nickname` varchar(30) DEFAULT '' COMMENT '用户昵称',
    `avatar` varchar(512) DEFAULT '' COMMENT '头像',
    `sex` tinyint DEFAULT '0' COMMENT '性别',
    `area_id` bigint DEFAULT NULL COMMENT '地区编号',
    `mobile` varchar(11) NOT NULL COMMENT '手机号',
    `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
    `birthday` date DEFAULT NULL COMMENT '出生日期',
    `mark` varchar(255) DEFAULT '' COMMENT '会员备注',
    `point` int NOT NULL DEFAULT '0' COMMENT '积分',
    `tag_ids` varchar(255) DEFAULT '' COMMENT '标签编号列表，以逗号分隔',
    `level_id` bigint DEFAULT NULL COMMENT '等级编号',
    `experience` int NOT NULL DEFAULT '0' COMMENT '经验',
    `gold` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '鱼币',
    `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
    `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_mobile` (`mobile`,`update_time`,`tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员用户';

-- 会员等级表
CREATE TABLE IF NOT EXISTS `member_level` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '等级名称',
    `experience` int NOT NULL COMMENT '升级经验',
    `level` int NOT NULL COMMENT '等级',
    `discount_percent` tinyint NOT NULL COMMENT '享受折扣',
    `icon` varchar(255) DEFAULT '' COMMENT '等级图标',
    `bg_color` varchar(20) DEFAULT '' COMMENT '等级背景色',
    `status` tinyint NOT NULL COMMENT '状态（字典值：0开启，1关闭）',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员等级';

-- 会员等级记录表
CREATE TABLE IF NOT EXISTS `member_level_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `user_id` bigint NOT NULL COMMENT '用户编号',
    `level_id` bigint NOT NULL COMMENT '等级编号',
    `level` int NOT NULL COMMENT '会员等级',
    `experience` int NOT NULL COMMENT '升级经验',
    `user_experience` int NOT NULL COMMENT '会员经验',
    `reason` varchar(255) NOT NULL COMMENT '原因',
    `description` varchar(255) NOT NULL COMMENT '描述',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员等级记录';

-- 会员分组表
CREATE TABLE IF NOT EXISTS `member_group` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '分组名称',
    `remark` varchar(255) DEFAULT '' COMMENT '备注',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户分组';

-- 会员标签表
CREATE TABLE IF NOT EXISTS `member_tag` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '标签名称',
    `color` varchar(20) DEFAULT '' COMMENT '标签颜色',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员标签';

-- =============================================
-- 会员积分系统
-- =============================================

-- 会员积分记录表
CREATE TABLE IF NOT EXISTS `member_point_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `user_id` bigint NOT NULL COMMENT '用户编号',
    `biz_id` varchar(255) NOT NULL COMMENT '业务编码',
    `biz_type` tinyint NOT NULL COMMENT '业务类型',
    `title` varchar(255) NOT NULL COMMENT '积分标题',
    `description` varchar(512) DEFAULT '' COMMENT '积分描述',
    `point` int NOT NULL COMMENT '积分',
    `total_point` int NOT NULL COMMENT '变更后的积分',
    `status` tinyint NOT NULL COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_biz_id` (`biz_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分记录';

-- 会员积分配置表
CREATE TABLE IF NOT EXISTS `member_point_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `type` tinyint NOT NULL COMMENT '积分类型',
    `title` varchar(127) NOT NULL COMMENT '积分标题',
    `description` varchar(255) DEFAULT '' COMMENT '积分描述',
    `point` int NOT NULL COMMENT '积分',
    `status` tinyint NOT NULL COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员积分配置';

-- =============================================
-- 会员签到系统
-- =============================================

-- 会员签到记录表
CREATE TABLE IF NOT EXISTS `member_sign_in_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `user_id` bigint NOT NULL COMMENT '用户编号',
    `day` int NOT NULL COMMENT '第几天签到',
    `point` int NOT NULL COMMENT '签到的积分',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户签到记录';

-- 会员签到配置表
CREATE TABLE IF NOT EXISTS `member_sign_in_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `day` int NOT NULL COMMENT '第几天',
    `point` int NOT NULL COMMENT '奖励积分',
    `status` tinyint NOT NULL COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='签到规则';

-- =============================================
-- 会员经验系统
-- =============================================

-- 会员经验记录表
CREATE TABLE IF NOT EXISTS `member_experience_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `user_id` bigint NOT NULL COMMENT '用户编号',
    `biz_id` varchar(255) NOT NULL COMMENT '业务编码',
    `biz_type` tinyint NOT NULL COMMENT '业务类型',
    `title` varchar(255) NOT NULL COMMENT '经验标题',
    `description` varchar(512) DEFAULT '' COMMENT '经验描述',
    `experience` int NOT NULL COMMENT '经验',
    `total_experience` int NOT NULL COMMENT '变更后的经验',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员经验记录';

-- =============================================
-- 会员地址管理
-- =============================================

-- 用户收件地址表
CREATE TABLE IF NOT EXISTS `member_address` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '收件地址编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `user_id` bigint NOT NULL COMMENT '用户编号',
    `name` varchar(10) NOT NULL COMMENT '收件人名称',
    `mobile` varchar(20) NOT NULL COMMENT '手机号',
    `area_id` bigint NOT NULL COMMENT '地区编码',
    `detail_address` varchar(250) NOT NULL COMMENT '收件详细地址',
    `default_status` bit(1) NOT NULL COMMENT '是否默认',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收件地址';

SET FOREIGN_KEY_CHECKS = 1;
