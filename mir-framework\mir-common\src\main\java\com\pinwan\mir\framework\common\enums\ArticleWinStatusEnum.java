package com.pinwan.mir.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ArticleWinStatusEnum {
    NO_END(0, "未结束", false),
    RED(1, "红", true),
    BLACK(2, "黑", false),
    FLOW(3, "走水", false),
    TWO_IN_ONE(4, "二中一",true),
    THREE_IN_TWO(5, "三中二",true),
    FOUR_IN_THREE(6, "四中三",true),
    KILLED (7, "被绝杀",false),
    ;


    private final Integer status;
    private final String name;
    private final Boolean result;

    // 根据状态码获取枚举对象
    public static Boolean getResultByStatus(Integer status) {
        for (ArticleWinStatusEnum e : ArticleWinStatusEnum.values()) {
            if (e.getStatus().equals(status)) {
                return e.getResult();
            }
        }
        return false;
    }

}
