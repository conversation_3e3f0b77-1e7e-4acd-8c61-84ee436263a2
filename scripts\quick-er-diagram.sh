#!/bin/bash

# =============================================
# 快速生成ER图脚本
# 连接数据库，快速生成Mermaid ER图
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "========================================"
echo "快速ER图生成工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database

log_info "获取数据库表结构..."

# 获取所有表和字段信息
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF' > /tmp/db_structure.txt
SELECT 
    CONCAT(
        'TABLE:', TABLE_NAME, '|',
        IFNULL(TABLE_COMMENT, ''), '|',
        COLUMN_NAME, '|',
        DATA_TYPE, '|',
        IFNULL(COLUMN_COMMENT, ''), '|',
        COLUMN_KEY, '|',
        IS_NULLABLE
    ) as info
FROM 
    INFORMATION_SCHEMA.TABLES t
    JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME AND t.TABLE_SCHEMA = c.TABLE_SCHEMA
WHERE 
    t.TABLE_SCHEMA = DATABASE()
    AND t.TABLE_TYPE = 'BASE TABLE'
ORDER BY 
    t.TABLE_NAME, c.ORDINAL_POSITION;
EOF

# 获取外键关系
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF' > /tmp/foreign_keys.txt
SELECT 
    CONCAT('FK:', TABLE_NAME, '|', COLUMN_NAME, '|', REFERENCED_TABLE_NAME, '|', REFERENCED_COLUMN_NAME) as fk_info
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND REFERENCED_TABLE_NAME IS NOT NULL;
EOF

log_info "生成Mermaid ER图..."

# 生成Mermaid ER图
cat > docs/database/er-diagram.mmd << 'EOF'
erDiagram
    %% 足球彩票系统数据库ER图
    %% 基于实际数据库结构生成
    
EOF

# 处理表结构
current_table=""
while IFS='|' read -r prefix table_name table_comment column_name data_type column_comment column_key is_nullable; do
    if [ "$prefix" = "TABLE" ] && [ "$table_name" != "$current_table" ]; then
        if [ -n "$current_table" ]; then
            echo "    }" >> docs/database/er-diagram.mmd
            echo "" >> docs/database/er-diagram.mmd
        fi
        
        current_table="$table_name"
        comment_text=""
        if [ -n "$table_comment" ]; then
            comment_text=" : $table_comment"
        fi
        
        echo "    $table_name {" >> docs/database/er-diagram.mmd
    fi
    
    if [ "$prefix" = "TABLE" ] && [ "$table_name" = "$current_table" ]; then
        # 确定键类型
        key_indicator=""
        case "$column_key" in
            "PRI") key_indicator=" PK" ;;
            "UNI") key_indicator=" UK" ;;
            "MUL") key_indicator=" FK" ;;
        esac
        
        # 格式化注释
        comment_part=""
        if [ -n "$column_comment" ]; then
            comment_part=" \"$column_comment\""
        fi
        
        echo "        $data_type $column_name$key_indicator$comment_part" >> docs/database/er-diagram.mmd
    fi
done < /tmp/db_structure.txt

# 关闭最后一个表
if [ -n "$current_table" ]; then
    echo "    }" >> docs/database/er-diagram.mmd
    echo "" >> docs/database/er-diagram.mmd
fi

# 添加关系
echo "    %% 表关系" >> docs/database/er-diagram.mmd
while IFS='|' read -r prefix table_name column_name ref_table ref_column; do
    if [ "$prefix" = "FK" ]; then
        echo "    $ref_table ||--o{ $table_name : \"$column_name\"" >> docs/database/er-diagram.mmd
    fi
done < /tmp/foreign_keys.txt

log_success "ER图已生成: docs/database/er-diagram.mmd"

# 生成简化版本（只包含主要业务表）
log_info "生成简化版ER图..."

cat > docs/database/er-diagram-simple.mmd << 'EOF'
erDiagram
    %% 足球彩票系统核心业务表ER图
    
    %% 会员模块
    member_user {
        bigint id PK "用户ID"
        bigint tenant_id "租户ID"
        varchar username "用户名"
        varchar mobile "手机号"
        varchar nickname "昵称"
        decimal gold "鱼币"
        decimal balance "余额"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
    member_level {
        bigint id PK "等级ID"
        bigint tenant_id "租户ID"
        varchar name "等级名称"
        int level "等级值"
        decimal discount_percent "折扣比例"
        tinyint status "状态"
    }
    
    %% 支付模块
    pay_order {
        bigint id PK "订单ID"
        bigint tenant_id "租户ID"
        bigint app_id "应用ID"
        varchar merchant_order_id "商户订单号"
        varchar subject "订单标题"
        int price "支付金额(分)"
        tinyint status "订单状态"
        datetime success_time "支付成功时间"
    }
    
    pay_wallet {
        bigint id PK "钱包ID"
        bigint tenant_id "租户ID"
        bigint user_id "用户ID"
        varchar user_type "用户类型"
        int balance "余额(分)"
        int total_expense "总支出"
        int total_recharge "总充值"
        datetime create_time "创建时间"
    }
    
    %% 业务模块
    author_article {
        bigint id PK "文章ID"
        bigint tenant_id "租户ID"
        bigint author_id "作者ID"
        varchar title "标题"
        text intro "简介"
        text free_contents "免费内容"
        text contents "付费内容"
        decimal price "价格"
        tinyint status "状态"
        datetime start_time "开始时间"
    }
    
    %% 系统模块
    system_tenant {
        bigint id PK "租户ID"
        varchar name "租户名称"
        varchar contact_name "联系人"
        varchar contact_mobile "联系电话"
        tinyint status "状态"
        datetime expire_time "过期时间"
    }
    
    %% 关系定义
    system_tenant ||--o{ member_user : "tenant_id"
    system_tenant ||--o{ pay_order : "tenant_id"
    system_tenant ||--o{ pay_wallet : "tenant_id"
    system_tenant ||--o{ author_article : "tenant_id"
    
    member_user ||--|| pay_wallet : "user_id"
    member_user ||--o{ pay_order : "user_id"
    member_user ||--o{ author_article : "author_id"
    member_user }o--|| member_level : "level_id"
EOF

log_success "简化版ER图已生成: docs/database/er-diagram-simple.mmd"

# 生成表统计信息
log_info "生成表统计信息..."

mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF' > docs/database/table-statistics.txt
-- 表统计信息
SELECT '=== 数据库表统计 ===' as section;

SELECT 
    CASE 
        WHEN TABLE_NAME LIKE 'member_%' THEN '会员模块'
        WHEN TABLE_NAME LIKE 'pay_%' THEN '支付模块'
        WHEN TABLE_NAME LIKE 'system_%' THEN '系统模块'
        WHEN TABLE_NAME LIKE 'infra_%' THEN '基础设施模块'
        WHEN TABLE_NAME LIKE 'mp_%' THEN '微信模块'
        WHEN TABLE_NAME LIKE 'author_%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'match_%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'banner%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'gold_%' THEN '业务模块'
        ELSE '其他模块'
    END as '模块',
    COUNT(*) as '表数量'
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_TYPE = 'BASE TABLE'
GROUP BY 
    CASE 
        WHEN TABLE_NAME LIKE 'member_%' THEN '会员模块'
        WHEN TABLE_NAME LIKE 'pay_%' THEN '支付模块'
        WHEN TABLE_NAME LIKE 'system_%' THEN '系统模块'
        WHEN TABLE_NAME LIKE 'infra_%' THEN '基础设施模块'
        WHEN TABLE_NAME LIKE 'mp_%' THEN '微信模块'
        WHEN TABLE_NAME LIKE 'author_%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'match_%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'banner%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'gold_%' THEN '业务模块'
        ELSE '其他模块'
    END
ORDER BY 
    COUNT(*) DESC;

SELECT '=== 多租户字段覆盖情况 ===' as section;

SELECT 
    COUNT(*) as '总表数',
    COUNT(CASE WHEN c.COLUMN_NAME IS NOT NULL THEN 1 END) as '已添加tenant_id的表数',
    ROUND(COUNT(CASE WHEN c.COLUMN_NAME IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as '覆盖率(%)'
FROM 
    INFORMATION_SCHEMA.TABLES t
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS c 
        ON t.TABLE_NAME = c.TABLE_NAME 
        AND t.TABLE_SCHEMA = c.TABLE_SCHEMA 
        AND c.COLUMN_NAME = 'tenant_id'
WHERE 
    t.TABLE_SCHEMA = DATABASE()
    AND t.TABLE_TYPE = 'BASE TABLE';
EOF

log_success "表统计信息已生成: docs/database/table-statistics.txt"

# 清理临时文件
rm -f /tmp/db_structure.txt /tmp/foreign_keys.txt

echo
echo "========================================"
echo "ER图生成完成！"
echo "========================================"
echo "🎨 docs/database/er-diagram.mmd - 完整ER图"
echo "🎯 docs/database/er-diagram-simple.mmd - 简化版ER图"
echo "📊 docs/database/table-statistics.txt - 表统计信息"
echo "========================================"
echo
echo "查看ER图的方法："
echo "1. 在VSCode中安装Mermaid Preview插件"
echo "2. 访问 https://mermaid.live/ 粘贴内容"
echo "3. 使用支持Mermaid的Markdown编辑器"
