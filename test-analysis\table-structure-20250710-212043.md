# 数据库表结构详情

## 📊 数据库: sports_gaming

**生成时间**: 2025年07月10日 21:21:56

### 表: account_statistic

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| day | date | ❌ |  | 
 | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| article_num | int(11) | ❌ |  | 
 | - |
| read_num | int(11) | ❌ |  | 
 | - |
| read_percent | decimal(10,2) | ❌ |  | 
 | - |
| total_pay_num | int(11) | ❌ |  | 
 | - |
| total_pay_percent | decimal(10,2) | ❌ |  | 
 | - |
| new_pay_num | int(11) | ❌ |  | 
 | - |
| new_pay_percent | decimal(10,2) | ❌ |  | 
 | - |
| new_pay_price_avg | decimal(10,2) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: account_users

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| app_id | varchar(255) | ❌ |  | 
 | - |
| open_id | varchar(255) | ❌ | 📇 FK | NULL | - |
| union_id | varchar(255) | ❌ | 📇 FK | NULL | - |
| subscribe_time | datetime | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

**索引信息**:
- openid
- unionid

---

### 表: article_push_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| article_id | bigint(20) | ❌ |  | 
 | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| consume_status | int(10) | ❌ |  | 
 | - |
| consume_min_num | int(11) | ✅ |  | 
 | - |
| consume_max_num | int(11) | ✅ |  | 
 | - |
| consume_min_amount | decimal(10,2) | ✅ |  | 
 | - |
| consume_max_amount | decimal(10,2) | ✅ |  | 
 | - |
| push_time | datetime | ❌ |  | 
 | - |
| template_id | varchar(255) | ✅ |  | 
 | - |
| idx | int(3) | ✅ |  | 
 | - |

---

### 表: author_accomplishment

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| show_data | mediumtext | ❌ |  | 
 | - |
| show_pic | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: author_article

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ | 📇 FK | NULL | - |
| title | varchar(255) | ✅ |  | 
 | - |
| intro | varchar(512) | ✅ |  | 
 | - |
| free_contents | longtext | ✅ |  | 
 | - |
| contents | longtext | ✅ |  | 
 | - |
| start_time | datetime | ✅ |  | 
 | - |
| second_push_time | datetime | ✅ |  | 
 | - |
| end_time | datetime | ✅ |  | 
 | - |
| price | decimal(10,2) | ❌ |  | 
 | - |
| refund_type | int(11) | ❌ |  | 
 | - |
| win | int(11) | ❌ |  | 
 | - |
| win_name | varchar(50) | ✅ |  | 
 | - |
| win_exc | int(11) | ❌ |  | 
 | - |
| state | tinyint(4) | ❌ |  | 
 | - |
| conclusion | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ✅ |  | 
 | - |
| share_pic_url | varchar(255) | ✅ |  | 
 | - |
| top | int(10) | ✅ |  | 
 | - |
| top_time | datetime | ✅ |  | 
 | - |
| status | int(11) | ❌ |  | 
 | - |
| consume_status | int(11) | ❌ |  | 
 | - |
| consume_min_num | int(11) | ✅ |  | 
 | - |
| consume_max_num | int(11) | ✅ |  | 
 | - |
| consume_min_amount | decimal(10,2) | ✅ |  | 
 | - |
| consume_max_amount | decimal(10,2) | ✅ |  | 
 | - |
| add_free_content | longtext | ✅ |  | 
 | - |
| add_content | longtext | ✅ |  | 
 | - |
| scheme_play | bigint(20) | ✅ |  | 
 | - |
| match_scheme | text | ✅ |  | 
 | - |
| auto_replacement | tinyint(4) | ✅ |  | 
 | - |
| match_ids | varchar(255) | ✅ |  | 
 | - |
| draft | tinyint(4) | ✅ |  | 
 | - |
| recommend_win | int(10) | ✅ |  | 
 | - |
| recommend_win_name | varchar(100) | ✅ |  | 
 | - |
| fourteen | tinyint(3) | ✅ |  | 
 | - |
| issue | varchar(100) | ✅ |  | 
 | - |
| accomplishment | varchar(255) | ✅ |  | 
 | - |
| top_bg | varchar(255) | ✅ |  | 
 | - |

**索引信息**:
- author_Id_index

---

### 表: author_article_append

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK | NULL | - |
| article_id | bigint(20) | ❌ |  | 
 | - |
| type | int(4) | ✅ |  | 
 | - |
| contents | longtext | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ✅ |  | 
 | - |

---

### 表: author_article_pv_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| article_id | bigint(20) | ❌ | 📇 FK | NULL | - |
| ip | varchar(255) | ✅ |  | 
 | - |
| user_agent | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| deleted | tinyint(3) | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ✅ |  | 
 | - |

**索引信息**:
- article_id_index

---

### 表: author_audit

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| img_url | varchar(255) | ✅ |  | 
 | - |
| nickname | varchar(255) | ✅ |  | 
 | - |
| phone | varchar(255) | ✅ |  | 
 | - |
| desc | varchar(500) | ✅ |  | 
 | - |
| id_card | varchar(255) | ✅ |  | 
 | - |
| id_card_front | varchar(255) | ✅ |  | 
 | - |
| id_card_back | varchar(255) | ✅ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| bank_no | varchar(255) | ✅ |  | 
 | - |
| bank_name | varchar(255) | ✅ |  | 
 | - |
| bank_real_name | varchar(255) | ✅ |  | 
 | - |
| pro_desc | varchar(255) | ✅ |  | 
 | - |
| pro_img_urls | varchar(1000) | ✅ |  | 
 | - |
| status | int(11) | ❌ |  | 
 | - |
| fail_reason | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: author_audit_copy1

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| img_url | varchar(255) | ✅ |  | 
 | - |
| id_card | varchar(255) | ✅ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ❌ |  | 
 | - |
| fail_reason | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: author_commission_rate

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| rate | decimal(8,2) | ❌ |  | 
 | - |
| limit | int(11) | ❌ |  | 
 | - |
| max | decimal(10,2) | ❌ |  | 
 | - |
| min | decimal(10,2) | ❌ |  | 
 | - |
| days | int(10) | ❌ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: author_day_report

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| date | date | ❌ |  | 
 | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| article_num | int(11) | ❌ |  | 
 | - |
| pay_user_num | int(11) | ❌ |  | 
 | - |
| pay_amount | decimal(10,2) | ❌ |  | 
 | - |
| refund_amount | decimal(10,2) | ❌ |  | 
 | - |
| income_amount | decimal(10,2) | ❌ |  | 
 | - |
| gold_pay_amount | decimal(10,2) | ❌ |  | 
 | - |
| gold_refund_amount | decimal(10,2) | ❌ |  | 
 | - |
| income_gold_amount | decimal(10,2) | ❌ |  | 
 | - |
| income_total_amount | decimal(10,2) | ❌ |  | 
 | - |
| new_user_pay_amount | decimal(10,2) | ❌ |  | 
 | - |
| pay_amount_per_new_user | decimal(10,2) | ❌ |  | 
 | - |
| pay_new_user_percent | decimal(10,2) | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |

---

### 表: author_hit_rate

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| hit_rate | decimal(10,4) | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |

---

### 表: author_info

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| phone | varchar(255) | ❌ |  | 
 | - |
| id_card | varchar(255) | ❌ |  | 
 | - |
| name | varchar(255) | ❌ |  | 
 | - |
| idcard_img_front | varchar(500) | ✅ |  | 
 | - |
| idcard_img_back | varchar(500) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: author_privilege_set

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| type | tinyint(4) | ❌ |  | 
 | - |
| days | int(11) | ✅ |  | 
 | - |
| price | decimal(20,6) | ✅ |  | 
 | - |
| deleted | int(4) | ❌ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| match_type | int(3) | ✅ |  | 
 | - |

---

### 表: author_withdraw_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| settlement_id | bigint(20) | ❌ |  | 
 | - |
| amount | decimal(10,2) | ❌ |  | 
 | - |
| commission | decimal(10,2) | ❌ |  | 
 | - |
| received | decimal(10,2) | ❌ |  | 
 | - |
| status | int(10) | ❌ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: authors

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| correlation_id | varchar(255) | ✅ |  | 
 | - |
| nickname | varchar(255) | ✅ |  | 
 | - |
| avatar_url | varchar(255) | ✅ |  | 
 | - |
| total_income | decimal(10,2) | ✅ |  | 
 | - |
| fans | int(10) | ✅ |  | 
 | - |
| article_num | int(10) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| register_ip | varchar(255) | ✅ |  | 
 | - |
| register_time | datetime | ✅ |  | 
 | - |
| last_login_ip | varchar(255) | ✅ |  | 
 | - |
| last_login_time | datetime | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: broomer_match_scheme

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| match_id | bigint(20) | ❌ |  | 
 | - |
| data_type | int(11) | ❌ |  | 
 | - |
| match_scheme | varchar(2000) | ❌ |  | 
 | - |
| contents | mediumtext | ❌ |  | 
 | - |
| price | decimal(10,2) | ❌ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: broomers

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: football_bd_odds

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| comp | varchar(100) | ✅ |  | 
 | - |
| home | varchar(100) | ✅ |  | 
 | - |
| away | varchar(100) | ✅ |  | 
 | - |
| issue | int(11) | ✅ |  | 
 | - |
| issue_num | int(10) | ✅ |  | 
 | - |
| match_time | bigint(20) | ✅ |  | 
 | - |
| sell_status | varchar(255) | ✅ |  | 
 | - |
| odds | longtext | ✅ |  | 
 | - |

---

### 表: football_bd_result

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| comp | varchar(100) | ✅ |  | 
 | - |
| home | varchar(100) | ✅ |  | 
 | - |
| away | varchar(100) | ✅ |  | 
 | - |
| issue | int(11) | ✅ |  | 
 | - |
| issue_num | int(10) | ✅ |  | 
 | - |
| match_time | bigint(20) | ✅ |  | 
 | - |
| sell_status | varchar(255) | ✅ |  | 
 | - |
| odds | longtext | ✅ |  | 
 | - |
| home_score | int(3) | ✅ |  | 
 | - |
| away_score | int(3) | ✅ |  | 
 | - |

---

### 表: football_bd_sf_odds

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| sport_id | tinyint(4) | ✅ |  | 
 | - |
| comp | varchar(100) | ✅ |  | 
 | - |
| home | varchar(100) | ✅ |  | 
 | - |
| away | varchar(100) | ✅ |  | 
 | - |
| issue | int(10) | ✅ |  | 
 | - |
| issue_num | int(10) | ✅ |  | 
 | - |
| match_time | bigint(20) | ✅ |  | 
 | - |
| sell_status | varchar(100) | ✅ |  | 
 | - |
| odds | longtext | ✅ |  | 
 | - |

---

### 表: football_bd_sf_result

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| sport_id | tinyint(4) | ✅ |  | 
 | - |
| comp | varchar(100) | ✅ |  | 
 | - |
| home | varchar(100) | ✅ |  | 
 | - |
| away | varchar(100) | ✅ |  | 
 | - |
| issue | int(10) | ✅ |  | 
 | - |
| issue_num | int(10) | ✅ |  | 
 | - |
| match_time | bigint(20) | ✅ |  | 
 | - |
| sell_status | varchar(100) | ✅ |  | 
 | - |
| odds | longtext | ✅ |  | 
 | - |
| home_score | int(3) | ✅ |  | 
 | - |
| away_score | int(3) | ✅ |  | 
 | - |

---

### 表: football_bo_live_odds

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| company_id | bigint(20) | ❌ |  | 
 | - |
| match_id | bigint(20) | ❌ |  | 
 | - |
| odds_update_times | bigint(20) | ✅ |  | 
 | - |
| home_odds | decimal(10,2) | ❌ |  | 
 | - |
| draw_odds | decimal(10,2) | ❌ |  | 
 | - |
| away_odds | decimal(10,2) | ❌ |  | 
 | - |
| status | int(10) | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |

---

### 表: football_company

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name_zh | varchar(100) | ✅ |  | 
 | - |
| name_en | varchar(255) | ✅ |  | 
 | - |

---

### 表: football_half_live_odds

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| company_id | bigint(20) | ❌ |  | 
 | - |
| match_id | bigint(20) | ❌ |  | 
 | - |
| match_status | int(10) | ❌ |  | 
 | - |
| type | varchar(255) | ❌ |  | 
 | - |
| odds_update_times | bigint(20) | ✅ |  | 
 | - |
| match_time | varchar(10) | ✅ |  | 
 | - |
| home_odds | decimal(10,2) | ❌ |  | 
 | - |
| draw_odds | decimal(10,2) | ❌ |  | 
 | - |
| away_odds | decimal(10,2) | ❌ |  | 
 | - |
| status | int(10) | ❌ |  | 
 | - |
| home_score | int(10) | ❌ |  | 
 | - |
| away_score | int(10) | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |

---

### 表: football_issue

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| issue | int(11) | ✅ |  | 
 | - |
| start_time | varchar(100) | ✅ |  | 
 | - |
| end_time | varchar(100) | ✅ |  | 
 | - |
| draw_time | varchar(100) | ✅ |  | 
 | - |

---

### 表: football_jc_odds

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| comp | varchar(100) | ✅ |  | 
 | - |
| home | varchar(100) | ✅ |  | 
 | - |
| away | varchar(100) | ✅ |  | 
 | - |
| short_comp | varchar(100) | ✅ |  | 
 | - |
| short_home | varchar(100) | ✅ |  | 
 | - |
| short_away | varchar(100) | ✅ |  | 
 | - |
| issue_num | varchar(100) | ✅ |  | 
 | - |
| sell_status | varchar(100) | ✅ |  | 
 | - |
| match_time | bigint(20) | ✅ |  | 
 | - |
| spf | varchar(100) | ✅ |  | 
 | - |
| rq | varchar(100) | ✅ |  | 
 | - |
| bf | text | ✅ |  | 
 | - |
| jq | varchar(255) | ✅ |  | 
 | - |
| bqc | varchar(255) | ✅ |  | 
 | - |

---

### 表: football_jc_result

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| comp | varchar(100) | ✅ |  | 
 | - |
| home | varchar(100) | ✅ |  | 
 | - |
| away | varchar(100) | ✅ |  | 
 | - |
| short_comp | varchar(100) | ✅ |  | 
 | - |
| short_home | varchar(100) | ✅ |  | 
 | - |
| short_away | varchar(100) | ✅ |  | 
 | - |
| issue_num | varchar(100) | ✅ |  | 
 | - |
| match_time | bigint(20) | ✅ |  | 
 | - |
| home_score | int(3) | ✅ |  | 
 | - |
| away_score | int(3) | ✅ |  | 
 | - |
| half_home_score | int(3) | ✅ |  | 
 | - |
| half_away_score | int(3) | ✅ |  | 
 | - |
| spf | varchar(255) | ✅ |  | 
 | - |
| rq | varchar(255) | ✅ |  | 
 | - |
| bf | varchar(255) | ✅ |  | 
 | - |
| jq | varchar(255) | ✅ |  | 
 | - |
| bqc | varchar(255) | ✅ |  | 
 | - |

---

### 表: football_live_odds

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| company_id | bigint(20) | ❌ |  | 
 | - |
| match_id | bigint(20) | ❌ |  | 
 | - |
| match_status | int(10) | ❌ |  | 
 | - |
| type | varchar(255) | ❌ |  | 
 | - |
| odds_update_times | bigint(20) | ✅ |  | 
 | - |
| match_time | varchar(10) | ✅ |  | 
 | - |
| home_odds | decimal(10,2) | ❌ |  | 
 | - |
| draw_odds | decimal(10,2) | ❌ |  | 
 | - |
| away_odds | decimal(10,2) | ❌ |  | 
 | - |
| status | int(10) | ❌ |  | 
 | - |
| home_score | int(10) | ❌ |  | 
 | - |
| away_score | int(10) | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |

---

### 表: football_match

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| comp | varchar(100) | ✅ |  | 
 | - |
| home | varchar(100) | ✅ |  | 
 | - |
| away | varchar(100) | ✅ |  | 
 | - |
| issue | int(10) | ✅ |  | 
 | - |
| match_time | bigint(20) | ✅ |  | 
 | - |
| result | varchar(100) | ✅ |  | 
 | - |
| type | varchar(100) | ✅ |  | 
 | - |

---

### 表: football_match_result

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| issue | varchar(100) | ✅ |  | 
 | - |
| result | varchar(255) | ✅ |  | 
 | - |
| first_pot_count | int(10) | ✅ |  | 
 | - |
| first_prize | decimal(10,2) | ✅ |  | 
 | - |
| sales | decimal(10,2) | ✅ |  | 
 | - |
| second_pont_count | int(10) | ✅ |  | 
 | - |
| second_prize | decimal(10,2) | ✅ |  | 
 | - |
| jackpot | decimal(10,2) | ✅ |  | 
 | - |
| type | varchar(100) | ✅ |  | 
 | - |

---

### 表: football_tc_match

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| sport_id | tinyint(4) | ✅ |  | 
 | - |
| match_id | bigint(20) | ✅ |  | 
 | - |
| lottery_type | int(11) | ✅ |  | 
 | - |
| issue | varchar(100) | ✅ |  | 
 | - |
| issue_num | varchar(100) | ✅ |  | 
 | - |
| home_name | varchar(100) | ✅ |  | 
 | - |
| away_name | varchar(100) | ✅ |  | 
 | - |
| is_name | tinyint(4) | ✅ |  | 
 | - |

---

### 表: gold_order

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| order_no | varchar(100) | ✅ | 🔒 UK | NULL | - |
| user_id | bigint(20) | ✅ | 📇 FK | NULL | - |
| amount | decimal(10,2) | ✅ |  | 
 | - |
| pay_amount | decimal(10,2) | ✅ |  | 
 | - |
| commission | decimal(10,2) | ✅ |  | 
 | - |
| gold_num | decimal(10,2) | ✅ |  | 
 | - |
| pay_type | tinyint(4) | ✅ |  | 
 | - |
| pay_time | datetime | ✅ |  | 
 | - |
| status | tinyint(4) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| third_party_no | varchar(100) | ✅ |  | 
 | - |
| channel_order_sn | varchar(100) | ✅ |  | 
 | - |
| ins_order_sn | varchar(100) | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| client_ip | varchar(100) | ✅ |  | 
 | - |
| present_gold | decimal(10,2) | ✅ |  | 
 | - |
| pay_app_id | varchar(255) | ✅ |  | 
 | - |
| pay_app_type | int(10) | ✅ |  | 
 | - |

**索引信息**:
- order_no
- user_id

---

### 表: guess_records

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK | NULL | - |
| match_id | bigint(20) unsigned | ❌ | 📇 FK | NULL | - |
| wx_account | varchar(50) | ❌ | 📇 FK | NULL | - |
| phone | varchar(20) | ❌ | 📇 FK | NULL | - |
| guess_type | tinyint(4) | ❌ |  | 
 | - |
| guess_item | tinyint(4) | ❌ |  | 
 | - |
| guess_value | varchar(20) | ❌ |  | 
 | - |
| status | tinyint(4) | ✅ | 📇 FK | 0 | - |
| win_amount | decimal(10,2) | ✅ |  | 
 | - |
| is_auto_selected | tinyint(4) | ✅ |  | 
 | - |
| error_msg | varchar(255) | ✅ |  | 
 | - |
| created_at | timestamp | ❌ |  | 
 | - |
| updated_at | timestamp | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted_at | timestamp | ✅ |  | 
 | - |

**索引信息**:
- idx_match_id
- idx_match_user
- idx_phone
- idx_status
- idx_wx_account

---

### 表: home_banner

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| title | varchar(255) | ❌ |  | 
 | - |
| pic | varchar(255) | ❌ |  | 
 | - |
| to_url | varchar(255) | ✅ |  | 
 | - |
| status | int(10) | ❌ |  | 
 | - |
| sort | int(10) | ❌ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | int(10) | ❌ |  | 
 | - |

---

### 表: infra_api_access_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| trace_id | varchar(64) | ❌ |  |  | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| application_name | varchar(50) | ❌ |  | 
 | - |
| request_method | varchar(16) | ❌ |  |  | - |
| request_url | varchar(255) | ❌ |  |  | - |
| request_params | text | ✅ |  | 
 | - |
| response_body | text | ✅ |  | 
 | - |
| user_ip | varchar(50) | ❌ |  | 
 | - |
| user_agent | varchar(512) | ❌ |  | 
 | - |
| operate_module | varchar(50) | ✅ |  | 
 | - |
| operate_name | varchar(50) | ✅ |  | 
 | - |
| operate_type | tinyint(4) | ✅ |  | 
 | - |
| begin_time | datetime | ❌ |  | 
 | - |
| end_time | datetime | ❌ |  | 
 | - |
| duration | int(11) | ❌ |  | 
 | - |
| result_code | int(11) | ❌ |  | 
 | - |
| result_msg | varchar(512) | ✅ |  |  | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ | 📇 FK | CURRENT_TIMESTAMP | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

**索引信息**:
- idx_create_time

---

### 表: infra_api_error_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| trace_id | varchar(64) | ❌ |  | 
 | - |
| user_id | int(11) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| application_name | varchar(50) | ❌ |  | 
 | - |
| request_method | varchar(16) | ❌ |  | 
 | - |
| request_url | varchar(255) | ❌ |  | 
 | - |
| request_params | varchar(8000) | ❌ |  | 
 | - |
| user_ip | varchar(50) | ❌ |  | 
 | - |
| user_agent | varchar(512) | ❌ |  | 
 | - |
| exception_time | datetime | ❌ |  | 
 | - |
| exception_name | varchar(128) | ❌ |  |  | - |
| exception_message | text | ❌ |  | 
 | - |
| exception_root_cause_message | text | ❌ |  | 
 | - |
| exception_stack_trace | text | ❌ |  | 
 | - |
| exception_class_name | varchar(512) | ❌ |  | 
 | - |
| exception_file_name | varchar(512) | ❌ |  | 
 | - |
| exception_method_name | varchar(512) | ❌ |  | 
 | - |
| exception_line_number | int(11) | ❌ |  | 
 | - |
| process_status | tinyint(4) | ❌ |  | 
 | - |
| process_time | datetime | ✅ |  | 
 | - |
| process_user_id | int(11) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: infra_codegen_column

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| table_id | bigint(20) | ❌ |  | 
 | - |
| column_name | varchar(200) | ❌ |  | 
 | - |
| data_type | varchar(100) | ❌ |  | 
 | - |
| column_comment | varchar(500) | ❌ |  | 
 | - |
| nullable | bit(1) | ❌ |  | 
 | - |
| primary_key | bit(1) | ❌ |  | 
 | - |
| ordinal_position | int(11) | ❌ |  | 
 | - |
| java_type | varchar(32) | ❌ |  | 
 | - |
| java_field | varchar(64) | ❌ |  | 
 | - |
| dict_type | varchar(200) | ✅ |  |  | - |
| example | varchar(64) | ✅ |  | 
 | - |
| create_operation | bit(1) | ❌ |  | 
 | - |
| update_operation | bit(1) | ❌ |  | 
 | - |
| list_operation | bit(1) | ❌ |  | 
 | - |
| list_operation_condition | varchar(32) | ❌ |  | 
 | - |
| list_operation_result | bit(1) | ❌ |  | 
 | - |
| html_type | varchar(32) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: infra_codegen_table

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| data_source_config_id | bigint(20) | ❌ |  | 
 | - |
| scene | tinyint(4) | ❌ |  | 
 | - |
| table_name | varchar(200) | ❌ |  |  | - |
| table_comment | varchar(500) | ❌ |  |  | - |
| remark | varchar(500) | ✅ |  | 
 | - |
| module_name | varchar(30) | ❌ |  | 
 | - |
| business_name | varchar(30) | ❌ |  | 
 | - |
| class_name | varchar(100) | ❌ |  |  | - |
| class_comment | varchar(50) | ❌ |  | 
 | - |
| author | varchar(50) | ❌ |  | 
 | - |
| template_type | tinyint(4) | ❌ |  | 
 | - |
| front_type | tinyint(4) | ❌ |  | 
 | - |
| parent_menu_id | bigint(20) | ✅ |  | 
 | - |
| master_table_id | bigint(20) | ✅ |  | 
 | - |
| sub_join_column_id | bigint(20) | ✅ |  | 
 | - |
| sub_join_many | bit(1) | ✅ |  | 
 | - |
| tree_parent_column_id | bigint(20) | ✅ |  | 
 | - |
| tree_name_column_id | bigint(20) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: infra_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| category | varchar(50) | ❌ |  | 
 | - |
| type | tinyint(4) | ❌ |  | 
 | - |
| name | varchar(100) | ❌ |  |  | - |
| config_key | varchar(100) | ❌ |  |  | - |
| value | varchar(500) | ❌ |  |  | - |
| visible | bit(1) | ❌ |  | 
 | - |
| remark | varchar(500) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: infra_data_source_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(100) | ❌ |  |  | - |
| url | varchar(1024) | ❌ |  | 
 | - |
| username | varchar(255) | ❌ |  | 
 | - |
| password | varchar(255) | ❌ |  |  | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: infra_file

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| config_id | bigint(20) | ✅ |  | 
 | - |
| name | varchar(256) | ✅ |  | 
 | - |
| path | varchar(512) | ❌ |  | 
 | - |
| url | varchar(1024) | ❌ |  | 
 | - |
| type | varchar(128) | ✅ |  | 
 | - |
| size | int(11) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: infra_file_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| name | varchar(63) | ❌ |  | 
 | - |
| storage | tinyint(4) | ❌ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| master | bit(1) | ❌ |  | 
 | - |
| config | varchar(4096) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: infra_file_content

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| config_id | bigint(20) | ❌ |  | 
 | - |
| path | varchar(512) | ❌ |  | 
 | - |
| content | mediumblob | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: infra_job

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(32) | ❌ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| handler_name | varchar(64) | ❌ |  | 
 | - |
| handler_param | varchar(255) | ✅ |  | 
 | - |
| cron_expression | varchar(32) | ❌ |  | 
 | - |
| retry_count | int(11) | ❌ |  | 
 | - |
| retry_interval | int(11) | ❌ |  | 
 | - |
| monitor_timeout | int(11) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: infra_job_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| job_id | bigint(20) | ❌ |  | 
 | - |
| handler_name | varchar(64) | ❌ |  | 
 | - |
| handler_param | varchar(255) | ✅ |  | 
 | - |
| execute_index | tinyint(4) | ❌ |  | 
 | - |
| begin_time | datetime | ❌ |  | 
 | - |
| end_time | datetime | ✅ |  | 
 | - |
| duration | int(11) | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| result | varchar(4000) | ✅ |  |  | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: match_category

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(50) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |

---

### 表: match_coach

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(50) | ❌ |  | 
 | - |
| logo | varchar(255) | ✅ |  | 
 | - |
| birthday | bigint(20) | ✅ |  | 
 | - |
| age | int(3) | ✅ |  | 
 | - |
| preferred_formation | varchar(50) | ✅ |  | 
 | - |
| joined | bigint(20) | ✅ |  | 
 | - |
| team_id | bigint(20) | ✅ |  | 
 | - |
| contract_until | bigint(20) | ✅ |  | 
 | - |
| type | tinyint(4) | ✅ |  | 
 | - |

---

### 表: match_competition

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| category_id | bigint(20) | ✅ |  | 
 | - |
| name | varchar(50) | ✅ |  | 
 | - |
| short_name | varchar(50) | ✅ |  | 
 | - |
| logo | varchar(255) | ✅ |  | 
 | - |
| type | int(11) | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| country_id | int(10) | ✅ |  | 
 | - |

---

### 表: match_country

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | int(10) | ❌ | 🔑 PK | NULL | - |
| category_id | bigint(20) | ✅ | 📇 FK | NULL | - |
| logo | varchar(255) | ✅ |  | 
 | - |
| name | varchar(100) | ✅ |  | 
 | - |

**索引信息**:
- match_country_category_id_IDX

---

### 表: match_future_record

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| match_id | bigint(20) | ❌ |  | 
 | - |
| competition_id | bigint(20) | ❌ |  | 
 | - |
| home_team_id | bigint(20) | ❌ |  | 
 | - |
| away_team_id | bigint(20) | ❌ |  | 
 | - |
| match_date | datetime | ❌ |  | 
 | - |

---

### 表: match_history

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| competition_id | bigint(20) | ❌ |  | 
 | - |
| match_id | bigint(20) | ❌ |  | 
 | - |
| home_team_id | bigint(20) | ❌ |  | 
 | - |
| home_qt_team_id | bigint(20) | ❌ |  | 
 | - |
| away_team_id | bigint(20) | ❌ |  | 
 | - |
| away_qt_team_id | bigint(20) | ❌ |  | 
 | - |
| match_date | date | ❌ |  | 
 | - |
| home_score | int(11) | ❌ |  | 
 | - |
| away_score | int(11) | ❌ |  | 
 | - |
| home_half_score | int(11) | ❌ |  | 
 | - |
| away_half_score | int(11) | ❌ |  | 
 | - |
| home_corner_kick | int(11) | ❌ |  | 
 | - |
| away_corner_kick | int(11) | ❌ |  | 
 | - |
| home_odds | decimal(10,2) | ❌ |  | 
 | - |
| odds | varchar(255) | ❌ |  | 
 | - |
| away_odds | decimal(10,2) | ❌ |  | 
 | - |
| home_avg | decimal(10,2) | ❌ |  | 
 | - |
| draw_avg | decimal(10,2) | ❌ |  | 
 | - |
| away_avg | decimal(10,2) | ❌ |  | 
 | - |
| win_or_loss | varchar(255) | ❌ |  | 
 | - |
| handi_cap | varchar(255) | ❌ |  | 
 | - |
| goals | varchar(255) | ❌ |  | 
 | - |

---

### 表: match_lineup_detail

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| match_id | bigint(20) | ❌ |  | 
 | - |
| lineup_type | int(11) | ❌ |  | 
 | - |
| team_id | bigint(20) | ❌ |  | 
 | - |
| player_id | bigint(20) | ✅ |  | 
 | - |
| first | int(11) | ✅ |  | 
 | - |
| captain | int(11) | ✅ |  | 
 | - |
| name | varchar(50) | ✅ |  | 
 | - |
| logo | varchar(255) | ✅ |  | 
 | - |
| shirt_number | int(11) | ✅ |  | 
 | - |
| position | varchar(50) | ✅ |  | 
 | - |
| x | int(11) | ✅ |  | 
 | - |
| y | int(11) | ✅ |  | 
 | - |
| rating | varchar(50) | ✅ |  | 
 | - |
| upadte_time | datetime | ✅ |  | 
 | - |
| incidents | text | ✅ |  | 
 | - |

---

### 表: match_list

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| season_id | bigint(20) | ❌ |  | 
 | - |
| competition_id | bigint(20) | ❌ |  | 
 | - |
| home_team_id | bigint(20) | ❌ |  | 
 | - |
| away_team_id | bigint(20) | ❌ |  | 
 | - |
| status_id | int(11) | ❌ |  | 
 | - |
| match_time | bigint(20) | ❌ | 📇 FK | NULL | - |
| neutral | int(11) | ✅ |  | 
 | - |
| home_scores | varchar(50) | ✅ |  | 
 | - |
| away_scores | varchar(50) | ✅ |  | 
 | - |
| home_position | varchar(50) | ✅ |  | 
 | - |
| away_position | varchar(50) | ✅ |  | 
 | - |
| agg_score | varchar(50) | ✅ |  | 
 | - |
| related_id | int(11) | ✅ |  | 
 | - |
| live_info | longtext | ✅ |  | 
 | - |
| coverage | text | ✅ |  | 
 | - |
| incidents | longtext | ✅ |  | 
 | - |
| round | text | ✅ |  | 
 | - |
| stats | longtext | ✅ |  | 
 | - |
| confirmed | tinyint(3) | ✅ |  | 
 | - |
| home_formation | varchar(100) | ✅ |  | 
 | - |
| away_formation | varchar(100) | ✅ |  | 
 | - |
| home_color | varchar(100) | ✅ |  | 
 | - |
| away_color | varchar(100) | ✅ |  | 
 | - |
| referee_id | bigint(20) | ✅ |  | 
 | - |

**索引信息**:
- match_list_match_time_IDX

---

### 表: match_live_info

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| main | tinyint(4) | ✅ |  | 
 | - |
| type | int(10) | ✅ |  | 
 | - |
| position | tinyint(4) | ✅ |  | 
 | - |
| time | varchar(100) | ✅ |  | 
 | - |
| data | varchar(100) | ✅ |  | 
 | - |
| match_id | bigint(20) | ✅ | 📇 FK | NULL | - |

**索引信息**:
- match_live_info_match_id_IDX

---

### 表: match_odds

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| match_id | bigint(20) | ❌ |  | 
 | - |
| odds | varchar(255) | ❌ |  | 
 | - |

---

### 表: match_player

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(50) | ❌ |  | 
 | - |
| short_name | varchar(50) | ❌ |  | 
 | - |
| logo | varchar(255) | ✅ |  | 
 | - |
| age | int(11) | ✅ |  | 
 | - |
| height | int(11) | ✅ |  | 
 | - |
| weight | int(11) | ✅ |  | 
 | - |
| market_value | int(11) | ✅ |  | 
 | - |
| market_value_currency | int(11) | ✅ |  | 
 | - |
| contract_until | int(11) | ✅ |  | 
 | - |
| coach_id | int(11) | ✅ |  | 
 | - |
| uid | int(11) | ✅ |  | 
 | - |
| ability | varchar(255) | ✅ |  | 
 | - |
| characteristics | varchar(255) | ✅ |  | 
 | - |
| position | varchar(255) | ✅ |  | 
 | - |
| positions | varchar(255) | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |

---

### 表: match_player_info

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| match_id | bigint(20) | ❌ |  | 
 | - |
| team_id | bigint(20) | ❌ |  | 
 | - |
| player | varchar(255) | ✅ |  | 
 | - |
| reason | varchar(255) | ✅ |  | 
 | - |

---

### 表: match_player_transfer

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| team_id | bigint(20) | ❌ |  | 
 | - |
| transfer_date | date | ❌ |  | 
 | - |
| type | int(10) | ❌ |  | 
 | - |
| player | varchar(255) | ❌ |  | 
 | - |
| player_type | varchar(255) | ❌ |  | 
 | - |
| transfer_type | varchar(255) | ❌ |  | 
 | - |
| to_team_id | bigint(20) | ❌ |  | 
 | - |
| to_team_name | varchar(255) | ❌ |  | 
 | - |
| to_team_qt_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: match_point_rank

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| team_id | bigint(20) | ❌ |  | 
 | - |
| name | varchar(255) | ❌ |  | 
 | - |
| match_num | int(11) | ❌ |  | 
 | - |
| win_num | int(11) | ❌ |  | 
 | - |
| draw_num | int(11) | ❌ |  | 
 | - |
| loss_num | int(11) | ❌ |  | 
 | - |
| goals_scored | int(11) | ❌ |  | 
 | - |
| goals_conceded | int(11) | ❌ |  | 
 | - |
| goal_difference | int(11) | ❌ |  | 
 | - |
| points | int(11) | ❌ |  | 
 | - |
| ranking | int(11) | ✅ |  | 
 | - |
| winning_rate | varchar(255) | ✅ |  | 
 | - |
| qt_id | bigint(20) | ✅ |  | 
 | - |

---

### 表: match_referee

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(100) | ✅ |  | 
 | - |
| birthday | bigint(20) | ✅ |  | 
 | - |
| age | int(3) | ✅ |  | 
 | - |

---

### 表: match_season

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| competition_id | bigint(20) | ❌ |  | 
 | - |
| year | varchar(50) | ❌ |  | 
 | - |
| start_time | datetime | ❌ |  | 
 | - |
| end_time | datetime | ❌ |  | 
 | - |
| is_current | int(11) | ❌ |  | 
 | - |
| has_player_stats | int(11) | ❌ |  | 
 | - |
| has_team_stats | int(11) | ❌ |  | 
 | - |
| has_table | int(11) | ❌ |  | 
 | - |
| update_time | datetime | ❌ |  | 
 | - |

---

### 表: match_stage

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| season_id | bigint(20) | ✅ |  | 
 | - |
| name_zh | varchar(100) | ✅ |  | 
 | - |
| name_en | varchar(255) | ✅ |  | 
 | - |
| mode | tinyint(3) | ✅ |  | 
 | - |
| group_count | int(10) | ✅ |  | 
 | - |
| round_count | int(10) | ✅ |  | 
 | - |
| order_by | int(10) | ✅ |  | 
 | - |

---

### 表: match_stats

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| stats | longtext | ✅ |  | 
 | - |

---

### 表: match_team

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(50) | ❌ |  | 
 | - |
| short_name | varchar(50) | ✅ |  | 
 | - |
| coach_id | bigint(20) | ✅ |  | 
 | - |
| logo | varchar(255) | ✅ |  | 
 | - |
| foundation_time | bigint(20) | ✅ |  | 
 | - |
| market_value | bigint(20) | ✅ |  | 
 | - |
| market_value_currency | varchar(50) | ✅ |  | 
 | - |
| uid | int(11) | ✅ |  | 
 | - |
| country_id | int(6) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: matches

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK | NULL | - |
| title | varchar(100) | ✅ | 📇 FK | NULL | - |
| match_time | timestamp | ❌ | 📇 FK | CURRENT_TIMESTAMP | - |
| team_a | varchar(50) | ❌ |  | 
 | - |
| team_b | varchar(50) | ❌ |  | 
 | - |
| video_account | varchar(100) | ✅ |  | 
 | - |
| anchor_name | varchar(50) | ✅ |  | 
 | - |
| has_guess | tinyint(4) | ✅ |  | 
 | - |
| guess_type | tinyint(4) | ❌ | 📇 FK | NULL | - |
| start_time | timestamp | ✅ | 📇 FK | NULL | - |
| end_time | timestamp | ✅ | 📇 FK | NULL | - |
| match_code | varchar(20) | ❌ | 🔒 UK | NULL | - |
| status | tinyint(4) | ✅ | 📇 FK | 0 | - |
| result | varchar(100) | ✅ |  | 
 | - |
| winner_announced | tinyint(4) | ✅ |  | 
 | - |
| bonus_pool | decimal(10,2) | ✅ |  | 
 | - |
| error_msg | varchar(255) | ✅ |  | 
 | - |
| created_at | timestamp | ❌ |  | 
 | - |
| updated_at | timestamp | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted_at | timestamp | ✅ |  | 
 | - |

**索引信息**:
- idx_end_time
- idx_guess_type
- idx_match_code
- idx_match_time
- idx_start_time
- idx_status
- idx_title

---

### 表: member_address

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| mobile | varchar(255) | ✅ |  | 
 | - |
| area_id | bigint(20) | ✅ |  | 
 | - |
| detail_address | varchar(255) | ✅ |  | 
 | - |
| default_status | tinyint(1) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_attention

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ✅ | 📇 FK | NULL | - |
| author_id | bigint(20) | ✅ | 📇 FK | NULL | - |
| attention_time | date | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(3) | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ✅ |  | 
 | - |
| status | tinyint(4) | ✅ |  | 
 | - |

**索引信息**:
- author_id
- user_id

---

### 表: member_author_privilege

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| privilege_id | int(11) | ✅ |  | 
 | - |
| type | int(11) | ❌ |  | 
 | - |
| start_date | datetime | ✅ |  | 
 | - |
| end_date | datetime | ✅ |  | 
 | - |
| num | int(11) | ✅ |  | 
 | - |
| ex_num | int(11) | ✅ |  | 
 | - |
| status | int(11) | ❌ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| update_time | datetime | ❌ |  | 
 | - |
| match_type | int(10) | ✅ |  | 
 | - |
| activate | tinyint(4) | ✅ |  | 
 | - |

---

### 表: member_bind_record

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| bind_id | varchar(255) | ✅ |  | 
 | - |
| balance | decimal(10,2) | ✅ |  | 
 | - |
| welfare | decimal(10,2) | ✅ |  | 
 | - |
| status | tinyint(4) | ✅ |  | 
 | - |
| remark | varchar(100) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | bit(1) | ✅ |  | 
 | - |

---

### 表: member_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| point_trade_deduct_enable | tinyint(1) | ✅ |  | 
 | - |
| point_trade_deduct_unit_price | int(11) | ✅ |  | 
 | - |
| point_trade_deduct_max_price | int(11) | ✅ |  | 
 | - |
| point_trade_give_point | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_experience_record

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| biz_type | int(11) | ✅ |  | 
 | - |
| biz_id | varchar(255) | ✅ |  | 
 | - |
| title | varchar(255) | ✅ |  | 
 | - |
| description | varchar(255) | ✅ |  | 
 | - |
| experience | int(11) | ✅ |  | 
 | - |
| total_experience | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_group

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_level

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| level | int(11) | ✅ |  | 
 | - |
| experience | int(11) | ✅ |  | 
 | - |
| discount_percent | int(11) | ✅ |  | 
 | - |
| icon | varchar(255) | ✅ |  | 
 | - |
| background_url | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_level_record

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| level_id | bigint(20) | ✅ |  | 
 | - |
| level | int(11) | ✅ |  | 
 | - |
| discount_percent | int(11) | ✅ |  | 
 | - |
| experience | int(11) | ✅ |  | 
 | - |
| user_experience | int(11) | ✅ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| description | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_match_attention

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ | 📇 FK | NULL | - |
| match_id | bigint(20) | ❌ | 📇 FK | NULL | - |
| create_time | datetime | ❌ |  | 
 | - |

**索引信息**:
- match_id
- user_id
- user_id_2

---

### 表: member_match_chat

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| match_id | bigint(20) | ❌ | 📇 FK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| msg | varchar(255) | ❌ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |

**索引信息**:
- member_match_chat_match_and_id_IDX
- member_match_chat_match_id_IDX

---

### 表: member_match_vote

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| match_id | bigint(20) | ❌ | 📇 FK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| team_id | bigint(20) | ❌ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |

**索引信息**:
- member_match_vote_match_id_IDX

---

### 表: member_point_record

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| biz_id | varchar(255) | ✅ |  | 
 | - |
| biz_type | int(11) | ✅ |  | 
 | - |
| title | varchar(255) | ✅ |  | 
 | - |
| description | varchar(255) | ✅ |  | 
 | - |
| point | int(11) | ✅ |  | 
 | - |
| total_point | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_privilege_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| privilege_id | bigint(20) | ❌ |  | 
 | - |
| type | tinyint(4) | ✅ |  | 
 | - |
| order_id | bigint(20) | ✅ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| update_time | datetime | ❌ |  | 
 | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| price | decimal(10,2) | ✅ |  | 
 | - |
| title | varchar(100) | ✅ |  | 
 | - |
| num | int(10) | ✅ |  | 
 | - |

---

### 表: member_settlement_info

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| settle_type | bigint(20) | ❌ |  | 
 | - |
| account | varchar(50) | ❌ |  | 
 | - |
| name | varchar(20) | ❌ |  | 
 | - |
| id_no | varchar(50) | ✅ |  | 
 | - |
| bank_id | bigint(20) | ✅ |  | 
 | - |
| bank_name | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| update_time | datetime | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ❌ |  | 
 | - |

---

### 表: member_sign_in_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| day | int(11) | ✅ |  | 
 | - |
| point | int(11) | ✅ |  | 
 | - |
| experience | int(11) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_sign_in_record

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| day | int(11) | ✅ |  | 
 | - |
| point | int(11) | ✅ |  | 
 | - |
| experience | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_tag

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_user

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| username | varchar(255) | ✅ |  | 
 | - |
| password | varchar(255) | ✅ |  | 
 | - |
| correlation_id | varchar(255) | ✅ |  | 
 | - |
| mobile | varchar(255) | ✅ | 🔒 UK | NULL | - |
| nickname | varchar(255) | ✅ |  | 
 | - |
| avatar_url | varchar(255) | ✅ |  | 
 | - |
| news_head_url | varchar(255) | ✅ |  | 
 | - |
| recharge_num | decimal(10,2) | ✅ |  | 
 | - |
| consume_amount | decimal(10,2) | ✅ |  | 
 | - |
| gold | decimal(10,2) | ✅ |  | 
 | - |
| balance | decimal(10,2) | ✅ |  | 
 | - |
| ex_balance | decimal(10,2) | ❌ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| register_ip | varchar(255) | ✅ |  | 
 | - |
| register_time | datetime | ✅ |  | 
 | - |
| last_login_ip | varchar(255) | ✅ |  | 
 | - |
| last_login_time | datetime | ✅ |  | 
 | - |
| author | int(11) | ✅ |  | 
 | - |
| partner | int(11) | ✅ |  | 
 | - |
| ex_code | varchar(255) | ✅ |  | 
 | - |
| fans | int(11) | ✅ |  | 
 | - |
| total_income | decimal(10,2) | ✅ |  | 
 | - |
| article_num | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |
| wx_work_id | bigint(20) | ✅ |  | 
 | - |
| welcome_msg | varchar(512) | ✅ |  | 
 | - |
| qq | varchar(20) | ✅ |  | 
 | - |
| parent_id | bigint(20) | ✅ |  | 
 | - |
| captive_push_account | bigint(20) | ✅ |  | 
 | - |
| qq_mail | varchar(100) | ✅ |  | 
 | - |
| competition_ids | text | ✅ |  | 
 | - |
| origin | int(10) | ❌ |  | 
 | - |
| belong_author | bigint(20) | ✅ |  | 
 | - |
| belong_partner | bigint(20) | ✅ |  | 
 | - |
| commission_ratio | int(10) | ✅ |  | 
 | - |
| public_user | int(10) | ✅ |  | 
 | - |
| intro | varchar(255) | ✅ |  | 
 | - |
| is_attention | tinyint(4) | ✅ |  | 
 | - |
| consume | tinyint(4) | ✅ |  | 
 | - |
| accomplishment | tinyint(4) | ✅ |  | 
 | - |
| editor_type | tinyint(4) | ✅ |  | 
 | - |

**索引信息**:
- 唯一电话

---

### 表: member_user_bak

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| mobile | varchar(255) | ✅ |  | 
 | - |
| password | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| register_ip | varchar(255) | ✅ |  | 
 | - |
| register_terminal | int(11) | ✅ |  | 
 | - |
| login_ip | varchar(255) | ✅ |  | 
 | - |
| login_date | datetime | ✅ |  | 
 | - |
| nickname | varchar(255) | ✅ |  | 
 | - |
| avatar | varchar(255) | ✅ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| sex | int(11) | ✅ |  | 
 | - |
| birthday | datetime | ✅ |  | 
 | - |
| area_id | int(11) | ✅ |  | 
 | - |
| mark | varchar(255) | ✅ |  | 
 | - |
| point | int(11) | ✅ |  | 
 | - |
| level_id | bigint(20) | ✅ |  | 
 | - |
| experience | int(11) | ✅ |  | 
 | - |
| group_id | bigint(20) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_user_balance_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| classify | int(11) | ❌ |  | 
 | - |
| before_amount | decimal(10,2) | ❌ |  | 
 | - |
| amount | decimal(10,2) | ❌ |  | 
 | - |
| after_amount | decimal(10,2) | ❌ |  | 
 | - |
| remark | varchar(50) | ✅ |  | 
 | - |
| order_id | bigint(20) | ✅ |  | 
 | - |
| order_type | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_user_ex_balance_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| classify | int(11) | ❌ |  | 
 | - |
| before_amount | decimal(10,2) | ❌ |  | 
 | - |
| amount | decimal(10,2) | ❌ |  | 
 | - |
| after_amount | decimal(10,2) | ❌ |  | 
 | - |
| remark | varchar(50) | ✅ |  | 
 | - |
| order_id | bigint(20) | ✅ |  | 
 | - |
| order_type | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: member_user_gold_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| classify | int(11) | ❌ |  | 
 | - |
| before_amount | decimal(10,2) | ❌ |  | 
 | - |
| amount | decimal(10,2) | ❌ |  | 
 | - |
| after_amount | decimal(10,2) | ❌ |  | 
 | - |
| remark | varchar(50) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |
| order_id | bigint(20) | ✅ |  | 
 | - |
| order_type | int(11) | ✅ |  | 
 | - |

---

### 表: mp_account

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(100) | ✅ |  | 
 | - |
| account | varchar(100) | ✅ |  | 
 | - |
| app_id | varchar(100) | ✅ |  | 
 | - |
| app_secret | varchar(100) | ✅ |  | 
 | - |
| url | varchar(100) | ✅ |  | 
 | - |
| token | varchar(100) | ✅ |  | 
 | - |
| aes_key | varchar(300) | ✅ |  | 
 | - |
| qr_code_url | varchar(200) | ✅ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| status | tinyint(4) | ✅ |  | 
 | - |
| push_status | tinyint(4) | ✅ |  | 
 | - |
| type | tinyint(4) | ✅ |  | 
 | - |
| pay_channel | varchar(255) | ✅ |  | 
 | - |
| bind_author_id | bigint(20) | ✅ |  | 
 | - |

---

### 表: mp_auto_reply

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| app_id | varchar(128) | ❌ |  | 
 | - |
| type | tinyint(4) | ❌ |  | 
 | - |
| request_keyword | varchar(255) | ✅ |  | 
 | - |
| request_match | tinyint(4) | ✅ |  | 
 | - |
| request_message_type | varchar(32) | ✅ |  | 
 | - |
| response_message_type | varchar(32) | ❌ |  | 
 | - |
| response_content | varchar(1024) | ✅ |  | 
 | - |
| response_media_id | varchar(128) | ✅ |  | 
 | - |
| response_media_url | varchar(1024) | ✅ |  | 
 | - |
| response_title | varchar(128) | ✅ |  | 
 | - |
| response_description | varchar(256) | ✅ |  | 
 | - |
| response_thumb_media_id | varchar(128) | ✅ |  | 
 | - |
| response_thumb_media_url | varchar(1024) | ✅ |  | 
 | - |
| response_articles | varchar(1024) | ✅ |  | 
 | - |
| response_music_url | varchar(1024) | ✅ |  | 
 | - |
| response_hq_music_url | varchar(1024) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: mp_click_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| day | date | ❌ |  | 
 | - |
| count | int(11) | ❌ |  | 
 | - |
| type | int(11) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: mp_material

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| app_id | varchar(128) | ❌ |  | 
 | - |
| media_id | varchar(128) | ❌ |  | 
 | - |
| type | varchar(32) | ❌ |  | 
 | - |
| permanent | bit(1) | ❌ |  | 
 | - |
| url | varchar(1024) | ✅ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| mp_url | varchar(1024) | ✅ |  | 
 | - |
| title | varchar(255) | ✅ |  | 
 | - |
| introduction | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: mp_menu

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| app_id | varchar(128) | ❌ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| menu_key | varchar(255) | ✅ |  | 
 | - |
| parent_id | varchar(32) | ✅ |  | 
 | - |
| type | varchar(32) | ❌ |  |  | - |
| url | varchar(500) | ✅ |  | 
 | - |
| mini_program_app_id | varchar(32) | ✅ |  | 
 | - |
| mini_program_page_path | varchar(200) | ✅ |  | 
 | - |
| article_id | varchar(200) | ✅ |  | 
 | - |
| reply_message_type | varchar(32) | ✅ |  | 
 | - |
| reply_content | varchar(1024) | ✅ |  | 
 | - |
| reply_media_id | varchar(128) | ✅ |  | 
 | - |
| reply_media_url | varchar(1024) | ✅ |  | 
 | - |
| reply_title | varchar(128) | ✅ |  | 
 | - |
| reply_description | varchar(256) | ✅ |  | 
 | - |
| reply_thumb_media_id | varchar(128) | ✅ |  | 
 | - |
| reply_thumb_media_url | varchar(1024) | ✅ |  | 
 | - |
| reply_articles | varchar(1024) | ✅ |  | 
 | - |
| reply_music_url | varchar(1024) | ✅ |  | 
 | - |
| reply_hq_music_url | varchar(1024) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: mp_message

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| msg_id | bigint(20) | ✅ |  | 
 | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| app_id | varchar(128) | ❌ |  | 
 | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| openid | varchar(100) | ❌ |  | 
 | - |
| type | varchar(32) | ❌ |  | 
 | - |
| send_from | tinyint(4) | ❌ |  | 
 | - |
| content | varchar(1024) | ✅ |  | 
 | - |
| media_id | varchar(128) | ✅ |  | 
 | - |
| media_url | varchar(1024) | ✅ |  | 
 | - |
| recognition | varchar(1024) | ✅ |  | 
 | - |
| format | varchar(16) | ✅ |  | 
 | - |
| title | varchar(128) | ✅ |  | 
 | - |
| description | varchar(256) | ✅ |  | 
 | - |
| thumb_media_id | varchar(128) | ✅ |  | 
 | - |
| thumb_media_url | varchar(1024) | ✅ |  | 
 | - |
| url | varchar(500) | ✅ |  | 
 | - |
| location_x | double | ✅ |  | 
 | - |
| location_y | double | ✅ |  | 
 | - |
| scale | double | ✅ |  | 
 | - |
| label | varchar(128) | ✅ |  | 
 | - |
| articles | varchar(1024) | ✅ |  | 
 | - |
| music_url | varchar(1024) | ✅ |  | 
 | - |
| hq_music_url | varchar(1024) | ✅ |  | 
 | - |
| event | varchar(64) | ✅ |  | 
 | - |
| event_key | varchar(1024) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: mp_mini_user

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| correlation_id | varchar(128) | ✅ | 📇 FK | NULL | - |
| openid | varchar(100) | ❌ |  | 
 | - |
| union_id | varchar(128) | ✅ | 📇 FK | NULL | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

**索引信息**:
- index_correlationId
- index_union_id

---

### 表: mp_other_even_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| ip | varchar(50) | ❌ |  | 
 | - |
| province | varchar(50) | ✅ |  | 
 | - |
| city | varchar(50) | ✅ |  | 
 | - |
| union_id | varchar(50) | ✅ |  | 
 | - |
| open_id | varchar(50) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |

---

### 表: mp_pay_config_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| pay_channel_id | bigint(20) | ❌ |  | 
 | - |
| mp_app_id | varchar(255) | ❌ |  | 
 | - |
| result_msg | varchar(1000) | ✅ |  | 
 | - |
| sub_appid_code | int(11) | ✅ |  | 
 | - |
| sub_appid_msg | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| jsapi_msg | varchar(255) | ✅ |  | 
 | - |
| jsapi_code | int(11) | ✅ |  | 
 | - |

---

### 表: mp_tag

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tag_id | bigint(20) | ✅ |  | 
 | - |
| name | varchar(32) | ✅ |  | 
 | - |
| count | int(11) | ✅ |  | 
 | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| app_id | varchar(128) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: mp_template_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| appid | varchar(50) | ❌ |  |  | - |
| template_type | varchar(50) | ❌ |  | 
 | - |
| template_id | varchar(255) | ❌ |  | 
 | - |
| template_filed | longtext | ✅ |  | 
 | - |
| name | varchar(100) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| status | tinyint(4) | ✅ |  | 
 | - |

---

### 表: mp_user

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| openid | varchar(100) | ❌ |  | 
 | - |
| correlation_id | varchar(128) | ❌ | 📇 FK | NULL | - |
| union_id | varchar(128) | ✅ | 📇 FK | NULL | - |
| plat | bigint(20) | ❌ |  | 
 | - |
| subscribe_status | tinyint(4) | ❌ |  | 
 | - |
| subscribe_time | datetime | ❌ |  | 
 | - |
| nickname | varchar(64) | ✅ |  | 
 | - |
| head_image_url | varchar(1024) | ✅ |  | 
 | - |
| unsubscribe_time | datetime | ✅ |  | 
 | - |
| language | varchar(30) | ✅ |  | 
 | - |
| country | varchar(30) | ✅ |  | 
 | - |
| province | varchar(30) | ✅ |  | 
 | - |
| city | varchar(30) | ✅ |  | 
 | - |
| remark | varchar(128) | ✅ |  | 
 | - |
| tag_ids | varchar(255) | ✅ |  | 
 | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| app_id | varchar(128) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| push_account_status | tinyint(4) | ✅ |  | 
 | - |
| bind_user_id | bigint(20) | ✅ |  | 
 | - |

**索引信息**:
- index_correlationId
- index_union_id

---

### 表: partner_audit

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| idcard_img_front | varchar(500) | ✅ |  | 
 | - |
| idcard_img_back | varchar(500) | ✅ |  | 
 | - |
| headshot | varchar(255) | ✅ |  | 
 | - |
| phone | varchar(255) | ✅ |  | 
 | - |
| id_card | varchar(255) | ✅ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ❌ |  | 
 | - |
| fail_reason | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: partner_commission_rate

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| rate | decimal(8,2) | ❌ |  | 
 | - |
| limit | int(11) | ❌ |  | 
 | - |
| max | decimal(10,2) | ❌ |  | 
 | - |
| min | decimal(10,2) | ❌ |  | 
 | - |
| days | int(10) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: partner_divide_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| partner_id | bigint(20) | ✅ |  | 
 | - |
| partner_divide | int(3) | ✅ |  | 
 | - |
| author_divide | int(3) | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ✅ |  | 
 | - |

---

### 表: partner_info

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| phone | varchar(255) | ❌ |  | 
 | - |
| id_card | varchar(255) | ❌ |  | 
 | - |
| idcard_img_front | varchar(500) | ✅ |  | 
 | - |
| idcard_img_back | varchar(500) | ✅ |  | 
 | - |
| headshot | varchar(255) | ✅ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| status | int(10) | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |

---

### 表: partner_invite_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| partner_id | bigint(20) | ❌ |  | 
 | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| type | int(10) | ❌ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: partner_settlement_info

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| settle_type | bigint(20) | ❌ |  | 
 | - |
| account | varchar(50) | ❌ |  | 
 | - |
| name | varchar(20) | ❌ |  | 
 | - |
| id_no | varchar(50) | ❌ |  | 
 | - |
| bank_id | bigint(20) | ✅ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| update_time | datetime | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ❌ |  | 
 | - |

---

### 表: partner_withdraw_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| settlement_id | bigint(20) | ❌ |  | 
 | - |
| amount | decimal(10,2) | ❌ |  | 
 | - |
| commission | decimal(10,2) | ❌ |  | 
 | - |
| received | decimal(10,2) | ❌ |  | 
 | - |
| status | int(10) | ❌ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_app

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ | 📇 FK | 0 | - |
| app_key | varchar(255) | ✅ |  | 
 | - |
| name | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| order_notify_url | varchar(255) | ✅ |  | 
 | - |
| refund_notify_url | varchar(255) | ✅ |  | 
 | - |
| transfer_notify_url | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

**索引信息**:
- idx_pay_app_tenant_app_key
- idx_pay_app_tenant_id
- idx_pay_app_tenant_status

---

### 表: pay_bank_info

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| bank_no | varchar(50) | ✅ |  | 
 | - |
| bank_name | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| update_time | datetime | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ❌ |  | 
 | - |

---

### 表: pay_channel

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| code | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| fee_rate | double | ✅ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| app_id | bigint(20) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_demo_order

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| spu_id | bigint(20) | ✅ |  | 
 | - |
| spu_name | varchar(255) | ✅ |  | 
 | - |
| price | int(11) | ✅ |  | 
 | - |
| pay_status | tinyint(1) | ✅ |  | 
 | - |
| pay_order_id | bigint(20) | ✅ |  | 
 | - |
| pay_time | datetime | ✅ |  | 
 | - |
| pay_channel_code | varchar(255) | ✅ |  | 
 | - |
| pay_refund_id | bigint(20) | ✅ |  | 
 | - |
| refund_price | int(11) | ✅ |  | 
 | - |
| refund_time | datetime | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_demo_transfer

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| app_id | bigint(20) | ✅ |  | 
 | - |
| type | int(11) | ✅ |  | 
 | - |
| price | int(11) | ✅ |  | 
 | - |
| user_name | varchar(255) | ✅ |  | 
 | - |
| alipay_logon_id | varchar(255) | ✅ |  | 
 | - |
| openid | varchar(255) | ✅ |  | 
 | - |
| transfer_status | int(11) | ✅ |  | 
 | - |
| pay_transfer_id | bigint(20) | ✅ |  | 
 | - |
| pay_channel_code | varchar(255) | ✅ |  | 
 | - |
| transfer_time | datetime | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_notify_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| task_id | bigint(20) | ✅ |  | 
 | - |
| notify_times | int(11) | ✅ |  | 
 | - |
| response | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_notify_task

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| app_id | bigint(20) | ✅ |  | 
 | - |
| type | int(11) | ✅ |  | 
 | - |
| data_id | bigint(20) | ✅ |  | 
 | - |
| merchant_order_id | varchar(255) | ✅ |  | 
 | - |
| merchant_transfer_id | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| next_notify_time | datetime | ✅ |  | 
 | - |
| last_execute_time | datetime | ✅ |  | 
 | - |
| notify_times | int(11) | ✅ |  | 
 | - |
| max_notify_times | int(11) | ✅ |  | 
 | - |
| notify_url | varchar(255) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_order

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ | 📇 FK | 0 | - |
| app_id | bigint(20) | ✅ |  | 
 | - |
| channel_id | bigint(20) | ✅ |  | 
 | - |
| channel_code | varchar(255) | ✅ |  | 
 | - |
| merchant_order_id | varchar(255) | ✅ |  | 
 | - |
| subject | varchar(255) | ✅ |  | 
 | - |
| body | varchar(255) | ✅ |  | 
 | - |
| notify_url | varchar(255) | ✅ |  | 
 | - |
| price | int(11) | ✅ |  | 
 | - |
| channel_fee_rate | decimal(10,2) | ✅ |  | 
 | - |
| channel_fee_price | int(11) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| user_ip | varchar(255) | ✅ |  | 
 | - |
| expire_time | datetime | ✅ |  | 
 | - |
| success_time | datetime | ✅ |  | 
 | - |
| extension_id | bigint(20) | ✅ |  | 
 | - |
| no | varchar(255) | ✅ |  | 
 | - |
| refund_price | int(11) | ✅ |  | 
 | - |
| channel_user_id | varchar(255) | ✅ |  | 
 | - |
| channel_order_no | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

**索引信息**:
- idx_pay_order_tenant_app_id
- idx_pay_order_tenant_create_time
- idx_pay_order_tenant_id
- idx_pay_order_tenant_merchant_order_id
- idx_pay_order_tenant_status
- idx_pay_order_tenant_success_time

---

### 表: pay_order_extension

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| no | varchar(255) | ✅ |  | 
 | - |
| order_id | bigint(20) | ✅ |  | 
 | - |
| channel_id | bigint(20) | ✅ |  | 
 | - |
| channel_code | varchar(255) | ✅ |  | 
 | - |
| user_ip | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| channel_error_code | varchar(255) | ✅ |  | 
 | - |
| channel_error_msg | varchar(255) | ✅ |  | 
 | - |
| channel_notify_data | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_refund

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ | 📇 FK | 0 | - |
| no | varchar(255) | ✅ |  | 
 | - |
| app_id | bigint(20) | ✅ |  | 
 | - |
| channel_id | bigint(20) | ✅ |  | 
 | - |
| channel_code | varchar(255) | ✅ |  | 
 | - |
| order_id | bigint(20) | ✅ |  | 
 | - |
| order_no | varchar(255) | ✅ |  | 
 | - |
| merchant_order_id | varchar(255) | ✅ |  | 
 | - |
| merchant_refund_id | varchar(255) | ✅ |  | 
 | - |
| notify_url | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| pay_price | int(11) | ✅ |  | 
 | - |
| refund_price | int(11) | ✅ |  | 
 | - |
| reason | varchar(255) | ✅ |  | 
 | - |
| user_ip | varchar(255) | ✅ |  | 
 | - |
| channel_order_no | varchar(255) | ✅ |  | 
 | - |
| channel_refund_no | varchar(255) | ✅ |  | 
 | - |
| success_time | datetime | ✅ |  | 
 | - |
| channel_error_code | varchar(255) | ✅ |  | 
 | - |
| channel_error_msg | varchar(255) | ✅ |  | 
 | - |
| channel_notify_data | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

**索引信息**:
- idx_pay_refund_tenant_create_time
- idx_pay_refund_tenant_id
- idx_pay_refund_tenant_order_id
- idx_pay_refund_tenant_status

---

### 表: pay_transfer

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| no | varchar(255) | ✅ |  | 
 | - |
| app_id | bigint(20) | ✅ |  | 
 | - |
| channel_id | bigint(20) | ✅ |  | 
 | - |
| channel_code | varchar(255) | ✅ |  | 
 | - |
| merchant_transfer_id | varchar(255) | ✅ |  | 
 | - |
| type | int(11) | ✅ |  | 
 | - |
| subject | varchar(255) | ✅ |  | 
 | - |
| price | int(11) | ✅ |  | 
 | - |
| user_name | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| success_time | datetime | ✅ |  | 
 | - |
| alipay_logon_id | varchar(255) | ✅ |  | 
 | - |
| openid | varchar(255) | ✅ |  | 
 | - |
| notify_url | varchar(255) | ✅ |  | 
 | - |
| user_ip | varchar(255) | ✅ |  | 
 | - |
| channel_transfer_no | varchar(255) | ✅ |  | 
 | - |
| channel_error_code | varchar(255) | ✅ |  | 
 | - |
| channel_error_msg | varchar(255) | ✅ |  | 
 | - |
| channel_notify_data | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_wallet

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ | 📇 FK | 0 | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| user_type | int(11) | ✅ |  | 
 | - |
| balance | int(11) | ✅ |  | 
 | - |
| freeze_price | int(11) | ✅ |  | 
 | - |
| total_expense | int(11) | ✅ |  | 
 | - |
| total_recharge | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

**索引信息**:
- idx_pay_wallet_tenant_id
- idx_pay_wallet_tenant_user

---

### 表: pay_wallet_recharge

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| wallet_id | bigint(20) | ✅ |  | 
 | - |
| total_price | int(11) | ✅ |  | 
 | - |
| pay_price | int(11) | ✅ |  | 
 | - |
| bonus_price | int(11) | ✅ |  | 
 | - |
| package_id | bigint(20) | ✅ |  | 
 | - |
| pay_status | tinyint(1) | ✅ |  | 
 | - |
| pay_order_id | bigint(20) | ✅ |  | 
 | - |
| pay_channel_code | varchar(255) | ✅ |  | 
 | - |
| pay_time | datetime | ✅ |  | 
 | - |
| pay_refund_id | bigint(20) | ✅ |  | 
 | - |
| refund_total_price | int(11) | ✅ |  | 
 | - |
| refund_pay_price | int(11) | ✅ |  | 
 | - |
| refund_bonus_price | int(11) | ✅ |  | 
 | - |
| refund_time | datetime | ✅ |  | 
 | - |
| refund_status | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_wallet_recharge_package

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(255) | ✅ |  | 
 | - |
| pay_price | int(11) | ✅ |  | 
 | - |
| bonus_price | int(11) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: pay_wallet_transaction

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| tenant_id | bigint(20) | ❌ | 📇 FK | 0 | - |
| no | varchar(255) | ✅ |  | 
 | - |
| wallet_id | bigint(20) | ✅ |  | 
 | - |
| biz_type | int(11) | ✅ |  | 
 | - |
| biz_id | varchar(255) | ✅ |  | 
 | - |
| title | varchar(255) | ✅ |  | 
 | - |
| price | int(11) | ✅ |  | 
 | - |
| balance | int(11) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

**索引信息**:
- idx_pay_wallet_transaction_tenant_create_time
- idx_pay_wallet_transaction_tenant_id
- idx_pay_wallet_transaction_tenant_wallet

---

### 表: play_type

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(100) | ✅ |  | 
 | - |
| status | tinyint(4) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: playing_method

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(100) | ✅ |  | 
 | - |
| status | tinyint(4) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |
| type | varchar(100) | ✅ |  | 
 | - |
| result_type | int(3) | ✅ |  | 
 | - |
| result_num | int(3) | ✅ |  | 
 | - |

---

### 表: privilege_order

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| order_no | varchar(100) | ❌ |  | 
 | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| privilege_id | bigint(20) | ❌ |  | 
 | - |
| type | int(11) | ❌ |  | 
 | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| amount | decimal(10,2) | ❌ |  | 
 | - |
| pay_amount | decimal(10,2) | ❌ |  | 
 | - |
| author_divide | decimal(10,2) | ❌ |  | 
 | - |
| sharer_divide | decimal(10,2) | ❌ |  | 
 | - |
| partner_divide | decimal(10,2) | ❌ |  | 
 | - |
| pay_type | int(11) | ❌ |  | 
 | - |
| pay_time | datetime | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| third_party_no | varchar(100) | ✅ |  | 
 | - |
| channel_order_sn | varchar(100) | ✅ |  | 
 | - |
| ins_order_sn | varchar(100) | ✅ |  | 
 | - |
| pay_app_id | varchar(255) | ✅ |  | 
 | - |
| pay_app_type | int(10) | ✅ |  | 
 | - |
| privilege_num | int(10) | ✅ |  | 
 | - |
| match_type | int(10) | ✅ |  | 
 | - |

---

### 表: push_amount_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| amount | decimal(10,2) | ❌ |  | 
 | - |
| gift_ratio | int(10) | ✅ |  | 
 | - |
| commission_ratio | int(10) | ✅ |  | 
 | - |
| partner_ratio | int(11) | ✅ |  | 
 | - |
| author_ratio | int(11) | ✅ |  | 
 | - |
| partner_author_ratio | int(11) | ✅ |  | 
 | - |
| plat_ratio | int(11) | ✅ |  | 
 | - |
| partner_plat_ratio | int(11) | ✅ |  | 
 | - |

---

### 表: qrtz_blob_triggers

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_NAME | varchar(190) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_GROUP | varchar(190) | ❌ | 🔑 PK | NULL | - |
| BLOB_DATA | blob | ✅ |  | 
 | - |

**索引信息**:
- SCHED_NAME

---

### 表: qrtz_calendars

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| CALENDAR_NAME | varchar(190) | ❌ | 🔑 PK | NULL | - |
| CALENDAR | blob | ❌ |  | 
 | - |

---

### 表: qrtz_cron_triggers

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_NAME | varchar(190) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_GROUP | varchar(190) | ❌ | 🔑 PK | NULL | - |
| CRON_EXPRESSION | varchar(120) | ❌ |  | 
 | - |
| TIME_ZONE_ID | varchar(80) | ✅ |  | 
 | - |

---

### 表: qrtz_fired_triggers

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| ENTRY_ID | varchar(95) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_NAME | varchar(190) | ❌ |  | 
 | - |
| TRIGGER_GROUP | varchar(190) | ❌ |  | 
 | - |
| INSTANCE_NAME | varchar(190) | ❌ |  | 
 | - |
| FIRED_TIME | bigint(20) | ❌ |  | 
 | - |
| SCHED_TIME | bigint(20) | ❌ |  | 
 | - |
| PRIORITY | int(11) | ❌ |  | 
 | - |
| STATE | varchar(16) | ❌ |  | 
 | - |
| JOB_NAME | varchar(190) | ✅ |  | 
 | - |
| JOB_GROUP | varchar(190) | ✅ |  | 
 | - |
| IS_NONCONCURRENT | varchar(1) | ✅ |  | 
 | - |
| REQUESTS_RECOVERY | varchar(1) | ✅ |  | 
 | - |

**索引信息**:
- IDX_QRTZ_FT_INST_JOB_REQ_RCVRY
- IDX_QRTZ_FT_J_G
- IDX_QRTZ_FT_JG
- IDX_QRTZ_FT_T_G
- IDX_QRTZ_FT_TG
- IDX_QRTZ_FT_TRIG_INST_NAME

---

### 表: qrtz_job_details

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| JOB_NAME | varchar(190) | ❌ | 🔑 PK | NULL | - |
| JOB_GROUP | varchar(190) | ❌ | 🔑 PK | NULL | - |
| DESCRIPTION | varchar(250) | ✅ |  | 
 | - |
| JOB_CLASS_NAME | varchar(250) | ❌ |  | 
 | - |
| IS_DURABLE | varchar(1) | ❌ |  | 
 | - |
| IS_NONCONCURRENT | varchar(1) | ❌ |  | 
 | - |
| IS_UPDATE_DATA | varchar(1) | ❌ |  | 
 | - |
| REQUESTS_RECOVERY | varchar(1) | ❌ |  | 
 | - |
| JOB_DATA | blob | ✅ |  | 
 | - |

**索引信息**:
- IDX_QRTZ_J_GRP
- IDX_QRTZ_J_REQ_RECOVERY

---

### 表: qrtz_locks

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| LOCK_NAME | varchar(40) | ❌ | 🔑 PK | NULL | - |

---

### 表: qrtz_paused_trigger_grps

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_GROUP | varchar(190) | ❌ | 🔑 PK | NULL | - |

---

### 表: qrtz_scheduler_state

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| INSTANCE_NAME | varchar(190) | ❌ | 🔑 PK | NULL | - |
| LAST_CHECKIN_TIME | bigint(20) | ❌ |  | 
 | - |
| CHECKIN_INTERVAL | bigint(20) | ❌ |  | 
 | - |

---

### 表: qrtz_simple_triggers

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_NAME | varchar(190) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_GROUP | varchar(190) | ❌ | 🔑 PK | NULL | - |
| REPEAT_COUNT | bigint(20) | ❌ |  | 
 | - |
| REPEAT_INTERVAL | bigint(20) | ❌ |  | 
 | - |
| TIMES_TRIGGERED | bigint(20) | ❌ |  | 
 | - |

---

### 表: qrtz_simprop_triggers

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_NAME | varchar(190) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_GROUP | varchar(190) | ❌ | 🔑 PK | NULL | - |
| STR_PROP_1 | varchar(512) | ✅ |  | 
 | - |
| STR_PROP_2 | varchar(512) | ✅ |  | 
 | - |
| STR_PROP_3 | varchar(512) | ✅ |  | 
 | - |
| INT_PROP_1 | int(11) | ✅ |  | 
 | - |
| INT_PROP_2 | int(11) | ✅ |  | 
 | - |
| LONG_PROP_1 | bigint(20) | ✅ |  | 
 | - |
| LONG_PROP_2 | bigint(20) | ✅ |  | 
 | - |
| DEC_PROP_1 | decimal(13,4) | ✅ |  | 
 | - |
| DEC_PROP_2 | decimal(13,4) | ✅ |  | 
 | - |
| BOOL_PROP_1 | varchar(1) | ✅ |  | 
 | - |
| BOOL_PROP_2 | varchar(1) | ✅ |  | 
 | - |

---

### 表: qrtz_triggers

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| SCHED_NAME | varchar(120) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_NAME | varchar(190) | ❌ | 🔑 PK | NULL | - |
| TRIGGER_GROUP | varchar(190) | ❌ | 🔑 PK | NULL | - |
| JOB_NAME | varchar(190) | ❌ |  | 
 | - |
| JOB_GROUP | varchar(190) | ❌ |  | 
 | - |
| DESCRIPTION | varchar(250) | ✅ |  | 
 | - |
| NEXT_FIRE_TIME | bigint(20) | ✅ |  | 
 | - |
| PREV_FIRE_TIME | bigint(20) | ✅ |  | 
 | - |
| PRIORITY | int(11) | ✅ |  | 
 | - |
| TRIGGER_STATE | varchar(16) | ❌ |  | 
 | - |
| TRIGGER_TYPE | varchar(8) | ❌ |  | 
 | - |
| START_TIME | bigint(20) | ❌ |  | 
 | - |
| END_TIME | bigint(20) | ✅ |  | 
 | - |
| CALENDAR_NAME | varchar(190) | ✅ |  | 
 | - |
| MISFIRE_INSTR | smallint(6) | ✅ |  | 
 | - |
| JOB_DATA | blob | ✅ |  | 
 | - |

**索引信息**:
- IDX_QRTZ_T_C
- IDX_QRTZ_T_G
- IDX_QRTZ_T_J
- IDX_QRTZ_T_JG
- IDX_QRTZ_T_N_G_STATE
- IDX_QRTZ_T_N_STATE
- IDX_QRTZ_T_NEXT_FIRE_TIME
- IDX_QRTZ_T_NFT_MISFIRE
- IDX_QRTZ_T_NFT_ST
- IDX_QRTZ_T_NFT_ST_MISFIRE
- IDX_QRTZ_T_NFT_ST_MISFIRE_GRP
- IDX_QRTZ_T_STATE

---

### 表: recommend_author

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| status | int(10) | ❌ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |

---

### 表: recommend_user_register_logs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| article_id | bigint(20) | ❌ |  | 
 | - |
| create_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |

---

### 表: refund_order

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 📇 FK | NULL | - |
| refund_no | varchar(50) | ❌ |  | 
 | - |
| order_type | int(11) | ❌ |  | 
 | - |
| order_id | bigint(20) | ❌ |  | 
 | - |
| order_no | varchar(50) | ❌ |  | 
 | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| amount | decimal(20,6) | ❌ |  | 
 | - |
| refund_amount | decimal(20,6) | ✅ |  | 
 | - |
| refund_fee | decimal(20,6) | ✅ |  | 
 | - |
| status | int(11) | ❌ |  | 
 | - |
| result_message | varchar(512) | ✅ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| deleted | bit(4) | ❌ |  | 
 | - |
| third_party_no | varchar(50) | ✅ |  | 
 | - |
| pay_app_id | varchar(500) | ❌ |  | 
 | - |
| finish_time | varchar(50) | ✅ |  | 
 | - |
| old_refund_no | varchar(512) | ✅ |  | 
 | - |

**索引信息**:
- 索引

---

### 表: report_operation_day

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| date | date | ❌ |  | 
 | - |
| active_user | int(11) | ❌ |  | 
 | - |
| add_user | int(11) | ❌ |  | 
 | - |
| pay_user | int(11) | ❌ |  | 
 | - |
| article_num | int(11) | ❌ |  | 
 | - |
| pay_amount | decimal(20,6) | ❌ |  | 
 | - |
| refund_amount | decimal(20,6) | ❌ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |

---

### 表: scheme_order

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| order_no | varchar(100) | ❌ | 🔒 UK | NULL | - |
| user_id | bigint(20) | ❌ | 📇 FK | NULL | - |
| article_id | bigint(20) | ❌ |  | 
 | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| amount | decimal(10,2) | ❌ |  | 
 | - |
| pay_amount | decimal(10,2) | ❌ |  | 
 | - |
| author_divide | decimal(10,2) | ❌ |  | 
 | - |
| sharer_divide | decimal(10,2) | ❌ |  | 
 | - |
| partner_divide | decimal(10,2) | ❌ |  | 
 | - |
| pay_type | int(11) | ❌ |  | 
 | - |
| pay_time | datetime | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| is_refund | int(11) | ✅ |  | 
 | - |
| refund_time | datetime | ✅ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| third_party_no | varchar(100) | ✅ |  | 
 | - |
| channel_order_sn | varchar(100) | ✅ |  | 
 | - |
| ins_order_sn | varchar(100) | ✅ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| from_article_id | bigint(20) | ✅ |  | 
 | - |
| pay_app_id | varchar(255) | ✅ |  | 
 | - |
| user_privilege_id | bigint(20) | ✅ |  | 
 | - |
| pay_app_type | int(10) | ✅ |  | 
 | - |
| origin | int(10) | ❌ |  | 
 | - |
| is_mark | int(10) | ❌ |  | 
 | - |
| real_divide_amount | decimal(10,2) | ✅ |  | 
 | - |

**索引信息**:
- order_no
- user_id

---

### 表: scheme_play

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(100) | ✅ |  | 
 | - |
| play | varchar(100) | ✅ |  | 
 | - |
| session | int(3) | ✅ |  | 
 | - |
| multiple | tinyint(3) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |
| status | tinyint(4) | ✅ |  | 
 | - |
| data_type | tinyint(4) | ✅ |  | 
 | - |
| bundled | tinyint(4) | ✅ |  | 
 | - |

---

### 表: sys_configs

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK | NULL | - |
| config_key | varchar(50) | ❌ | 🔒 UK | NULL | - |
| config_value | varchar(255) | ❌ |  | 
 | - |
| description | varchar(255) | ✅ |  | 
 | - |
| created_at | timestamp | ❌ |  | 
 | - |
| updated_at | timestamp | ❌ |  | on update CURRENT_TIMESTAMP
 | - |

**索引信息**:
- idx_key

---

### 表: system_command

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| keywords | varchar(255) | ❌ |  | 
 | - |
| api_path | varchar(255) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_dept

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(30) | ❌ |  |  | - |
| parent_id | bigint(20) | ❌ |  | 
 | - |
| sort | int(11) | ❌ |  | 
 | - |
| leader_user_id | bigint(20) | ✅ |  | 
 | - |
| phone | varchar(11) | ✅ |  | 
 | - |
| email | varchar(50) | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_dict_data

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| sort | int(11) | ❌ |  | 
 | - |
| label | varchar(100) | ❌ |  |  | - |
| value | varchar(100) | ❌ |  |  | - |
| dict_type | varchar(100) | ❌ |  |  | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| color_type | varchar(100) | ✅ |  |  | - |
| css_class | varchar(100) | ✅ |  |  | - |
| remark | varchar(500) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_dict_type

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(100) | ❌ |  |  | - |
| type | varchar(100) | ❌ |  |  | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| remark | varchar(500) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| deleted_time | datetime | ✅ |  | 
 | - |

---

### 表: system_login_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| log_type | bigint(20) | ❌ |  | 
 | - |
| trace_id | varchar(64) | ❌ |  |  | - |
| user_id | bigint(20) | ❌ | 📇 FK | 0 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| username | varchar(50) | ❌ |  |  | - |
| result | tinyint(4) | ❌ |  | 
 | - |
| user_ip | varchar(50) | ❌ |  | 
 | - |
| user_agent | varchar(512) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ | 📇 FK | CURRENT_TIMESTAMP | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

**索引信息**:
- create_time
- user_id

---

### 表: system_mail_account

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| mail | varchar(255) | ❌ |  | 
 | - |
| username | varchar(255) | ❌ |  | 
 | - |
| password | varchar(255) | ❌ |  | 
 | - |
| host | varchar(255) | ❌ |  | 
 | - |
| port | int(11) | ❌ |  | 
 | - |
| ssl_enable | bit(1) | ❌ |  | 
 | - |
| starttls_enable | bit(1) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_mail_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| user_type | tinyint(4) | ✅ |  | 
 | - |
| to_mail | varchar(255) | ❌ |  | 
 | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| from_mail | varchar(255) | ❌ |  | 
 | - |
| template_id | bigint(20) | ❌ |  | 
 | - |
| template_code | varchar(63) | ❌ |  | 
 | - |
| template_nickname | varchar(255) | ✅ |  | 
 | - |
| template_title | varchar(255) | ❌ |  | 
 | - |
| template_content | varchar(10240) | ❌ |  | 
 | - |
| template_params | varchar(255) | ❌ |  | 
 | - |
| send_status | tinyint(4) | ❌ |  | 
 | - |
| send_time | datetime | ✅ |  | 
 | - |
| send_message_id | varchar(255) | ✅ |  | 
 | - |
| send_exception | varchar(4096) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_mail_template

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(63) | ❌ |  | 
 | - |
| code | varchar(63) | ❌ |  | 
 | - |
| account_id | bigint(20) | ❌ |  | 
 | - |
| nickname | varchar(255) | ✅ |  | 
 | - |
| title | varchar(255) | ❌ |  | 
 | - |
| content | varchar(10240) | ❌ |  | 
 | - |
| params | varchar(255) | ❌ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_menu

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(50) | ❌ |  | 
 | - |
| permission | varchar(100) | ❌ |  |  | - |
| type | tinyint(4) | ❌ |  | 
 | - |
| sort | int(11) | ❌ |  | 
 | - |
| parent_id | bigint(20) | ❌ |  | 
 | - |
| path | varchar(200) | ✅ |  |  | - |
| icon | varchar(100) | ✅ |  | 
 | - |
| component | varchar(255) | ✅ |  | 
 | - |
| component_name | varchar(255) | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| visible | bit(1) | ❌ |  | 
 | - |
| keep_alive | bit(1) | ❌ |  | 
 | - |
| always_show | bit(1) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_notice

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| title | varchar(50) | ❌ |  | 
 | - |
| content | text | ❌ |  | 
 | - |
| type | tinyint(4) | ❌ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_notify_message

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| template_id | bigint(20) | ❌ |  | 
 | - |
| template_code | varchar(64) | ❌ |  | 
 | - |
| template_nickname | varchar(63) | ❌ |  | 
 | - |
| template_content | varchar(1024) | ❌ |  | 
 | - |
| template_type | int(11) | ❌ |  | 
 | - |
| template_params | varchar(255) | ❌ |  | 
 | - |
| read_status | bit(1) | ❌ |  | 
 | - |
| read_time | datetime | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_notify_template

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(63) | ❌ |  | 
 | - |
| code | varchar(64) | ❌ |  | 
 | - |
| nickname | varchar(255) | ❌ |  | 
 | - |
| content | varchar(1024) | ❌ |  | 
 | - |
| type | tinyint(4) | ❌ |  | 
 | - |
| params | varchar(255) | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_oauth2_access_token

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| user_info | varchar(512) | ✅ |  | 
 | - |
| access_token | varchar(255) | ❌ | 📇 FK | NULL | - |
| refresh_token | varchar(32) | ❌ | 📇 FK | NULL | - |
| client_id | varchar(255) | ❌ |  | 
 | - |
| scopes | varchar(255) | ✅ |  | 
 | - |
| expires_time | datetime | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

**索引信息**:
- idx_access_token
- idx_refresh_token

---

### 表: system_oauth2_approve

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| client_id | varchar(255) | ❌ |  | 
 | - |
| scope | varchar(255) | ❌ |  |  | - |
| approved | bit(1) | ❌ |  | 
 | - |
| expires_time | datetime | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_oauth2_client

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| client_id | varchar(255) | ❌ |  | 
 | - |
| secret | varchar(255) | ❌ |  | 
 | - |
| name | varchar(255) | ❌ |  | 
 | - |
| logo | varchar(255) | ❌ |  | 
 | - |
| description | varchar(255) | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| access_token_validity_seconds | int(11) | ❌ |  | 
 | - |
| refresh_token_validity_seconds | int(11) | ❌ |  | 
 | - |
| redirect_uris | varchar(255) | ❌ |  | 
 | - |
| authorized_grant_types | varchar(255) | ❌ |  | 
 | - |
| scopes | varchar(255) | ✅ |  | 
 | - |
| auto_approve_scopes | varchar(255) | ✅ |  | 
 | - |
| authorities | varchar(255) | ✅ |  | 
 | - |
| resource_ids | varchar(255) | ✅ |  | 
 | - |
| additional_information | varchar(4096) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_oauth2_code

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| code | varchar(32) | ❌ |  | 
 | - |
| client_id | varchar(255) | ❌ |  | 
 | - |
| scopes | varchar(255) | ✅ |  |  | - |
| expires_time | datetime | ❌ |  | 
 | - |
| redirect_uri | varchar(255) | ✅ |  | 
 | - |
| state | varchar(255) | ❌ |  |  | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_oauth2_refresh_token

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| refresh_token | varchar(32) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| client_id | varchar(255) | ❌ |  | 
 | - |
| scopes | varchar(255) | ✅ |  | 
 | - |
| expires_time | datetime | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_operate_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| trace_id | varchar(64) | ❌ |  |  | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| type | varchar(50) | ❌ |  | 
 | - |
| sub_type | varchar(50) | ❌ |  | 
 | - |
| biz_id | bigint(20) | ❌ |  | 
 | - |
| action | varchar(2000) | ❌ |  |  | - |
| extra | varchar(2000) | ❌ |  |  | - |
| request_method | varchar(16) | ✅ |  |  | - |
| request_url | varchar(255) | ✅ |  |  | - |
| user_ip | varchar(50) | ✅ |  | 
 | - |
| user_agent | varchar(200) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_post

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| code | varchar(64) | ❌ |  | 
 | - |
| name | varchar(50) | ❌ |  | 
 | - |
| sort | int(11) | ❌ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| remark | varchar(500) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_role

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(30) | ❌ |  | 
 | - |
| code | varchar(100) | ❌ |  | 
 | - |
| post_id | bigint(20) | ✅ |  | 
 | - |
| sort | int(11) | ❌ |  | 
 | - |
| data_scope | tinyint(4) | ❌ |  | 
 | - |
| data_scope_dept_ids | varchar(500) | ❌ |  |  | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| type | tinyint(4) | ❌ |  | 
 | - |
| remark | varchar(500) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_role_menu

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| role_id | bigint(20) | ❌ |  | 
 | - |
| menu_id | bigint(20) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_sensitive_word

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(255) | ❌ |  | 
 | - |
| description | varchar(512) | ✅ |  | 
 | - |
| tags | varchar(255) | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_sms_channel

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| signature | varchar(12) | ❌ |  | 
 | - |
| code | varchar(63) | ❌ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| api_key | varchar(128) | ❌ |  | 
 | - |
| api_secret | varchar(128) | ✅ |  | 
 | - |
| callback_url | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_sms_code

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| mobile | varchar(11) | ❌ | 📇 FK | NULL | - |
| code | varchar(6) | ❌ |  | 
 | - |
| create_ip | varchar(15) | ❌ |  | 
 | - |
| scene | tinyint(4) | ❌ |  | 
 | - |
| today_index | tinyint(4) | ❌ |  | 
 | - |
| used | tinyint(4) | ❌ |  | 
 | - |
| used_time | datetime | ✅ |  | 
 | - |
| used_ip | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

**索引信息**:
- idx_mobile

---

### 表: system_sms_log

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| channel_id | bigint(20) | ❌ |  | 
 | - |
| channel_code | varchar(63) | ❌ |  | 
 | - |
| template_id | bigint(20) | ❌ |  | 
 | - |
| template_code | varchar(63) | ❌ |  | 
 | - |
| template_type | tinyint(4) | ❌ |  | 
 | - |
| template_content | varchar(255) | ❌ |  | 
 | - |
| template_params | varchar(255) | ❌ |  | 
 | - |
| api_template_id | varchar(63) | ❌ |  | 
 | - |
| mobile | varchar(11) | ❌ |  | 
 | - |
| user_id | bigint(20) | ✅ |  | 
 | - |
| user_type | tinyint(4) | ✅ |  | 
 | - |
| send_status | tinyint(4) | ❌ |  | 
 | - |
| send_time | datetime | ✅ |  | 
 | - |
| api_send_code | varchar(63) | ✅ |  | 
 | - |
| api_send_msg | varchar(255) | ✅ |  | 
 | - |
| api_request_id | varchar(255) | ✅ |  | 
 | - |
| api_serial_no | varchar(255) | ✅ |  | 
 | - |
| receive_status | tinyint(4) | ❌ |  | 
 | - |
| receive_time | datetime | ✅ |  | 
 | - |
| api_receive_code | varchar(63) | ✅ |  | 
 | - |
| api_receive_msg | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_sms_template

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| type | tinyint(4) | ❌ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| code | varchar(63) | ❌ |  | 
 | - |
| name | varchar(63) | ❌ |  | 
 | - |
| content | varchar(255) | ❌ |  | 
 | - |
| params | varchar(255) | ❌ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| api_template_id | varchar(63) | ❌ |  | 
 | - |
| channel_id | bigint(20) | ❌ |  | 
 | - |
| channel_code | varchar(63) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_social_client

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(255) | ❌ |  | 
 | - |
| social_type | tinyint(4) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| client_id | varchar(255) | ❌ |  | 
 | - |
| client_secret | varchar(255) | ❌ |  | 
 | - |
| agent_id | varchar(255) | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_social_user

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK | NULL | - |
| type | tinyint(4) | ❌ |  | 
 | - |
| openid | varchar(32) | ❌ |  | 
 | - |
| token | varchar(256) | ✅ |  | 
 | - |
| raw_token_info | varchar(1024) | ❌ |  | 
 | - |
| nickname | varchar(32) | ❌ |  | 
 | - |
| avatar | varchar(255) | ✅ |  | 
 | - |
| raw_user_info | varchar(1024) | ❌ |  | 
 | - |
| code | varchar(256) | ❌ |  | 
 | - |
| state | varchar(256) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_social_user_bind

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| user_type | tinyint(4) | ❌ |  | 
 | - |
| social_type | tinyint(4) | ❌ |  | 
 | - |
| social_user_id | bigint(20) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_tenant

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(30) | ❌ |  | 
 | - |
| contact_user_id | bigint(20) | ✅ |  | 
 | - |
| contact_name | varchar(30) | ❌ |  | 
 | - |
| contact_mobile | varchar(500) | ✅ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| website | varchar(256) | ✅ |  |  | - |
| package_id | bigint(20) | ❌ |  | 
 | - |
| expire_time | datetime | ❌ |  | 
 | - |
| account_count | int(11) | ❌ |  | 
 | - |
| creator | varchar(64) | ❌ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_tenant_package

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(30) | ❌ |  | 
 | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| remark | varchar(256) | ✅ |  |  | - |
| menu_ids | varchar(4096) | ❌ |  | 
 | - |
| creator | varchar(64) | ❌ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: system_user_post

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| post_id | bigint(20) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_user_role

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | bigint(20) | ❌ |  | 
 | - |
| role_id | bigint(20) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ✅ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ✅ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: system_users

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| username | varchar(30) | ❌ |  | 
 | - |
| password | varchar(100) | ❌ |  |  | - |
| nickname | varchar(30) | ❌ |  | 
 | - |
| remark | varchar(500) | ✅ |  | 
 | - |
| dept_id | bigint(20) | ✅ |  | 
 | - |
| post_ids | varchar(255) | ✅ |  | 
 | - |
| email | varchar(50) | ✅ |  |  | - |
| mobile | varchar(11) | ✅ |  |  | - |
| sex | tinyint(4) | ✅ |  | 
 | - |
| avatar | varchar(512) | ✅ |  |  | - |
| status | tinyint(4) | ❌ |  | 
 | - |
| login_ip | varchar(50) | ✅ |  |  | - |
| login_date | datetime | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
| author_ids | varchar(255) | ✅ |  | 
 | - |

---

### 表: third_pay_channel

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(255) | ❌ |  | 
 | - |
| app_id | varchar(255) | ❌ |  | 
 | - |
| app_secret | varchar(255) | ❌ |  | 
 | - |
| store_id | varchar(255) | ❌ |  | 
 | - |
| merchant_code | varchar(255) | ✅ |  | 
 | - |
| status | int(10) | ❌ |  | 
 | - |
| channel_name | varchar(255) | ✅ |  | 
 | - |
| channel_merchant_id | varchar(255) | ✅ |  | 
 | - |
| organization_name | varchar(255) | ✅ |  | 
 | - |
| create_time | datetime | ✅ |  | 
 | - |
| update_time | datetime | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| deleted | tinyint(1) | ✅ |  | 
 | - |

---

### 表: third_pay_sqb_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| vendor_sn | varchar(255) | ❌ |  | 
 | - |
| vendor_key | varchar(255) | ❌ |  | 
 | - |
| app_id | varchar(255) | ❌ |  | 
 | - |
| code | varchar(255) | ❌ |  | 
 | - |
| type | int(10) | ❌ |  | 
 | - |
| status | int(10) | ✅ |  | 
 | - |
| terminal_sn | varchar(255) | ✅ |  | 
 | - |
| terminal_key | varchar(255) | ✅ |  | 
 | - |
| remark | varchar(255) | ✅ |  | 
 | - |
| creator | varchar(255) | ✅ |  | 
 | - |
| updater | varchar(255) | ✅ |  | 
 | - |
| create_time | timestamp | ✅ |  | 
 | - |
| update_time | timestamp | ✅ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | tinyint(4) | ✅ |  | 
 | - |

---

### 表: wecom_setting

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| login_url | varchar(255) | ✅ |  | 
 | - |
| appid | varchar(255) | ✅ |  | 
 | - |
| secret | varchar(255) | ✅ |  | 
 | - |
| login_web | varchar(255) | ✅ |  | 
 | - |
| corpid | varchar(255) | ✅ |  | 
 | - |
| call_token | varchar(255) | ✅ |  | 
 | - |
| call_encoding_a_e_s_key | varchar(100) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| status | tinyint(4) | ✅ |  | 
 | - |

---

### 表: wx_external_contact

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| union_id | varchar(255) | ✅ |  | 
 | - |
| external_userid | varchar(255) | ❌ |  | 
 | - |
| kf_user_id | varchar(255) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: wx_external_contact_way_config

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| work_id | bigint(20) | ❌ |  | 
 | - |
| work_user_id | varchar(50) | ❌ |  | 
 | - |
| author_id | bigint(20) | ❌ |  | 
 | - |
| config_id | varchar(255) | ✅ |  | 
 | - |
| qr_code | varchar(512) | ✅ |  | 
 | - |
| create_time | datetime | ❌ |  | 
 | - |
| update_time | datetime | ❌ |  | 
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |

---

### 表: wx_work_setting

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| user_id | varchar(50) | ✅ |  | 
 | - |
| phone | varchar(100) | ✅ |  | 
 | - |
| name | varchar(100) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| qr_code_url | varchar(255) | ✅ |  | 
 | - |
| hk_url | varchar(255) | ✅ |  | 
 | - |
| status | int(11) | ✅ |  | 
 | - |
| wecom_id | bigint(20) | ✅ |  | 
 | - |

---

### 表: yudao_demo01_contact

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(100) | ❌ |  |  | - |
| sex | tinyint(1) | ❌ |  | 
 | - |
| birthday | datetime | ❌ |  | 
 | - |
| description | varchar(255) | ❌ |  | 
 | - |
| avatar | varchar(512) | ✅ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: yudao_demo02_category

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| name | varchar(100) | ❌ |  |  | - |
| parent_id | bigint(20) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: yudao_demo03_course

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| student_id | bigint(20) | ❌ |  | 
 | - |
| name | varchar(100) | ❌ |  |  | - |
| score | tinyint(4) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |

---

### 表: yudao_demo03_grade

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | NULL | - |
| student_id | bigint(20) | ❌ |  | 
 | - |
| name | varchar(100) | ❌ |  |  | - |
| teacher | varchar(255) | ❌ |  | 
 | - |
| creator | varchar(64) | ✅ |  |  | - |
| create_time | datetime | ❌ |  | 
 | - |
| updater | varchar(64) | ✅ |  |  | - |
| update_time | datetime | ❌ |  | on update CURRENT_TIMESTAMP
 | - |
| deleted | bit(1) | ❌ |  | 
 | - |
| tenant_id | bigint(20) | ❌ |  | 
 | - |
