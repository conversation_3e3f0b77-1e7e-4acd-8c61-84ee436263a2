# 足球彩票系统部署运维文档

## 1. 环境要求

### 1.1 硬件要求
| 环境 | CPU | 内存 | 磁盘 | 网络 |
|------|-----|------|------|------|
| 开发环境 | 2核 | 4GB | 50GB | 10Mbps |
| 测试环境 | 4核 | 8GB | 100GB | 50Mbps |
| 生产环境 | 8核 | 16GB | 500GB | 100Mbps |

### 1.2 软件要求
| 软件 | 版本要求 | 说明 |
|------|----------|------|
| JDK | 1.8+ | Java运行环境 |
| MySQL | 5.7+ / 8.0+ | 主数据库 |
| Redis | 5.0+ | 缓存数据库 |
| Nginx | 1.18+ | 反向代理 |
| Docker | 20.0+ | 容器化部署 |

### 1.3 操作系统
- CentOS 7+
- Ubuntu 18.04+
- Windows Server 2016+

## 2. 本地开发环境搭建

### 2.1 安装JDK
```bash
# CentOS/RHEL
yum install java-1.8.0-openjdk-devel

# Ubuntu/Debian
apt-get install openjdk-8-jdk

# 验证安装
java -version
javac -version
```

### 2.2 安装MySQL
```bash
# 下载MySQL 8.0
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
rpm -ivh mysql80-community-release-el7-3.noarch.rpm
yum install mysql-server

# 启动MySQL
systemctl start mysqld
systemctl enable mysqld

# 获取临时密码
grep 'temporary password' /var/log/mysqld.log

# 安全配置
mysql_secure_installation
```

### 2.3 安装Redis
```bash
# CentOS/RHEL
yum install redis

# Ubuntu/Debian
apt-get install redis-server

# 启动Redis
systemctl start redis
systemctl enable redis

# 验证安装
redis-cli ping
```

### 2.4 配置数据库
```sql
-- 创建数据库
CREATE DATABASE `mir_football` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'mir_user'@'%' IDENTIFIED BY 'mir_password';
GRANT ALL PRIVILEGES ON mir_football.* TO 'mir_user'@'%';
FLUSH PRIVILEGES;

-- 导入数据
mysql -u mir_user -p mir_football < sql/mysql/ruoyi-vue-pro.sql
```

## 3. 应用配置

### 3.1 数据库配置
```yaml
# application-dev.yaml
spring:
  datasource:
    druid:
      master:
        url: ********************************************************************************************************************************************
        username: mir_user
        password: mir_password
        driver-class-name: com.mysql.cj.jdbc.Driver
```

### 3.2 Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10s
    password: # Redis密码，如果没有设置可以为空
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
```

### 3.3 文件存储配置
```yaml
mir:
  file:
    config:
      @bean/fileConfigProperties:
        domain: http://localhost:48080
        base-path: /tmp/files
        type: local
```

## 4. 编译打包

### 4.1 Maven编译
```bash
# 清理编译
mvn clean compile

# 运行测试
mvn test

# 打包应用
mvn clean package -Dmaven.test.skip=true

# 生成的jar包位置
ls mir-server/target/mir-server.jar
```

### 4.2 Docker镜像构建
```bash
# 构建镜像
docker build -t mir-football-server:latest .

# 查看镜像
docker images | grep mir-football-server
```

## 5. 部署方式

### 5.1 传统部署
```bash
# 创建应用目录
mkdir -p /opt/mir-football
cd /opt/mir-football

# 复制jar包
cp mir-server/target/mir-server.jar ./app.jar

# 创建启动脚本
cat > start.sh << 'EOF'
#!/bin/bash
JAVA_OPTS="-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom"
nohup java $JAVA_OPTS -jar app.jar --spring.profiles.active=prod > app.log 2>&1 &
echo $! > app.pid
EOF

chmod +x start.sh

# 启动应用
./start.sh
```

### 5.2 Docker部署
```bash
# 运行容器
docker run -d \
  --name mir-football-server \
  -p 48080:48080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e JAVA_OPTS="-Xms512m -Xmx1024m" \
  -v /opt/logs:/mir-server/logs \
  mir-football-server:latest

# 查看容器状态
docker ps | grep mir-football-server

# 查看日志
docker logs -f mir-football-server
```

### 5.3 Docker Compose部署
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mir-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: mir_football
      MYSQL_USER: mir_user
      MYSQL_PASSWORD: mir_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/mysql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:6.2
    container_name: mir-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  app:
    image: mir-football-server:latest
    container_name: mir-football-server
    ports:
      - "48080:48080"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      JAVA_OPTS: "-Xms512m -Xmx1024m"
    depends_on:
      - mysql
      - redis
    volumes:
      - app_logs:/mir-server/logs

volumes:
  mysql_data:
  redis_data:
  app_logs:
```

## 6. Nginx配置

### 6.1 反向代理配置
```nginx
# /etc/nginx/conf.d/mir-football.conf
upstream mir_backend {
    server 127.0.0.1:48080;
    # 如果有多个实例，可以添加更多server
    # server 127.0.0.1:48081;
}

server {
    listen 80;
    server_name your-domain.com;

    # 静态资源
    location /static/ {
        root /opt/mir-football/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # API接口
    location /admin-api/ {
        proxy_pass http://mir_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 前端页面
    location / {
        root /opt/mir-football/web;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
}
```

### 6.2 HTTPS配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # 其他配置同上...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 7. 监控配置

### 7.1 应用监控
```yaml
# application.yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### 7.2 日志配置
```yaml
# logback-spring.xml
logging:
  level:
    com.pinwan.mir: INFO
    org.springframework.web: INFO
  file:
    name: /opt/logs/mir-football/app.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 10GB
```

## 8. 运维脚本

### 8.1 启动脚本
```bash
#!/bin/bash
# start.sh

APP_NAME="mir-football-server"
APP_JAR="app.jar"
APP_PID="app.pid"

if [ -f $APP_PID ]; then
    echo "Application is already running. PID: $(cat $APP_PID)"
    exit 1
fi

echo "Starting $APP_NAME..."
nohup java $JAVA_OPTS -jar $APP_JAR --spring.profiles.active=prod > app.log 2>&1 &
echo $! > $APP_PID
echo "Application started. PID: $(cat $APP_PID)"
```

### 8.2 停止脚本
```bash
#!/bin/bash
# stop.sh

APP_NAME="mir-football-server"
APP_PID="app.pid"

if [ ! -f $APP_PID ]; then
    echo "Application is not running."
    exit 1
fi

PID=$(cat $APP_PID)
echo "Stopping $APP_NAME (PID: $PID)..."
kill $PID

# 等待进程结束
for i in {1..30}; do
    if ! kill -0 $PID 2>/dev/null; then
        echo "Application stopped."
        rm -f $APP_PID
        exit 0
    fi
    sleep 1
done

# 强制杀死进程
echo "Force killing application..."
kill -9 $PID
rm -f $APP_PID
echo "Application force stopped."
```

### 8.3 重启脚本
```bash
#!/bin/bash
# restart.sh

./stop.sh
sleep 2
./start.sh
```

### 8.4 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

HEALTH_URL="http://localhost:48080/admin-api/actuator/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "Application is healthy"
    exit 0
else
    echo "Application is unhealthy (HTTP: $RESPONSE)"
    exit 1
fi
```

## 9. 备份策略

### 9.1 数据库备份
```bash
#!/bin/bash
# backup_db.sh

DB_NAME="mir_football"
DB_USER="mir_user"
DB_PASS="mir_password"
BACKUP_DIR="/opt/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/mir_football_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/mir_football_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Database backup completed: mir_football_$DATE.sql.gz"
```

### 9.2 文件备份
```bash
#!/bin/bash
# backup_files.sh

SOURCE_DIR="/opt/mir-football/files"
BACKUP_DIR="/opt/backups/files"
DATE=$(date +%Y%m%d)

mkdir -p $BACKUP_DIR

# 同步文件
rsync -av --delete $SOURCE_DIR/ $BACKUP_DIR/$DATE/

echo "Files backup completed: $BACKUP_DIR/$DATE"
```

## 10. 故障排查

### 10.1 常见问题
1. **应用启动失败**
   - 检查Java版本和环境变量
   - 检查数据库连接配置
   - 查看启动日志

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查防火墙设置

3. **Redis连接失败**
   - 检查Redis服务状态
   - 验证连接配置
   - 检查网络连通性

### 10.2 日志查看
```bash
# 查看应用日志
tail -f /opt/logs/mir-football/app.log

# 查看错误日志
grep ERROR /opt/logs/mir-football/app.log

# 查看系统日志
journalctl -u mir-football-server -f
```

### 10.3 性能监控
```bash
# 查看JVM内存使用
jstat -gc $(cat app.pid)

# 查看线程状态
jstack $(cat app.pid)

# 查看系统资源
top -p $(cat app.pid)
htop
```

---

**注意事项**:
1. 生产环境部署前请充分测试
2. 定期备份数据库和重要文件
3. 监控应用性能和系统资源
4. 及时更新安全补丁
5. 建立完善的日志和监控体系
