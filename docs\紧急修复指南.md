# 紧急修复指南 - 解决 "Duplicate column name 'tenant_id'" 错误

## 🚨 问题描述

执行多租户改造脚本时遇到错误：
```
ERROR 1064 (42000) at line 230: You have an error in your SQL syntax
ERROR 1060 (42S21) at line 16: Duplicate column name 'tenant_id'
```

这表示：
1. 之前已经部分执行过改造脚本
2. 某些表已经添加了`tenant_id`字段
3. 脚本试图重复添加相同字段导致错误

## 🔧 立即解决方案

### 方案一：使用紧急修复脚本（最推荐）

```bash
# 1. 给脚本执行权限
chmod +x scripts/emergency-fix.sh

# 2. 运行紧急修复脚本
./scripts/emergency-fix.sh
```

这个脚本会：
- ✅ 自动检查每个表是否已有`tenant_id`字段
- ✅ 只为缺失的表添加字段
- ✅ 跳过已存在的字段，不会报错
- ✅ 提供详细的执行结果
- ✅ 避免所有SQL语法兼容性问题

### 方案二：使用简单检查脚本

```bash
# 1. 先检查当前状态
chmod +x scripts/simple-check.sh
./scripts/simple-check.sh

# 2. 根据检查结果决定下一步
```

### 方案三：使用超简单SQL检查

```bash
# 1. 使用最简单的检查脚本（避免复杂SQL）
mysql -u root -p mir < sql/tenant-migration/00-ultra-simple-check.sql

# 2. 使用更新后的脚本（支持重复执行）
mysql -u root -p mir < sql/tenant-migration/01-add-tenant-fields.sql
```

### 方案四：快速修复脚本

```bash
# 使用快速修复脚本
chmod +x scripts/quick-fix.sh
./scripts/quick-fix.sh
```

## 📋 检查当前状态

### 查看哪些表已有tenant_id字段

```sql
SELECT 
    TABLE_NAME as '表名',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY 
    TABLE_NAME;
```

### 查看需要添加但还没有的表

```sql
-- 检查重要表的状态
SELECT 
    'member_user' as table_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'member_user' 
            AND COLUMN_NAME = 'tenant_id'
        ) THEN '✅ 已添加'
        ELSE '❌ 未添加'
    END as status
    
UNION ALL

SELECT 
    'pay_order' as table_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'pay_order' 
            AND COLUMN_NAME = 'tenant_id'
        ) THEN '✅ 已添加'
        ELSE '❌ 未添加'
    END as status;
```

## 🛠️ 手动修复步骤

如果您更喜欢手动操作：

### 1. 创建安全添加字段的存储过程

```sql
DELIMITER $$

DROP PROCEDURE IF EXISTS SafeAddTenantId$$

CREATE PROCEDURE SafeAddTenantId(IN table_name VARCHAR(64))
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    DECLARE table_exists INT DEFAULT 0;
    
    -- 检查表是否存在
    SELECT COUNT(*) INTO table_exists
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = table_name;
    
    IF table_exists > 0 THEN
        -- 检查字段是否已存在
        SELECT COUNT(*) INTO column_exists
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = table_name
          AND COLUMN_NAME = 'tenant_id';
        
        -- 如果字段不存在，则添加
        IF column_exists = 0 THEN
            SET @sql = CONCAT('ALTER TABLE ', table_name, 
                             ' ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT ''租户编号'' AFTER id');
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            SELECT CONCAT('✅ 表 ', table_name, ' 已添加 tenant_id 字段') AS result;
        ELSE
            SELECT CONCAT('⚠️  表 ', table_name, ' 的 tenant_id 字段已存在') AS result;
        END IF;
    ELSE
        SELECT CONCAT('❌ 表 ', table_name, ' 不存在') AS result;
    END IF;
END$$

DELIMITER ;
```

### 2. 安全添加字段

```sql
-- 为需要的表添加字段
CALL SafeAddTenantId('member_user');
CALL SafeAddTenantId('member_level');
CALL SafeAddTenantId('pay_app');
CALL SafeAddTenantId('pay_order');
CALL SafeAddTenantId('pay_wallet');
CALL SafeAddTenantId('article');

-- 清理存储过程
DROP PROCEDURE SafeAddTenantId;
```

## 🔄 继续改造流程

修复完成后，继续执行改造：

### 1. 创建索引

```bash
mysql -u root -p mir < sql/tenant-migration/02-create-indexes.sql
```

### 2. 迁移数据

```bash
mysql -u root -p mir < sql/tenant-migration/03-migrate-data.sql
```

### 3. 启用多租户配置

```yaml
# application.yaml
mir:
  tenant:
    enable: true
```

### 4. 重启应用

```bash
# 重启Spring Boot应用
```

## 📊 验证修复结果

### 检查字段添加结果

```sql
-- 统计已添加tenant_id字段的表数量
SELECT 
    COUNT(*) as '已添加tenant_id字段的表数量'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id';

-- 列出所有已添加的表
SELECT 
    TABLE_NAME as '表名'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY 
    TABLE_NAME;
```

### 检查数据状态

```sql
-- 检查主要表的记录数
SELECT 'member_user' as table_name, COUNT(*) as total_records FROM member_user
UNION ALL
SELECT 'pay_order' as table_name, COUNT(*) as total_records FROM pay_order
UNION ALL
SELECT 'article' as table_name, COUNT(*) as total_records FROM article;
```

## ⚠️ 注意事项

1. **备份数据**：修复前请确保已备份数据库
2. **测试环境**：建议先在测试环境验证
3. **业务影响**：在业务低峰期执行
4. **权限检查**：确保数据库用户有足够权限

## 🆘 如果还有问题

如果紧急修复脚本仍然失败：

1. **查看详细错误**：
   ```bash
   # 查看MySQL错误日志
   tail -f /var/log/mysql/error.log
   ```

2. **手动逐个检查**：
   ```sql
   -- 检查具体哪个表有问题
   SHOW CREATE TABLE member_user;
   SHOW CREATE TABLE pay_order;
   ```

3. **联系技术支持**：
   - 提供完整的错误日志
   - 说明已执行的步骤
   - 提供数据库版本信息

---

**使用紧急修复脚本是最安全和快速的解决方案！**
