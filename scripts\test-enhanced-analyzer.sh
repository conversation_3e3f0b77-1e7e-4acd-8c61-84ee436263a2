#!/bin/bash

# =============================================
# 测试增强的数据库分析器
# 专门用于验证修复后的功能
# =============================================

echo "========================================"
echo "测试增强的数据库分析器"
echo "========================================"

# 设置数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

echo "数据库配置:"
echo "  主机: $DB_HOST:$DB_PORT"
echo "  数据库: $DB_NAME"
echo "  用户: $DB_USER"
echo

# 测试基本连接
echo "=== 测试1: 基本连接 ==="
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" 2>/dev/null >/dev/null; then
    echo "✅ 连接成功"
else
    echo "❌ 连接失败，请检查数据库配置"
    exit 1
fi

# 获取表列表
echo
echo "=== 测试2: 获取表列表 ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/test_tables.txt

if [ -s "/tmp/test_tables.txt" ]; then
    TABLE_COUNT=$(wc -l < /tmp/test_tables.txt)
    echo "✅ 发现 $TABLE_COUNT 个表"
    
    # 获取第一个表进行测试
    FIRST_TABLE=$(head -1 /tmp/test_tables.txt)
    echo "测试表: $FIRST_TABLE"
else
    echo "❌ 未发现任何表"
    exit 1
fi

# 测试字段信息获取
echo
echo "=== 测试3: 字段信息获取 ==="
temp_file="/tmp/test_columns_$$.txt"

# 方法1: INFORMATION_SCHEMA
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT 
        COLUMN_NAME,
        COLUMN_TYPE,
        IS_NULLABLE,
        IFNULL(COLUMN_KEY, ''),
        IFNULL(COLUMN_DEFAULT, ''),
        IFNULL(COLUMN_COMMENT, '')
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$FIRST_TABLE' 
    ORDER BY ORDINAL_POSITION;" \
    --batch --skip-column-names 2>/dev/null > "$temp_file"

if [ -s "$temp_file" ]; then
    COLUMN_COUNT=$(wc -l < "$temp_file")
    echo "✅ INFORMATION_SCHEMA查询成功，获取 $COLUMN_COUNT 个字段"
    
    echo "前3个字段:"
    head -3 "$temp_file" | while IFS=$'\t' read -r col_name col_type is_null col_key col_default col_comment; do
        echo "  - $col_name ($col_type) - $col_comment"
    done
else
    echo "⚠️ INFORMATION_SCHEMA查询失败，尝试DESCRIBE"
    
    # 备选方案：DESCRIBE
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "DESCRIBE $FIRST_TABLE;" --batch --skip-column-names 2>/dev/null > "$temp_file"
    
    if [ -s "$temp_file" ]; then
        COLUMN_COUNT=$(wc -l < "$temp_file")
        echo "✅ DESCRIBE查询成功，获取 $COLUMN_COUNT 个字段"
    else
        echo "❌ 所有查询方法都失败"
    fi
fi

rm -f "$temp_file"

# 测试表注释获取
echo
echo "=== 测试4: 表注释获取 ==="
TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$FIRST_TABLE';" \
    --batch --skip-column-names 2>/dev/null)

if [ -n "$TABLE_COMMENT" ]; then
    echo "✅ 表注释: $TABLE_COMMENT"
else
    echo "⚠️ 无法获取表注释"
fi

# 运行增强的分析器
echo
echo "=== 测试5: 运行增强的分析器 ==="
if [ -f "scripts/universal-database-analyzer.sh" ]; then
    echo "运行完整分析..."
    
    # 创建测试输出目录
    mkdir -p test-analysis
    
    # 运行分析器
    ./scripts/universal-database-analyzer.sh \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --user="$DB_USER" \
        --password="$DB_PASSWORD" \
        --database="$DB_NAME" \
        --output="./test-analysis" \
        --with-structure \
        --with-er-diagram \
        --with-data-dict
    
    if [ $? -eq 0 ]; then
        echo "✅ 分析器运行成功"
        echo
        echo "生成的文件:"
        ls -la test-analysis/
        
        # 检查文件内容
        echo
        echo "检查表结构文件内容..."
        STRUCTURE_FILE=$(ls test-analysis/table-structure-*.md 2>/dev/null | head -1)
        if [ -f "$STRUCTURE_FILE" ]; then
            echo "表结构文件: $STRUCTURE_FILE"
            # 检查是否有实际的字段数据
            if grep -q "| [^-].*| [^-].*| [^-].*|" "$STRUCTURE_FILE"; then
                echo "✅ 表结构文件包含字段数据"
            else
                echo "❌ 表结构文件缺少字段数据"
            fi
        fi
        
        echo
        echo "检查ER图文件内容..."
        ER_FILE=$(ls test-analysis/er-diagram-*.mmd 2>/dev/null | head -1)
        if [ -f "$ER_FILE" ]; then
            echo "ER图文件: $ER_FILE"
            # 检查是否有实际的字段定义
            if grep -q "string\|int\|varchar\|bigint" "$ER_FILE"; then
                echo "✅ ER图文件包含字段定义"
            else
                echo "❌ ER图文件缺少字段定义"
            fi
        fi
        
    else
        echo "❌ 分析器运行失败"
    fi
else
    echo "❌ 找不到分析器脚本"
fi

# 清理
rm -f /tmp/test_tables.txt

echo
echo "========================================"
echo "测试完成！"
echo "========================================"
