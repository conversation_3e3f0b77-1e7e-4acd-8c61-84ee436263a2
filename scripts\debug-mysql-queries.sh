#!/bin/bash

# =============================================
# MySQL查询调试脚本
# 专门诊断INFORMATION_SCHEMA查询问题
# =============================================

echo "========================================"
echo "MySQL查询调试脚本"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

echo "数据库配置:"
echo "  主机: $DB_HOST:$DB_PORT"
echo "  数据库: $DB_NAME"
echo "  用户: $DB_USER"
echo

# 1. 测试基本连接
echo "=== 测试1: 基本连接 ==="
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" 2>/dev/null >/dev/null; then
    echo "✅ 连接成功"
else
    echo "❌ 连接失败"
    exit 1
fi

# 2. 测试数据库选择
echo
echo "=== 测试2: 数据库选择 ==="
CURRENT_DB=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT DATABASE();" --batch --skip-column-names 2>/dev/null)
echo "当前数据库: $CURRENT_DB"

if [ "$CURRENT_DB" = "$DB_NAME" ]; then
    echo "✅ 数据库选择正确"
else
    echo "❌ 数据库选择错误，期望: $DB_NAME，实际: $CURRENT_DB"
fi

# 3. 测试表列表
echo
echo "=== 测试3: 获取表列表 ==="
TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null)

if [ -n "$TABLES" ]; then
    TABLE_COUNT=$(echo "$TABLES" | wc -l)
    echo "✅ 发现 $TABLE_COUNT 个表"
    FIRST_TABLE=$(echo "$TABLES" | head -1)
    echo "第一个表: $FIRST_TABLE"
else
    echo "❌ 未发现任何表"
    exit 1
fi

# 4. 测试INFORMATION_SCHEMA访问
echo
echo "=== 测试4: INFORMATION_SCHEMA访问 ==="

# 4.1 测试表信息查询
echo "4.1 测试表信息查询..."
TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT TABLE_NAME, TABLE_COMMENT, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() LIMIT 3;" \
    --batch --skip-column-names 2>/dev/null)

if [ -n "$TABLE_INFO" ]; then
    echo "✅ 表信息查询成功"
    echo "前3个表的信息:"
    echo "$TABLE_INFO" | while IFS=$'\t' read -r table_name table_comment table_rows; do
        echo "  - $table_name: $table_comment ($table_rows 行)"
    done
else
    echo "❌ 表信息查询失败"
fi

# 4.2 测试字段信息查询
echo
echo "4.2 测试字段信息查询..."
COLUMN_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COLUMN_NAME, COLUMN_TYPE, COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$FIRST_TABLE' LIMIT 3;" \
    --batch --skip-column-names 2>/dev/null)

if [ -n "$COLUMN_INFO" ]; then
    echo "✅ 字段信息查询成功"
    echo "表 $FIRST_TABLE 的前3个字段:"
    echo "$COLUMN_INFO" | while IFS=$'\t' read -r col_name col_type col_comment; do
        echo "  - $col_name ($col_type): $col_comment"
    done
else
    echo "❌ 字段信息查询失败"
    
    # 尝试使用具体数据库名
    echo "尝试使用具体数据库名..."
    COLUMN_INFO2=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COLUMN_NAME, COLUMN_TYPE, COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$FIRST_TABLE' LIMIT 3;" \
        --batch --skip-column-names 2>/dev/null)
    
    if [ -n "$COLUMN_INFO2" ]; then
        echo "✅ 使用具体数据库名查询成功"
        echo "$COLUMN_INFO2" | while IFS=$'\t' read -r col_name col_type col_comment; do
            echo "  - $col_name ($col_type): $col_comment"
        done
    else
        echo "❌ 使用具体数据库名也失败"
    fi
fi

# 5. 测试DESCRIBE备选方案
echo
echo "=== 测试5: DESCRIBE备选方案 ==="
DESCRIBE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "DESCRIBE $FIRST_TABLE;" --batch --skip-column-names 2>/dev/null)

if [ -n "$DESCRIBE_INFO" ]; then
    echo "✅ DESCRIBE查询成功"
    echo "表 $FIRST_TABLE 的字段结构:"
    echo "$DESCRIBE_INFO" | head -3 | while IFS=$'\t' read -r col_name col_type is_null col_key col_default extra; do
        echo "  - $col_name ($col_type) 键:$col_key 默认值:$col_default"
    done
else
    echo "❌ DESCRIBE查询失败"
fi

# 6. 测试权限
echo
echo "=== 测试6: 权限检查 ==="
GRANTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    -e "SHOW GRANTS FOR CURRENT_USER();" 2>/dev/null)

if [ -n "$GRANTS" ]; then
    echo "✅ 用户权限:"
    echo "$GRANTS"
else
    echo "❌ 无法获取权限信息"
fi

echo
echo "========================================"
echo "调试完成！"
echo "========================================"
