package com.pinwan.mir.module.member.dal.dataobject.bankinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;

/**
 * 银行信息 DO
 *
 * <AUTHOR>
 */
@TableName("pay_bank_info")
@KeySequence("pay_bank_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BankInfoDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 银行行号
     */
    private String bankNo;
    /**
     * 银行名称
     */
    private String bankName;

}