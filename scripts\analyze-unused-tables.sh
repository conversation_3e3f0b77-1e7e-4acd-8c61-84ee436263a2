#!/bin/bash

# =============================================
# 分析未使用表脚本
# 识别demo表、测试表和可能未使用的表
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "数据库表使用情况分析工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database

log_info "获取所有表列表..."

# 获取所有表名和基本信息
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF' > /tmp/all_tables_info.txt
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS,
    CREATE_TIME,
    UPDATE_TIME
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_TYPE = 'BASE TABLE'
ORDER BY 
    TABLE_NAME;
EOF

log_info "分析表使用情况..."

# 生成分析报告
cat > docs/database/table-usage-analysis.md << 'EOF'
# 数据库表使用情况分析报告

## 📊 概览

本报告分析数据库中所有表的使用情况，识别出可能的demo表、测试表和未使用的表。

## 🗑️ 可能可以删除的表

### 1. Demo表（框架示例表）
EOF

echo "" >> docs/database/table-usage-analysis.md

# 分析demo表
echo "### Demo表分析:" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md

# 检查yudao_demo相关表
DEMO_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES LIKE '%demo%';" --batch --skip-column-names 2>/dev/null)

if [ -n "$DEMO_TABLES" ]; then
    echo "发现以下demo相关表:" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
    echo "$DEMO_TABLES" | while read table; do
        # 获取表信息
        TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT TABLE_COMMENT, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        
        echo "- **$table**: $TABLE_INFO" >> docs/database/table-usage-analysis.md
    done
    echo "" >> docs/database/table-usage-analysis.md
else
    echo "✅ 未发现demo相关表" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
fi

# 分析测试表
echo "### 2. 测试表" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md

TEST_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES LIKE '%test%';" --batch --skip-column-names 2>/dev/null)

if [ -n "$TEST_TABLES" ]; then
    echo "发现以下测试相关表:" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
    echo "$TEST_TABLES" | while read table; do
        TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT TABLE_COMMENT, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        
        echo "- **$table**: $TABLE_INFO" >> docs/database/table-usage-analysis.md
    done
    echo "" >> docs/database/table-usage-analysis.md
else
    echo "✅ 未发现测试相关表" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
fi

# 分析临时表
echo "### 3. 临时表" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md

TEMP_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES LIKE '%temp%';" --batch --skip-column-names 2>/dev/null)

if [ -n "$TEMP_TABLES" ]; then
    echo "发现以下临时表:" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
    echo "$TEMP_TABLES" | while read table; do
        TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT TABLE_COMMENT, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        
        echo "- **$table**: $TABLE_INFO" >> docs/database/table-usage-analysis.md
    done
    echo "" >> docs/database/table-usage-analysis.md
else
    echo "✅ 未发现临时表" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
fi

# 分析空表
echo "### 4. 空表（无数据）" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md

EMPTY_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_TYPE = 'BASE TABLE' AND (TABLE_ROWS = 0 OR TABLE_ROWS IS NULL);" \
    --batch --skip-column-names 2>/dev/null)

if [ -n "$EMPTY_TABLES" ]; then
    echo "发现以下空表:" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
    echo "$EMPTY_TABLES" | while read table; do
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        
        echo "- **$table**: $TABLE_COMMENT" >> docs/database/table-usage-analysis.md
    done
    echo "" >> docs/database/table-usage-analysis.md
else
    echo "✅ 所有表都有数据" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
fi

# 分析业务核心表
echo "## ✅ 核心业务表（不能删除）" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md

echo "### 1. 会员模块" >> docs/database/table-usage-analysis.md
MEMBER_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES LIKE 'member_%';" --batch --skip-column-names 2>/dev/null)

if [ -n "$MEMBER_TABLES" ]; then
    echo "$MEMBER_TABLES" | while read table; do
        TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT TABLE_COMMENT, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        echo "- **$table**: $TABLE_INFO" >> docs/database/table-usage-analysis.md
    done
fi
echo "" >> docs/database/table-usage-analysis.md

echo "### 2. 支付模块" >> docs/database/table-usage-analysis.md
PAY_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES LIKE 'pay_%';" --batch --skip-column-names 2>/dev/null)

if [ -n "$PAY_TABLES" ]; then
    echo "$PAY_TABLES" | while read table; do
        TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT TABLE_COMMENT, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        echo "- **$table**: $TABLE_INFO" >> docs/database/table-usage-analysis.md
    done
fi
echo "" >> docs/database/table-usage-analysis.md

echo "### 3. 业务模块" >> docs/database/table-usage-analysis.md
BUSINESS_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES LIKE 'author_%';" --batch --skip-column-names 2>/dev/null)

if [ -n "$BUSINESS_TABLES" ]; then
    echo "$BUSINESS_TABLES" | while read table; do
        TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT TABLE_COMMENT, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        echo "- **$table**: $TABLE_INFO" >> docs/database/table-usage-analysis.md
    done
fi
echo "" >> docs/database/table-usage-analysis.md

# 生成删除建议
echo "## 🗑️ 删除建议" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md

echo "### 安全删除的表" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md

# 合并所有可能删除的表
ALL_DELETABLE=""
if [ -n "$DEMO_TABLES" ]; then
    ALL_DELETABLE="$ALL_DELETABLE $DEMO_TABLES"
fi
if [ -n "$TEST_TABLES" ]; then
    ALL_DELETABLE="$ALL_DELETABLE $TEST_TABLES"
fi
if [ -n "$TEMP_TABLES" ]; then
    ALL_DELETABLE="$ALL_DELETABLE $TEMP_TABLES"
fi

if [ -n "$ALL_DELETABLE" ]; then
    echo "以下表可以安全删除:" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
    echo '```sql' >> docs/database/table-usage-analysis.md
    echo "-- 删除demo/测试/临时表" >> docs/database/table-usage-analysis.md
    for table in $ALL_DELETABLE; do
        echo "DROP TABLE IF EXISTS $table;" >> docs/database/table-usage-analysis.md
    done
    echo '```' >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
else
    echo "✅ 未发现明显可删除的表" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
fi

echo "### 需要进一步确认的空表" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md

if [ -n "$EMPTY_TABLES" ]; then
    echo "以下空表需要确认是否还需要:" >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
    echo '```sql' >> docs/database/table-usage-analysis.md
    echo "-- 空表（需要确认后删除）" >> docs/database/table-usage-analysis.md
    for table in $EMPTY_TABLES; do
        echo "-- DROP TABLE IF EXISTS $table; -- 请确认是否需要" >> docs/database/table-usage-analysis.md
    done
    echo '```' >> docs/database/table-usage-analysis.md
    echo "" >> docs/database/table-usage-analysis.md
fi

echo "## ⚠️ 注意事项" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md
echo "1. **备份数据**: 删除表前请务必备份数据库" >> docs/database/table-usage-analysis.md
echo "2. **确认依赖**: 检查代码中是否有对这些表的引用" >> docs/database/table-usage-analysis.md
echo "3. **测试环境**: 建议先在测试环境执行删除操作" >> docs/database/table-usage-analysis.md
echo "4. **分步执行**: 不要一次性删除所有表，分批进行" >> docs/database/table-usage-analysis.md
echo "" >> docs/database/table-usage-analysis.md

echo "---" >> docs/database/table-usage-analysis.md
echo "**生成时间**: $(date)" >> docs/database/table-usage-analysis.md
echo "**数据库**: $DB_NAME" >> docs/database/table-usage-analysis.md

log_success "表使用情况分析完成: docs/database/table-usage-analysis.md"

# 清理临时文件
rm -f /tmp/all_tables_info.txt

echo
echo "========================================"
echo "分析完成！"
echo "========================================"
echo "📋 docs/database/table-usage-analysis.md - 详细分析报告"
echo "========================================"
