-- =============================================
-- 超简单状态检查脚本 - 绝对兼容所有MySQL版本
-- =============================================

-- 1. 检查已添加tenant_id字段的表
SELECT '=== 已添加tenant_id字段的表 ===' AS message;

SELECT 
    TABLE_NAME,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY 
    TABLE_NAME;

-- 2. 统计数量
SELECT 
    COUNT(*) as tenant_id_tables_count
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id';

-- 3. 检查重要表是否存在
SELECT '=== 重要表存在性检查 ===' AS message;

SELECT 'member_user' as table_name, 
       COUNT(*) as table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_user';

SELECT 'pay_order' as table_name, 
       COUNT(*) as table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_order';

SELECT 'article' as table_name, 
       COUNT(*) as table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'article';

SELECT 'pay_wallet' as table_name, 
       COUNT(*) as table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_wallet';

-- 4. 检查重要表的tenant_id字段
SELECT '=== 重要表tenant_id字段检查 ===' AS message;

SELECT 'member_user' as table_name, 
       COUNT(*) as tenant_id_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_user' AND COLUMN_NAME = 'tenant_id';

SELECT 'pay_order' as table_name, 
       COUNT(*) as tenant_id_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_order' AND COLUMN_NAME = 'tenant_id';

SELECT 'article' as table_name, 
       COUNT(*) as tenant_id_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'article' AND COLUMN_NAME = 'tenant_id';

SELECT 'pay_wallet' as table_name, 
       COUNT(*) as tenant_id_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_wallet' AND COLUMN_NAME = 'tenant_id';

-- 5. 检查索引
SELECT '=== tenant相关索引检查 ===' AS message;

SELECT 
    TABLE_NAME,
    INDEX_NAME
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME LIKE '%tenant%'
GROUP BY 
    TABLE_NAME, INDEX_NAME
ORDER BY 
    TABLE_NAME, INDEX_NAME;

-- 6. 检查system_tenant表
SELECT '=== 系统租户表检查 ===' AS message;

SELECT 'system_tenant' as table_name, 
       COUNT(*) as table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'system_tenant';

SELECT '=== 检查完成 ===' AS message;
