#!/bin/bash

# =============================================
# 通用数据库表分析脚本
# 可用于任何MySQL数据库的表分析和分类
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "通用数据库表分析脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -H, --host HOST         数据库主机 (默认: localhost)"
    echo "  -P, --port PORT         数据库端口 (默认: 3306)"
    echo "  -u, --user USER         数据库用户 (默认: root)"
    echo "  -p, --password PASS     数据库密码"
    echo "  -d, --database DB       数据库名称 (必需)"
    echo "  -o, --output DIR        输出目录 (默认: ./database-analysis)"
    echo "  -c, --config FILE       配置文件路径"
    echo ""
    echo "示例:"
    echo "  $0 -d mydb -u root -p mypass"
    echo "  $0 --database=mydb --config=db.conf"
    echo ""
    echo "配置文件格式 (可选):"
    echo "  DB_HOST=localhost"
    echo "  DB_PORT=3306"
    echo "  DB_USER=root"
    echo "  DB_PASSWORD=password"
    echo "  DB_NAME=database_name"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -H|--host)
                DB_HOST="$2"
                shift 2
                ;;
            -P|--port)
                DB_PORT="$2"
                shift 2
                ;;
            -u|--user)
                DB_USER="$2"
                shift 2
                ;;
            -p|--password)
                DB_PASSWORD="$2"
                shift 2
                ;;
            -d|--database)
                DB_NAME="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            --host=*)
                DB_HOST="${1#*=}"
                shift
                ;;
            --port=*)
                DB_PORT="${1#*=}"
                shift
                ;;
            --user=*)
                DB_USER="${1#*=}"
                shift
                ;;
            --password=*)
                DB_PASSWORD="${1#*=}"
                shift
                ;;
            --database=*)
                DB_NAME="${1#*=}"
                shift
                ;;
            --output=*)
                OUTPUT_DIR="${1#*=}"
                shift
                ;;
            --config=*)
                CONFIG_FILE="${1#*=}"
                shift
                ;;
            *)
                echo "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 智能分析表前缀和分类
analyze_table_patterns() {
    local tables_file="$1"
    
    # 分析表前缀
    echo "分析表前缀模式..." >&2
    
    # 提取所有前缀（下划线分隔的第一部分）
    awk -F'_' '{if(NF>1) print $1}' "$tables_file" | sort | uniq -c | sort -nr > /tmp/prefixes.txt
    
    # 分析常见模式
    declare -A PREFIX_CATEGORIES
    
    # 系统相关前缀
    PREFIX_CATEGORIES["system"]="系统管理"
    PREFIX_CATEGORIES["sys"]="系统管理"
    PREFIX_CATEGORIES["admin"]="管理后台"
    PREFIX_CATEGORIES["auth"]="认证授权"
    PREFIX_CATEGORIES["user"]="用户管理"
    PREFIX_CATEGORIES["role"]="角色权限"
    PREFIX_CATEGORIES["permission"]="权限管理"
    
    # 业务相关前缀
    PREFIX_CATEGORIES["member"]="会员管理"
    PREFIX_CATEGORIES["customer"]="客户管理"
    PREFIX_CATEGORIES["order"]="订单管理"
    PREFIX_CATEGORIES["product"]="产品管理"
    PREFIX_CATEGORIES["payment"]="支付管理"
    PREFIX_CATEGORIES["pay"]="支付管理"
    PREFIX_CATEGORIES["finance"]="财务管理"
    
    # 内容相关前缀
    PREFIX_CATEGORIES["content"]="内容管理"
    PREFIX_CATEGORIES["article"]="文章管理"
    PREFIX_CATEGORIES["news"]="新闻管理"
    PREFIX_CATEGORIES["blog"]="博客管理"
    PREFIX_CATEGORIES["cms"]="内容管理"
    PREFIX_CATEGORIES["author"]="作者管理"
    PREFIX_CATEGORIES["banner"]="Banner管理"
    PREFIX_CATEGORIES["gold"]="金币管理"    
    PREFIX_CATEGORIES["football"]="足球管理"
    PREFIX_CATEGORIES["match"]="赛事管理"

    # 基础设施前缀
    PREFIX_CATEGORIES["infra"]="基础设施"
    PREFIX_CATEGORIES["config"]="配置管理"
    PREFIX_CATEGORIES["log"]="日志管理"
    PREFIX_CATEGORIES["monitor"]="监控管理"
    PREFIX_CATEGORIES["job"]="任务调度"
    PREFIX_CATEGORIES["qrtz"]="Quartz调度"
    PREFIX_CATEGORIES["schedule"]="任务调度"
    
    # 第三方集成前缀
    PREFIX_CATEGORIES["mp"]="微信公众号"
    PREFIX_CATEGORIES["wechat"]="微信集成"
    PREFIX_CATEGORIES["wx"]="微信集成"
    PREFIX_CATEGORIES["oauth"]="OAuth认证"
    PREFIX_CATEGORIES["sms"]="短信服务"
    PREFIX_CATEGORIES["mail"]="邮件服务"
    
    # 输出分析结果
    echo "发现的表前缀模式:" >&2
    while read count prefix; do
        if [ "$count" -gt 1 ]; then
            category="${PREFIX_CATEGORIES[$prefix]:-未分类}"
            echo "  $prefix: $count个表 -> $category" >&2
        fi
    done < /tmp/prefixes.txt
}

# 生成通用分类报告
generate_analysis_report() {
    local db_name="$1"
    local output_file="$2"
    local tables_file="/tmp/all_tables.txt"
    
    # 获取数据库基本信息
    local total_tables=$(wc -l < "$tables_file")
    local db_size=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$db_name" \
        -e "SELECT ROUND(SUM(DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE();" \
        --batch --skip-column-names 2>/dev/null)
    
    # 生成报告头部
    cat > "$output_file" << EOF
# 数据库表分析报告

## 📊 数据库概览

- **数据库名称**: $db_name
- **总表数**: $total_tables
- **数据库大小**: ${db_size}MB
- **分析时间**: $(date)
- **分析工具**: 通用数据库表分析脚本

## 🔍 分析方法

本报告通过以下方式自动分析数据库表结构：

1. **前缀分析**: 基于表名前缀进行智能分类
2. **模式识别**: 识别常见的数据库设计模式
3. **功能推断**: 根据表名和字段推断功能用途
4. **关系分析**: 分析表之间的关联关系

## 📋 表分类结果

EOF

    # 智能分析表模式
    analyze_table_patterns "$tables_file"
    
    # 按前缀分类
    local category_num=1
    
    # 获取所有前缀并分类
    awk -F'_' '{if(NF>1) print $1}' "$tables_file" | sort | uniq > /tmp/unique_prefixes.txt
    
    while read prefix; do
        if [ -n "$prefix" ]; then
            local tables_with_prefix=$(grep "^${prefix}_" "$tables_file")
            local count=$(echo "$tables_with_prefix" | wc -l)
            
            if [ "$count" -gt 0 ]; then
                # 确定分类名称
                local category_name="未分类模块"
                case "$prefix" in
                    "system"|"sys") category_name="系统管理模块" ;;
                    "admin") category_name="管理后台模块" ;;
                    "user"|"member"|"customer") category_name="用户管理模块" ;;
                    "order"|"payment"|"pay") category_name="交易支付模块" ;;
                    "product"|"goods"|"item") category_name="商品管理模块" ;;
                    "content"|"article"|"news"|"blog") category_name="内容管理模块" ;;
                    "infra"|"config") category_name="基础设施模块" ;;
                    "log"|"audit") category_name="日志审计模块" ;;
                    "job"|"qrtz"|"schedule") category_name="任务调度模块" ;;
                    "mp"|"wechat"|"wx") category_name="微信集成模块" ;;
                    "oauth"|"auth") category_name="认证授权模块" ;;
                    "sms"|"mail"|"message") category_name="消息通知模块" ;;
                esac
                
                echo "### $category_num. ${category_name} (${prefix}_*)" >> "$output_file"
                echo "" >> "$output_file"
                echo "**表数量**: $count" >> "$output_file"
                echo "" >> "$output_file"
                
                # 列出该前缀下的所有表
                echo "$tables_with_prefix" | while read table_name; do
                    if [ -n "$table_name" ]; then
                        local table_info=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$db_name" \
                            -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                            --batch --skip-column-names 2>/dev/null)
                        echo "- **$table_name**: $table_info" >> "$output_file"
                    fi
                done
                
                echo "" >> "$output_file"
                category_num=$((category_num + 1))
            fi
        fi
    done < /tmp/unique_prefixes.txt
    
    # 处理无前缀的表
    local no_prefix_tables=$(grep -v "_" "$tables_file")
    if [ -n "$no_prefix_tables" ]; then
        echo "### $category_num. 单表模块" >> "$output_file"
        echo "" >> "$output_file"
        echo "**说明**: 没有明确前缀的独立表" >> "$output_file"
        echo "" >> "$output_file"
        
        echo "$no_prefix_tables" | while read table_name; do
            if [ -n "$table_name" ]; then
                local table_info=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$db_name" \
                    -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                    --batch --skip-column-names 2>/dev/null)
                echo "- **$table_name**: $table_info" >> "$output_file"
            fi
        done
        echo "" >> "$output_file"
    fi
    
    # 添加统计信息
    cat >> "$output_file" << EOF
## 📊 统计信息

| 指标 | 数值 |
|------|------|
| 总表数 | $total_tables |
| 数据库大小 | ${db_size}MB |
| 有前缀的表 | $(grep "_" "$tables_file" | wc -l) |
| 无前缀的表 | $(grep -v "_" "$tables_file" | wc -l) |
| 前缀类型数 | $(awk -F'_' '{if(NF>1) print $1}' "$tables_file" | sort | uniq | wc -l) |

## 🎯 建议

### 数据库设计建议
1. **命名规范**: 建议使用统一的表名前缀来标识功能模块
2. **注释完善**: 为表和字段添加清晰的注释说明
3. **模块化**: 按功能模块组织表结构，便于维护

### 重构建议
1. **模块分离**: 可以考虑按前缀将表分组到不同的SQL文件中
2. **清理优化**: 检查是否有废弃的表可以清理
3. **索引优化**: 检查表的索引设计是否合理

---

**分析完成时间**: $(date)
**工具版本**: 通用数据库表分析脚本 v1.0
EOF
}

# 主函数
main() {
    # 设置默认值
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_USER=${DB_USER:-root}
    DB_PASSWORD=${DB_PASSWORD:-}
    DB_NAME=${DB_NAME:-}
    OUTPUT_DIR=${OUTPUT_DIR:-./database-analysis}
    CONFIG_FILE=${CONFIG_FILE:-}
    
    # 解析命令行参数
    parse_args "$@"
    
    # 读取配置文件
    if [ -n "$CONFIG_FILE" ] && [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
    fi
    
    # 检查必需参数
    if [ -z "$DB_NAME" ]; then
        echo "错误: 必须指定数据库名称"
        show_help
        exit 1
    fi
    
    # 如果没有提供密码，提示输入
    if [ -z "$DB_PASSWORD" ]; then
        read -s -p "请输入数据库密码: " DB_PASSWORD
        echo
    fi
    
    echo "========================================"
    echo "通用数据库表分析工具"
    echo "========================================"
    echo "数据库: $DB_NAME"
    echo "主机: $DB_HOST:$DB_PORT"
    echo "用户: $DB_USER"
    echo "========================================"
    
    # 验证数据库连接
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
        echo "❌ 数据库连接失败"
        exit 1
    fi
    
    log_success "数据库连接成功"
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    log_info "开始分析数据库表结构..."
    
    # 获取所有表
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt
    
    local total_tables=$(wc -l < /tmp/all_tables.txt)
    log_info "发现 $total_tables 个表"
    
    # 生成分析报告
    local output_file="$OUTPUT_DIR/database-analysis-$(date +%Y%m%d-%H%M%S).md"
    generate_analysis_report "$DB_NAME" "$output_file"
    
    log_success "分析报告已生成: $output_file"
    
    # 清理临时文件
    rm -f /tmp/all_tables.txt /tmp/prefixes.txt /tmp/unique_prefixes.txt
    
    echo
    echo "========================================"
    echo "数据库分析完成！"
    echo "========================================"
    echo "📋 分析报告: $output_file"
    echo "📊 总表数: $total_tables"
    echo "========================================"
}

# 执行主函数
main "$@"
