# 数据库表清理指南

## 🎯 目标

识别和清理数据库中不必要的表，特别是：
- **yudao_demo*** 等框架demo表
- **测试表和临时表**
- **空表（无数据的表）**
- **未被代码引用的表**

## 🛠️ 分析工具

项目提供了多个分析工具，按复杂度和功能分类：

### 1. 快速分析工具

#### 综合清理分析（推荐）
```bash
chmod +x scripts/comprehensive-table-cleanup.sh
./scripts/comprehensive-table-cleanup.sh
```
**功能**: 一键分析所有可清理的表，生成综合报告和清理脚本

#### Demo表专项分析
```bash
chmod +x scripts/analyze-demo-tables.sh
./scripts/analyze-demo-tables.sh
```
**功能**: 专门分析yudao框架的demo表和测试表

### 2. 详细分析工具

#### 表使用情况分析
```bash
chmod +x scripts/analyze-unused-tables.sh
./scripts/analyze-unused-tables.sh
```
**功能**: 分析demo表、测试表、临时表和空表

#### 代码引用检查
```bash
chmod +x scripts/check-table-references.sh
./scripts/check-table-references.sh
```
**功能**: 检查表在Java代码中的引用情况

## 📋 执行步骤

### 步骤1: 数据库备份（必须）

```bash
# 备份当前数据库
mysqldump -u root -p mir > backup_$(date +%Y%m%d_%H%M%S).sql

# 验证备份文件
ls -lh backup_*.sql
```

### 步骤2: 执行综合分析

```bash
# 运行综合分析
./scripts/comprehensive-table-cleanup.sh
```

这将生成：
- `docs/database/comprehensive-cleanup-report.md` - 详细分析报告
- `docs/database/comprehensive-cleanup.sql` - 清理SQL脚本

### 步骤3: 查看分析结果

```bash
# 查看分析报告
cat docs/database/comprehensive-cleanup-report.md

# 查看清理脚本
cat docs/database/comprehensive-cleanup.sql
```

### 步骤4: 执行清理（谨慎）

#### 方法1: 分步执行（推荐）
```bash
# 1. 先删除明确的demo表
mysql -u root -p mir -e "DROP TABLE IF EXISTS mir_demo01_contact;"
mysql -u root -p mir -e "DROP TABLE IF EXISTS mir_demo02_category;"
mysql -u root -p mir -e "DROP TABLE IF EXISTS mir_demo03_student;"
# ... 其他demo表

# 2. 确认后删除空表
mysql -u root -p mir -e "DROP TABLE IF EXISTS some_empty_table;"
```

#### 方法2: 批量执行
```bash
# 执行生成的清理脚本
mysql -u root -p mir < docs/database/comprehensive-cleanup.sql
```

## 🔍 基于代码分析发现的Demo表

根据项目代码分析，发现以下Demo表：

### 已确认的Demo表
- **mir_demo01_contact** - 示例联系人表
- **mir_demo02_category** - 示例分类表  
- **mir_demo03_student** - 学生表
- **mir_demo03_course** - 学生课程表
- **mir_demo03_grade** - 学生班级表
- **pay_demo_transfer** - 示例转账订单表

### 对应的代码文件
```
mir-module-infra/mir-module-infra-biz/src/main/java/com/pinwan/mir/module/infra/dal/dataobject/demo/
├── demo01/Demo01ContactDO.java
├── demo02/Demo02CategoryDO.java
└── demo03/
    ├── Demo03StudentDO.java
    ├── Demo03CourseDO.java
    └── Demo03GradeDO.java

mir-module-pay/mir-module-pay-biz/src/main/java/com/pinwan/mir/module/pay/dal/dataobject/demo/
└── PayDemoTransferDO.java
```

## 🗑️ 清理建议

### 高优先级（可安全删除）

**Demo表**：
```sql
-- YuDao框架Demo表
DROP TABLE IF EXISTS mir_demo01_contact;
DROP TABLE IF EXISTS mir_demo02_category;
DROP TABLE IF EXISTS mir_demo03_student;
DROP TABLE IF EXISTS mir_demo03_course;
DROP TABLE IF EXISTS mir_demo03_grade;
DROP TABLE IF EXISTS pay_demo_transfer;
```

**测试表**：
```sql
-- 如果存在测试表
DROP TABLE IF EXISTS test_table;
DROP TABLE IF EXISTS temp_table;
```

### 中优先级（需确认）

**空表**：
- 先确认表的业务用途
- 确认是否为预留的业务表
- 确认后再删除

**未引用表**：
- 检查是否为配置表
- 检查是否为第三方工具使用
- 确认后再删除

## 📊 清理效果预估

基于典型的yudao项目：

| 类型 | 预估数量 | 清理建议 |
|------|----------|----------|
| Demo表 | 5-10个 | 可安全删除 |
| 测试表 | 0-5个 | 可安全删除 |
| 空表 | 5-15个 | 需确认后删除 |
| 未引用表 | 0-10个 | 需仔细确认 |

**预估清理效果**：
- 可安全删除：10-20个表
- 需确认删除：5-25个表
- 总体可清理：15-45个表

## ⚠️ 重要注意事项

### 删除前检查清单

- [ ] **数据库已备份**
- [ ] **在测试环境验证过**
- [ ] **确认表中无重要数据**
- [ ] **检查代码中无引用**
- [ ] **确认不被外部系统使用**
- [ ] **团队成员已知晓**

### 安全删除流程

1. **备份数据库**
2. **在测试环境执行**
3. **验证系统功能正常**
4. **在生产环境执行**
5. **再次验证系统功能**
6. **清理相关代码文件**

### 回滚方案

如果删除后发现问题：

```bash
# 从备份恢复
mysql -u root -p mir < backup_20250710_112000.sql

# 或者单独恢复某个表
mysqldump -u root -p mir table_name > table_backup.sql
mysql -u root -p mir < table_backup.sql
```

## 🧹 代码清理

删除表后，建议同时清理相关代码：

### 1. 删除DO实体类
```bash
# 删除Demo相关的DO类
rm -rf mir-module-infra/mir-module-infra-biz/src/main/java/com/pinwan/mir/module/infra/dal/dataobject/demo/
rm -f mir-module-pay/mir-module-pay-biz/src/main/java/com/pinwan/mir/module/pay/dal/dataobject/demo/PayDemoTransferDO.java
```

### 2. 删除相关Service、Controller等
```bash
# 查找并删除demo相关的其他文件
find . -name "*Demo*" -type f | grep -E "\.(java|xml)$"
```

### 3. 清理配置文件
检查并清理配置文件中的demo相关配置。

## 📈 清理后的好处

1. **减少数据库大小**
2. **提高备份速度**
3. **简化数据库结构**
4. **减少维护成本**
5. **避免混淆和误操作**

## 🔄 定期清理建议

建议建立定期清理机制：

1. **每月检查**：运行分析脚本检查新的无用表
2. **版本发布前**：清理测试过程中产生的临时表
3. **重大重构后**：清理废弃的业务表

---

**记住**：删除表是不可逆操作，务必谨慎执行并做好备份！
