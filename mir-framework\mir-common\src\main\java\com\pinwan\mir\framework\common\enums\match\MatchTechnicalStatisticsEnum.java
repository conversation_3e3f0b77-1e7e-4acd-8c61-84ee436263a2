package com.pinwan.mir.framework.common.enums.match;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MatchTechnicalStatisticsEnum {
    /**
     * 技术统计
     */

    GOALS(1, "进球"),
    CORNERS(2, "角球"),
    YELLOW_CARDS(3, "黄牌"),
    RED_CARDS(4, "红牌"),
    OFFSIDES(5, "越位"),
    FREE_KICKS(6, "任意球"),
    GOAL_KICKS(7, "球门球"),
    PENALTY_KICKS(8, "点球"),
    SUBSTITUTIONS(9, "换人"),
    MATCH_STARTED(10, "比赛开始"),
    HALF_TIME(11, "中场"),
    MATCH_ENDED(12, "结束"),
    HALF_TIME_SCORE(13, "半场比分"),
    TWO_YELLOWS_RED(15, "两黄变红"),
    PENALTY_MISSED(16, "点球未进"),
    OWN_GOAL(17, "乌龙球"),
    ASSISTS(18, "助攻"),
    INJURY_TIME(19, "伤停补时"),
    SHOTS_ON_TARGET(21, "射正"),
    SHOTS_OFF_TARGET(22, "射偏"),
    ATTACKS(23, "进攻"),
    DANGEROUS_ATTACKS(24, "危险进攻"),
    POSSESSION(25, "控球率"),
    EXTRA_TIME_ENDED(26, "加时赛结束"),
    PENALTY_SHOOTOUT_ENDED(27, "点球大战结束"),
    PENALTY_DURING_SHOOTOUT(29, "点球(点球大战)"),
    PENALTY_MISSED_DURING_SHOOTOUT(30, "点球未进(点球大战)");

    private Integer value;

    private String desc;

}
