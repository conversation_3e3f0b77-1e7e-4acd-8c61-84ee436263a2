# 足球彩票系统文档中心

欢迎来到足球彩票系统的文档中心！这里包含了项目的完整技术文档，帮助开发者快速了解和使用本系统。

## 📚 文档目录

### 1. [技术文档](./技术文档.md)
**完整的技术架构和开发指南**
- 项目概述和技术特点
- 技术栈详细说明
- 项目架构和模块设计
- 数据库设计和核心业务表
- 开发规范和最佳实践
- 安全机制和性能优化
- 测试策略和扩展指南

### 2. [API接口文档](./API接口文档.md)
**RESTful API接口详细说明**
- 接口概述和统一响应格式
- 认证授权机制
- 用户管理接口
- 会员管理接口
- 支付管理接口
- 系统管理接口
- 文件管理接口
- 错误码说明和调用示例

### 3. [部署运维文档](./部署运维文档.md)
**生产环境部署和运维指南**
- 环境要求和软件依赖
- 本地开发环境搭建
- 应用配置和编译打包
- 多种部署方式（传统/Docker/K8s）
- Nginx配置和负载均衡
- 监控配置和运维脚本
- 备份策略和故障排查

## 🏗️ 系统架构

本系统采用Spring Boot多模块架构，支持多租户SaaS模式：

```
足球彩票系统
├── 前端层 (Vue3/Vue2/uni-app)
├── 网关层 (Nginx/Spring Gateway)
├── 应用层 (mir-server)
├── 业务模块层
│   ├── 系统管理 (mir-module-system)
│   ├── 会员中心 (mir-module-member)
│   ├── 支付系统 (mir-module-pay)
│   ├── 基础设施 (mir-module-infra)
│   └── 微信公众号 (mir-module-mp)
├── 框架层 (mir-framework)
└── 数据层 (MySQL/Redis/MinIO)
```

## 🚀 快速开始

### 环境要求
- JDK 1.8+
- MySQL 5.7+
- Redis 5.0+
- Maven 3.6+

### 快速启动
1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd football-lottery
   ```

2. **配置数据库**
   ```sql
   CREATE DATABASE mir_football DEFAULT CHARACTER SET utf8mb4;
   ```

3. **导入数据**
   ```bash
   mysql -u root -p mir_football < sql/mysql/ruoyi-vue-pro.sql
   ```

4. **修改配置**
   ```yaml
   # mir-server/src/main/resources/application-dev.yaml
   spring:
     datasource:
       druid:
         master:
           url: ****************************************
           username: your_username
           password: your_password
   ```

5. **启动应用**
   ```bash
   mvn clean package -Dmaven.test.skip=true
   java -jar mir-server/target/mir-server.jar
   ```

6. **访问系统**
   - 后端API: http://localhost:48080
   - API文档: http://localhost:48080/doc.html
   - 默认账号: admin/admin123

## 🔧 核心功能

### 系统管理
- ✅ 用户管理 - 管理员用户的增删改查
- ✅ 角色管理 - 角色权限分配和数据权限
- ✅ 菜单管理 - 系统菜单和权限配置
- ✅ 部门管理 - 组织架构管理
- ✅ 字典管理 - 系统字典数据维护
- ✅ 操作日志 - 系统操作审计和登录日志

### 会员中心
- ✅ 会员管理 - 会员信息和状态管理
- ✅ 会员等级 - 等级体系和权益配置
- ✅ 积分管理 - 积分获取和消费记录
- ✅ 签到功能 - 每日签到奖励机制

### 支付系统
- ✅ 支付应用 - 支付渠道配置管理
- ✅ 支付订单 - 订单支付流程处理
- ✅ 退款管理 - 退款流程和记录
- ✅ 钱包系统 - 用户余额和交易记录

### 基础设施
- ✅ 文件管理 - 文件上传和存储服务
- ✅ 配置管理 - 系统参数动态配置
- ✅ 定时任务 - 任务调度和执行记录
- ✅ 代码生成 - 自动生成CRUD代码
- ✅ API文档 - Swagger接口文档

## 🛠️ 技术栈

### 后端技术
| 技术 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 2.7.18 | 应用开发框架 |
| Spring Security | 5.7.11 | 安全认证框架 |
| MyBatis Plus | 3.5.7 | ORM持久化框架 |
| MySQL | 5.7/8.0+ | 关系型数据库 |
| Redis | 5.0+ | 缓存数据库 |
| Swagger | 1.7.0 | API文档工具 |

### 前端技术
| 技术 | 说明 |
|------|------|
| Vue3 + Element Plus | 现代化管理后台 |
| Vue3 + Ant Design | 企业级管理后台 |
| Vue2 + Element UI | 经典版管理后台 |
| uni-app | 跨平台移动端 |

## 📖 开发规范

### 代码规范
- 遵循《阿里巴巴Java开发手册》
- 使用Lombok简化代码
- 统一异常处理机制
- 完善的单元测试覆盖

### API设计
- RESTful API设计风格
- 统一的响应格式
- 完整的参数校验
- 详细的接口文档

### 数据库设计
- 统一的表命名规范
- 完整的索引设计
- 多租户数据隔离
- 软删除和审计字段

## 🔒 安全特性

- **认证授权**: JWT Token + Spring Security
- **权限控制**: RBAC模型 + 数据权限
- **数据安全**: 敏感数据加密 + SQL注入防护
- **接口安全**: XSS防护 + CSRF防护
- **多租户**: SaaS模式数据隔离

## 📊 性能优化

- **缓存策略**: Redis多级缓存
- **数据库优化**: 索引优化 + 读写分离
- **连接池**: Druid连接池优化
- **异步处理**: 消息队列异步化
- **监控告警**: 完善的监控体系

## 🚀 部署方案

### 开发环境
- 本地IDE直接运行
- 内嵌Tomcat容器
- H2内存数据库（可选）

### 测试环境
- Docker容器化部署
- MySQL + Redis
- Nginx反向代理

### 生产环境
- Kubernetes集群部署
- 高可用数据库集群
- 负载均衡 + 自动扩缩容
- 完善的监控和日志

## 📞 技术支持

### 问题反馈
- 🐛 Bug报告: 请在Issues中提交
- 💡 功能建议: 欢迎提出改进建议
- ❓ 使用问题: 查看文档或提交Issue

### 联系方式
- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 📱 微信群: 扫码加入

### 相关链接
- 🏠 项目主页: https://github.com/your-org/football-lottery
- 📚 在线文档: https://docs.your-domain.com
- 🎯 演示地址: https://demo.your-domain.com

## 📄 许可证

本项目采用 [MIT License](../LICENSE) 开源协议，个人和企业可100%免费使用。

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**

**🔄 文档持续更新中，欢迎贡献和反馈！**
