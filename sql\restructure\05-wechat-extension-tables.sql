-- =============================================
-- 足球彩票系统 - 微信扩展表
-- 微信模块的扩展功能表
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 微信扩展功能表
-- =============================================

-- 微信小程序用户
CREATE TABLE IF NOT EXISTS `mp_mini_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `openid` varchar(100) NOT NULL COMMENT '粉丝标识',
    `correlation_id` varchar(100) DEFAULT NULL COMMENT '公众号粉丝表和平台用户表关联ID',
    `union_id` varchar(100) DEFAULT NULL COMMENT '微信生态唯一标识',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_openid` (`openid`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序用户';

-- 微信素材
CREATE TABLE IF NOT EXISTS `mp_material` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `account_id` bigint NOT NULL COMMENT '微信账号编号',
    `media_id` varchar(100) NOT NULL COMMENT '微信媒体编号',
    `type` varchar(20) NOT NULL COMMENT '素材类型',
    `name` varchar(100) NOT NULL COMMENT '素材名称',
    `url` varchar(500) DEFAULT NULL COMMENT '素材地址',
    `permanent` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否永久素材',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信素材';

-- 微信自动回复
CREATE TABLE IF NOT EXISTS `mp_auto_reply` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `account_id` bigint NOT NULL COMMENT '微信账号编号',
    `app_id` varchar(128) NOT NULL COMMENT '微信AppID',
    `type` tinyint NOT NULL COMMENT '回复类型',
    `request_keyword` varchar(255) DEFAULT NULL COMMENT '请求关键字',
    `request_match` tinyint DEFAULT NULL COMMENT '请求匹配类型',
    `request_message_type` varchar(30) DEFAULT NULL COMMENT '请求消息类型',
    `response_message_type` varchar(30) NOT NULL COMMENT '回复消息类型',
    `response_content` varchar(1024) DEFAULT NULL COMMENT '回复消息内容',
    `response_media_id` varchar(200) DEFAULT NULL COMMENT '回复媒体文件编号',
    `response_media_url` varchar(1024) DEFAULT NULL COMMENT '回复媒体文件地址',
    `response_title` varchar(100) DEFAULT NULL COMMENT '回复标题',
    `response_description` varchar(256) DEFAULT NULL COMMENT '回复描述',
    `response_thumb_media_id` varchar(200) DEFAULT NULL COMMENT '回复缩略图媒体编号',
    `response_articles` varchar(1024) DEFAULT NULL COMMENT '回复图文消息',
    `response_music_url` varchar(1024) DEFAULT NULL COMMENT '回复音乐链接',
    `response_hq_music_url` varchar(1024) DEFAULT NULL COMMENT '回复高质量音乐链接',
    `response_thumb_media_url` varchar(1024) DEFAULT NULL COMMENT '回复缩略图链接',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信自动回复';

-- 微信点击日志
CREATE TABLE IF NOT EXISTS `mp_click_logs` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `account_id` bigint NOT NULL COMMENT '微信账号编号',
    `openid` varchar(100) NOT NULL COMMENT '微信openid',
    `event_key` varchar(100) DEFAULT NULL COMMENT '事件key',
    `click_type` varchar(50) DEFAULT NULL COMMENT '点击类型',
    `scene_str` varchar(255) DEFAULT NULL COMMENT '场景值',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_account_id` (`account_id`),
    KEY `idx_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信点击日志';

-- 微信其他事件日志
CREATE TABLE IF NOT EXISTS `mp_other_even_logs` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `account_id` bigint NOT NULL COMMENT '微信账号编号',
    `openid` varchar(100) NOT NULL COMMENT '微信openid',
    `event_type` varchar(50) NOT NULL COMMENT '事件类型',
    `event_key` varchar(100) DEFAULT NULL COMMENT '事件key',
    `event_data` text COMMENT '事件数据',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_account_id` (`account_id`),
    KEY `idx_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信其他事件日志';

-- 微信支付配置日志
CREATE TABLE IF NOT EXISTS `mp_pay_config_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `account_id` bigint NOT NULL COMMENT '微信账号编号',
    `config_type` varchar(50) NOT NULL COMMENT '配置类型',
    `config_data` text COMMENT '配置数据',
    `operation_type` varchar(20) NOT NULL COMMENT '操作类型',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信支付配置日志';

-- =============================================
-- 企业微信扩展表
-- =============================================

-- 企业微信设置
CREATE TABLE IF NOT EXISTS `wx_work_setting` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `user_id` varchar(100) NOT NULL COMMENT '企微号id',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
    `name` varchar(100) DEFAULT NULL COMMENT '名称',
    `qr_code_url` varchar(500) DEFAULT NULL COMMENT '二维码地址',
    `hk_url` varchar(500) DEFAULT NULL COMMENT '获客链接地址',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 0：启用，1：停用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='企业微信设置';

-- 企业微信外部联系人
CREATE TABLE IF NOT EXISTS `wx_external_contact` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `external_userid` varchar(100) NOT NULL COMMENT '外部联系人userid',
    `union_id` varchar(100) DEFAULT NULL COMMENT '微信unionid',
    `kf_user_id` varchar(100) DEFAULT NULL COMMENT '客服用户id',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='企业微信外部联系人';

-- 企业微信联系方式配置
CREATE TABLE IF NOT EXISTS `wx_external_contact_way_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `work_id` bigint NOT NULL COMMENT '企微号ID',
    `author_id` bigint NOT NULL COMMENT '作者ID',
    `work_user_id` varchar(100) NOT NULL COMMENT '企微客服号userId',
    `config_id` varchar(100) NOT NULL COMMENT '企微官方返回的配置ID',
    `qr_code` varchar(500) DEFAULT NULL COMMENT '联系客服的二维码地址',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_work_id` (`work_id`),
    KEY `idx_author_id` (`author_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='企业微信联系方式配置';

SET FOREIGN_KEY_CHECKS = 1;
