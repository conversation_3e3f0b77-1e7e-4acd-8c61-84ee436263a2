#!/bin/bash

# =============================================
# 足球彩票数据库完整分析脚本
# 使用增强的通用数据库分析器
# =============================================

echo "========================================"
echo "足球彩票数据库完整分析"
echo "========================================"

# 检查通用分析器是否存在
if [ ! -f "scripts/universal-database-analyzer.sh" ]; then
    echo "❌ 找不到通用数据库分析器脚本"
    exit 1
fi

# 设置权限
chmod +x scripts/universal-database-analyzer.sh

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

echo "开始完整分析数据库: $DB_NAME"
echo "输出目录: ./database-analysis"
echo

# 执行完整分析
./scripts/universal-database-analyzer.sh \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --user="$DB_USER" \
    --password="$DB_PASSWORD" \
    --database="$DB_NAME" \
    --output="./database-analysis" \
    --with-structure \
    --with-er-diagram \
    --with-data-dict

if [ $? -eq 0 ]; then
    echo
    echo "✅ 分析完成！生成的文件："
    echo "📋 基础分析报告 - 表分类和统计"
    echo "📊 表结构详情 - 每个表的字段详情"
    echo "📖 数据字典 - 完整的数据字典"
    echo "🎨 ER图 - Mermaid格式的ER图"
    echo
    echo "💡 提示："
    echo "- 可以在 https://mermaid.live/ 查看ER图"
    echo "- 所有文件都保存在 ./database-analysis/ 目录中"
else
    echo "❌ 分析失败"
    exit 1
fi
