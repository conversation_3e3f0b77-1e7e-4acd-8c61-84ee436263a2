# 数据字典

## 📊 数据库: sports_gaming

**生成时间**: 2025年07月11日 17:23:11

## 📋 表概览

| 序号 | 表名 | 注释 | 字段数 | 记录数 |
|------|------|------|--------|--------|
| 1 | account_statistic
 |  | 0 |  |
| 2 | account_users
 |  | 0 |  |
| 3 | article_push_config
 |  | 0 |  |
| 4 | author_accomplishment
 |  | 0 |  |
| 5 | author_article
 |  | 0 |  |
| 6 | author_article_append
 |  | 0 |  |
| 7 | author_article_pv_logs
 |  | 0 |  |
| 8 | author_audit
 |  | 0 |  |
| 9 | author_audit_copy1
 |  | 0 |  |
| 10 | author_commission_rate
 |  | 0 |  |
| 11 | author_day_report
 |  | 0 |  |
| 12 | author_hit_rate
 |  | 0 |  |
| 13 | author_info
 |  | 0 |  |
| 14 | author_privilege_set
 |  | 0 |  |
| 15 | author_withdraw_logs
 |  | 0 |  |
| 16 | authors
 |  | 0 |  |
| 17 | broomer_match_scheme
 |  | 0 |  |
| 18 | broomers
 |  | 0 |  |
| 19 | football_bd_odds
 |  | 0 |  |
| 20 | football_bd_result
 |  | 0 |  |
| 21 | football_bd_sf_odds
 |  | 0 |  |
| 22 | football_bd_sf_result
 |  | 0 |  |
| 23 | football_bo_live_odds
 |  | 0 |  |
| 24 | football_company
 |  | 0 |  |
| 25 | football_half_live_odds
 |  | 0 |  |
| 26 | football_issue
 |  | 0 |  |
| 27 | football_jc_odds
 |  | 0 |  |
| 28 | football_jc_result
 |  | 0 |  |
| 29 | football_live_odds
 |  | 0 |  |
| 30 | football_match
 |  | 0 |  |
| 31 | football_match_result
 |  | 0 |  |
| 32 | football_tc_match
 |  | 0 |  |
| 33 | gold_order
 |  | 0 |  |
| 34 | guess_records
 |  | 0 |  |
| 35 | home_banner
 |  | 0 |  |
| 36 | infra_api_access_log
 |  | 0 |  |
| 37 | infra_api_error_log
 |  | 0 |  |
| 38 | infra_codegen_column
 |  | 0 |  |
| 39 | infra_codegen_table
 |  | 0 |  |
| 40 | infra_config
 |  | 0 |  |
| 41 | infra_data_source_config
 |  | 0 |  |
| 42 | infra_file
 |  | 0 |  |
| 43 | infra_file_config
 |  | 0 |  |
| 44 | infra_file_content
 |  | 0 |  |
| 45 | infra_job
 |  | 0 |  |
| 46 | infra_job_log
 |  | 0 |  |
| 47 | match_category
 |  | 0 |  |
| 48 | match_coach
 |  | 0 |  |
| 49 | match_competition
 |  | 0 |  |
| 50 | match_country
 |  | 0 |  |
| 51 | match_future_record
 |  | 0 |  |
| 52 | match_history
 |  | 0 |  |
| 53 | match_lineup_detail
 |  | 0 |  |
| 54 | match_list
 |  | 0 |  |
| 55 | match_live_info
 |  | 0 |  |
| 56 | match_odds
 |  | 0 |  |
| 57 | match_player
 |  | 0 |  |
| 58 | match_player_info
 |  | 0 |  |
| 59 | match_player_transfer
 |  | 0 |  |
| 60 | match_point_rank
 |  | 0 |  |
| 61 | match_referee
 |  | 0 |  |
| 62 | match_season
 |  | 0 |  |
| 63 | match_stage
 |  | 0 |  |
| 64 | match_stats
 |  | 0 |  |
| 65 | match_team
 |  | 0 |  |
| 66 | matches
 |  | 0 |  |
| 67 | member_address
 |  | 0 |  |
| 68 | member_attention
 |  | 0 |  |
| 69 | member_author_privilege
 |  | 0 |  |
| 70 | member_bind_record
 |  | 0 |  |
| 71 | member_config
 |  | 0 |  |
| 72 | member_experience_record
 |  | 0 |  |
| 73 | member_group
 |  | 0 |  |
| 74 | member_level
 |  | 0 |  |
| 75 | member_level_record
 |  | 0 |  |
| 76 | member_match_attention
 |  | 0 |  |
| 77 | member_match_chat
 |  | 0 |  |
| 78 | member_match_vote
 |  | 0 |  |
| 79 | member_point_record
 |  | 0 |  |
| 80 | member_privilege_log
 |  | 0 |  |
| 81 | member_settlement_info
 |  | 0 |  |
| 82 | member_sign_in_config
 |  | 0 |  |
| 83 | member_sign_in_record
 |  | 0 |  |
| 84 | member_tag
 |  | 0 |  |
| 85 | member_user
 |  | 0 |  |
| 86 | member_user_bak
 |  | 0 |  |
| 87 | member_user_balance_logs
 |  | 0 |  |
| 88 | member_user_ex_balance_logs
 |  | 0 |  |
| 89 | member_user_gold_logs
 |  | 0 |  |
| 90 | mp_account
 |  | 0 |  |
| 91 | mp_auto_reply
 |  | 0 |  |
| 92 | mp_click_logs
 |  | 0 |  |
| 93 | mp_material
 |  | 0 |  |
| 94 | mp_menu
 |  | 0 |  |
| 95 | mp_message
 |  | 0 |  |
| 96 | mp_mini_user
 |  | 0 |  |
| 97 | mp_other_even_logs
 |  | 0 |  |
| 98 | mp_pay_config_log
 |  | 0 |  |
| 99 | mp_tag
 |  | 0 |  |
| 100 | mp_template_config
 |  | 0 |  |
| 101 | mp_user
 |  | 0 |  |
| 102 | partner_audit
 |  | 0 |  |
| 103 | partner_commission_rate
 |  | 0 |  |
| 104 | partner_divide_config
 |  | 0 |  |
| 105 | partner_info
 |  | 0 |  |
| 106 | partner_invite_logs
 |  | 0 |  |
| 107 | partner_settlement_info
 |  | 0 |  |
| 108 | partner_withdraw_logs
 |  | 0 |  |
| 109 | pay_app
 |  | 0 |  |
| 110 | pay_bank_info
 |  | 0 |  |
| 111 | pay_channel
 |  | 0 |  |
| 112 | pay_demo_order
 |  | 0 |  |
| 113 | pay_demo_transfer
 |  | 0 |  |
| 114 | pay_notify_log
 |  | 0 |  |
| 115 | pay_notify_task
 |  | 0 |  |
| 116 | pay_order
 |  | 0 |  |
| 117 | pay_order_extension
 |  | 0 |  |
| 118 | pay_refund
 |  | 0 |  |
| 119 | pay_transfer
 |  | 0 |  |
| 120 | pay_wallet
 |  | 0 |  |
| 121 | pay_wallet_recharge
 |  | 0 |  |
| 122 | pay_wallet_recharge_package
 |  | 0 |  |
| 123 | pay_wallet_transaction
 |  | 0 |  |
| 124 | play_type
 |  | 0 |  |
| 125 | playing_method
 |  | 0 |  |
| 126 | privilege_order
 |  | 0 |  |
| 127 | push_amount_config
 |  | 0 |  |
| 128 | qrtz_blob_triggers
 |  | 0 |  |
| 129 | qrtz_calendars
 |  | 0 |  |
| 130 | qrtz_cron_triggers
 |  | 0 |  |
| 131 | qrtz_fired_triggers
 |  | 0 |  |
| 132 | qrtz_job_details
 |  | 0 |  |
| 133 | qrtz_locks
 |  | 0 |  |
| 134 | qrtz_paused_trigger_grps
 |  | 0 |  |
| 135 | qrtz_scheduler_state
 |  | 0 |  |
| 136 | qrtz_simple_triggers
 |  | 0 |  |
| 137 | qrtz_simprop_triggers
 |  | 0 |  |
| 138 | qrtz_triggers
 |  | 0 |  |
| 139 | recommend_author
 |  | 0 |  |
| 140 | recommend_user_register_logs
 |  | 0 |  |
| 141 | refund_order
 |  | 0 |  |
| 142 | report_operation_day
 |  | 0 |  |
| 143 | scheme_order
 |  | 0 |  |
| 144 | scheme_play
 |  | 0 |  |
| 145 | sys_configs
 |  | 0 |  |
| 146 | system_command
 |  | 0 |  |
| 147 | system_dept
 |  | 0 |  |
| 148 | system_dict_data
 |  | 0 |  |
| 149 | system_dict_type
 |  | 0 |  |
| 150 | system_login_log
 |  | 0 |  |
| 151 | system_mail_account
 |  | 0 |  |
| 152 | system_mail_log
 |  | 0 |  |
| 153 | system_mail_template
 |  | 0 |  |
| 154 | system_menu
 |  | 0 |  |
| 155 | system_notice
 |  | 0 |  |
| 156 | system_notify_message
 |  | 0 |  |
| 157 | system_notify_template
 |  | 0 |  |
| 158 | system_oauth2_access_token
 |  | 0 |  |
| 159 | system_oauth2_approve
 |  | 0 |  |
| 160 | system_oauth2_client
 |  | 0 |  |
| 161 | system_oauth2_code
 |  | 0 |  |
| 162 | system_oauth2_refresh_token
 |  | 0 |  |
| 163 | system_operate_log
 |  | 0 |  |
| 164 | system_post
 |  | 0 |  |
| 165 | system_role
 |  | 0 |  |
| 166 | system_role_menu
 |  | 0 |  |
| 167 | system_sensitive_word
 |  | 0 |  |
| 168 | system_sms_channel
 |  | 0 |  |
| 169 | system_sms_code
 |  | 0 |  |
| 170 | system_sms_log
 |  | 0 |  |
| 171 | system_sms_template
 |  | 0 |  |
| 172 | system_social_client
 |  | 0 |  |
| 173 | system_social_user
 |  | 0 |  |
| 174 | system_social_user_bind
 |  | 0 |  |
| 175 | system_tenant
 |  | 0 |  |
| 176 | system_tenant_package
 |  | 0 |  |
| 177 | system_user_post
 |  | 0 |  |
| 178 | system_user_role
 |  | 0 |  |
| 179 | system_users
 |  | 0 |  |
| 180 | third_pay_channel
 |  | 0 |  |
| 181 | third_pay_sqb_config
 |  | 0 |  |
| 182 | wecom_setting
 |  | 0 |  |
| 183 | wx_external_contact
 |  | 0 |  |
| 184 | wx_external_contact_way_config
 |  | 0 |  |
| 185 | wx_work_setting
 |  | 0 |  |
| 186 | yudao_demo01_contact
 |  | 0 |  |
| 187 | yudao_demo02_category
 |  | 0 |  |
| 188 | yudao_demo03_course
 |  | 0 |  |
| 189 | yudao_demo03_grade
 |  | 0 |  |
| 190 | yudao_demo03_student
 |  | 0 |  |

## 📝 字段详情

### account_statistic

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### account_users

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### article_push_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_accomplishment

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_article

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_article_append

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_article_pv_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_audit

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_audit_copy1

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_commission_rate

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_day_report

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_hit_rate

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_info

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_privilege_set

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### author_withdraw_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### authors

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### broomer_match_scheme

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### broomers

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_bd_odds

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_bd_result

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_bd_sf_odds

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_bd_sf_result

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_bo_live_odds

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_company

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_half_live_odds

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_issue

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_jc_odds

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_jc_result

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_live_odds

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_match

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_match_result

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### football_tc_match

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### gold_order

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### guess_records

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### home_banner

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_api_access_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_api_error_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_codegen_column

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_codegen_table

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_data_source_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_file

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_file_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_file_content

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_job

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### infra_job_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_category

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_coach

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_competition

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_country

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_future_record

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_history

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_lineup_detail

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_list

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_live_info

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_odds

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_player

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_player_info

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_player_transfer

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_point_rank

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_referee

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_season

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_stage

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_stats

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### match_team

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### matches

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_address

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_attention

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_author_privilege

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_bind_record

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_experience_record

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_group

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_level

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_level_record

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_match_attention

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_match_chat

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_match_vote

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_point_record

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_privilege_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_settlement_info

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_sign_in_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_sign_in_record

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_tag

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_user

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_user_bak

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_user_balance_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_user_ex_balance_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### member_user_gold_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_account

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_auto_reply

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_click_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_material

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_menu

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_message

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_mini_user

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_other_even_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_pay_config_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_tag

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_template_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### mp_user

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### partner_audit

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### partner_commission_rate

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### partner_divide_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### partner_info

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### partner_invite_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### partner_settlement_info

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### partner_withdraw_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_app

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_bank_info

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_channel

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_demo_order

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_demo_transfer

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_notify_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_notify_task

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_order

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_order_extension

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_refund

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_transfer

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_wallet

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_wallet_recharge

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_wallet_recharge_package

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### pay_wallet_transaction

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### play_type

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### playing_method

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### privilege_order

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### push_amount_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_blob_triggers

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_calendars

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_cron_triggers

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_fired_triggers

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_job_details

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_locks

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_paused_trigger_grps

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_scheduler_state

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_simple_triggers

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_simprop_triggers

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### qrtz_triggers

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### recommend_author

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### recommend_user_register_logs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### refund_order

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### report_operation_day

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### scheme_order

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### scheme_play

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### sys_configs

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_command

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_dept

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_dict_data

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_dict_type

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_login_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_mail_account

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_mail_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_mail_template

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_menu

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_notice

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_notify_message

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_notify_template

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_oauth2_access_token

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_oauth2_approve

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_oauth2_client

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_oauth2_code

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_oauth2_refresh_token

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_operate_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_post

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_role

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_role_menu

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_sensitive_word

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_sms_channel

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_sms_code

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_sms_log

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_sms_template

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_social_client

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_social_user

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_social_user_bind

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_tenant

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_tenant_package

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_user_post

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_user_role

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### system_users

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### third_pay_channel

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### third_pay_sqb_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### wecom_setting

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### wx_external_contact

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### wx_external_contact_way_config

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### wx_work_setting

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### yudao_demo01_contact

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### yudao_demo02_category

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### yudao_demo03_course

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### yudao_demo03_grade

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

### yudao_demo03_student

**表说明**: 

| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |
|------|------|------|-----|--------|------|

