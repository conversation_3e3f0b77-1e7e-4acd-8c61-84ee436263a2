-- =============================================
-- 足球彩票系统多租户改造 - 简单状态检查脚本
-- 目的：快速检查当前改造状态，兼容所有MySQL版本
-- =============================================

SELECT '=== 多租户改造状态检查（简化版） ===' AS message;

-- =============================================
-- 1. 检查已添加tenant_id字段的表
-- =============================================

SELECT '1. 已添加tenant_id字段的表:' AS step;

SELECT 
    TABLE_NAME as '表名',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY 
    TABLE_NAME;

-- 统计数量
SELECT 
    CONCAT('已添加tenant_id字段的表数量: ', COUNT(*)) as '统计结果'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id';

-- =============================================
-- 2. 检查重要表的字段状态
-- =============================================

SELECT '2. 重要表的tenant_id字段检查:' AS step;

SELECT 
    '会员用户表' as '表类型',
    'member_user' as '表名',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_user'
        ) THEN '表存在'
        ELSE '表不存在'
    END as '表状态',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_user' AND COLUMN_NAME = 'tenant_id'
        ) THEN '✅ 已添加'
        ELSE '❌ 未添加'
    END as 'tenant_id字段状态'

UNION ALL

SELECT 
    '会员等级表' as '表类型',
    'member_level' as '表名',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_level'
        ) THEN '表存在'
        ELSE '表不存在'
    END as '表状态',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_level' AND COLUMN_NAME = 'tenant_id'
        ) THEN '✅ 已添加'
        ELSE '❌ 未添加'
    END as 'tenant_id字段状态'

UNION ALL

SELECT 
    '支付应用表' as '表类型',
    'pay_app' as '表名',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_app'
        ) THEN '表存在'
        ELSE '表不存在'
    END as '表状态',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_app' AND COLUMN_NAME = 'tenant_id'
        ) THEN '✅ 已添加'
        ELSE '❌ 未添加'
    END as 'tenant_id字段状态'

UNION ALL

SELECT 
    '支付订单表' as '表类型',
    'pay_order' as '表名',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_order'
        ) THEN '表存在'
        ELSE '表不存在'
    END as '表状态',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_order' AND COLUMN_NAME = 'tenant_id'
        ) THEN '✅ 已添加'
        ELSE '❌ 未添加'
    END as 'tenant_id字段状态'

UNION ALL

SELECT 
    '用户钱包表' as '表类型',
    'pay_wallet' as '表名',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_wallet'
        ) THEN '表存在'
        ELSE '表不存在'
    END as '表状态',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_wallet' AND COLUMN_NAME = 'tenant_id'
        ) THEN '✅ 已添加'
        ELSE '❌ 未添加'
    END as 'tenant_id字段状态'

UNION ALL

SELECT 
    '文章表' as '表类型',
    'article' as '表名',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'article'
        ) THEN '表存在'
        ELSE '表不存在'
    END as '表状态',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'article' AND COLUMN_NAME = 'tenant_id'
        ) THEN '✅ 已添加'
        ELSE '❌ 未添加'
    END as 'tenant_id字段状态';

-- =============================================
-- 3. 检查租户相关索引
-- =============================================

SELECT '3. 租户相关索引检查:' AS step;

SELECT 
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as '索引字段'
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME LIKE '%tenant%'
GROUP BY 
    TABLE_NAME, INDEX_NAME
ORDER BY 
    TABLE_NAME, INDEX_NAME;

-- 统计索引数量
SELECT 
    CONCAT('tenant相关索引数量: ', COUNT(DISTINCT CONCAT(TABLE_NAME, '.', INDEX_NAME))) as '索引统计'
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME LIKE '%tenant%';

-- =============================================
-- 4. 检查系统租户表
-- =============================================

SELECT '4. 系统租户表检查:' AS step;

-- 检查system_tenant表是否存在
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'system_tenant'
        ) THEN '✅ system_tenant表存在'
        ELSE '❌ system_tenant表不存在'
    END as '租户表状态';

-- 检查默认租户（如果表存在）
SELECT 
    CASE 
        WHEN NOT EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'system_tenant'
        ) THEN '⚠️  system_tenant表不存在，无法检查默认租户'
        WHEN EXISTS (SELECT 1 FROM system_tenant WHERE id = 1) 
        THEN '✅ 默认租户存在'
        ELSE '❌ 默认租户不存在'
    END as '默认租户状态';

-- =============================================
-- 5. 生成改造建议
-- =============================================

SELECT '5. 改造建议:' AS step;

SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'tenant_id'
        ) = 0 
        THEN '建议: 执行 01-add-tenant-fields.sql 添加租户字段'
        
        WHEN (
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE() AND INDEX_NAME LIKE '%tenant%'
        ) = 0
        THEN '建议: 执行 02-create-indexes.sql 创建租户索引'
        
        WHEN NOT EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'system_tenant'
        )
        THEN '建议: 执行 03-migrate-data.sql 创建租户表和迁移数据'
        
        ELSE '建议: 字段和索引已就绪，可以执行数据迁移或启用多租户配置'
    END as '下一步操作建议';

SELECT '=== 状态检查完成 ===' AS message;
