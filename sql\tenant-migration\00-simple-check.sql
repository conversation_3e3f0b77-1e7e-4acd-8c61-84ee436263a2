-- =============================================
-- 足球彩票系统多租户改造 - 超简单状态检查脚本
-- 目的：快速检查当前改造状态，避免复杂SQL语法
-- =============================================

SELECT '=== 多租户改造状态检查 ===' AS message;

-- =============================================
-- 1. 检查已添加tenant_id字段的表
-- =============================================

SELECT '1. 已添加tenant_id字段的表:' AS step;

SELECT
    TABLE_NAME as table_name,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment
FROM
    INFORMATION_SCHEMA.COLUMNS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY
    TABLE_NAME;

-- 统计数量
SELECT
    CONCAT('已添加tenant_id字段的表数量: ', COUNT(*)) as result
FROM
    INFORMATION_SCHEMA.COLUMNS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id';

-- =============================================
-- 2. 检查重要表的字段状态（分别查询，避免复杂UNION）
-- =============================================

SELECT '2. 重要表的tenant_id字段检查:' AS step;

-- 会员用户表
SELECT
    'member_user' as table_name,
    CASE
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_user')
        THEN 'table_exists'
        ELSE 'table_not_exists'
    END as table_status,
    CASE
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_user' AND COLUMN_NAME = 'tenant_id')
        THEN 'field_added'
        ELSE 'field_missing'
    END as tenant_id_status;

-- 支付订单表
SELECT
    'pay_order' as table_name,
    CASE
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_order')
        THEN 'table_exists'
        ELSE 'table_not_exists'
    END as table_status,
    CASE
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_order' AND COLUMN_NAME = 'tenant_id')
        THEN 'field_added'
        ELSE 'field_missing'
    END as tenant_id_status;

-- 文章表
SELECT
    'article' as table_name,
    CASE
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'article')
        THEN 'table_exists'
        ELSE 'table_not_exists'
    END as table_status,
    CASE
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'article' AND COLUMN_NAME = 'tenant_id')
        THEN 'field_added'
        ELSE 'field_missing'
    END as tenant_id_status;

-- 用户钱包表
SELECT
    'pay_wallet' as table_name,
    CASE
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_wallet')
        THEN 'table_exists'
        ELSE 'table_not_exists'
    END as table_status,
    CASE
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_wallet' AND COLUMN_NAME = 'tenant_id')
        THEN 'field_added'
        ELSE 'field_missing'
    END as tenant_id_status;

-- =============================================
-- 3. 检查租户相关索引
-- =============================================

SELECT '3. 租户相关索引检查:' AS step;

SELECT
    TABLE_NAME as table_name,
    INDEX_NAME as index_name,
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as index_columns
FROM
    INFORMATION_SCHEMA.STATISTICS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME LIKE '%tenant%'
GROUP BY
    TABLE_NAME, INDEX_NAME
ORDER BY
    TABLE_NAME, INDEX_NAME;

-- 统计索引数量
SELECT
    CONCAT('tenant相关索引数量: ', COUNT(DISTINCT CONCAT(TABLE_NAME, '.', INDEX_NAME))) as index_count
FROM
    INFORMATION_SCHEMA.STATISTICS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME LIKE '%tenant%';

-- =============================================
-- 4. 检查系统租户表
-- =============================================

SELECT '4. 系统租户表检查:' AS step;

-- 检查system_tenant表是否存在
SELECT
    CASE
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'system_tenant')
        THEN 'system_tenant_exists'
        ELSE 'system_tenant_not_exists'
    END as tenant_table_status;

-- =============================================
-- 5. 生成改造建议
-- =============================================

SELECT '5. 改造建议:' AS step;

SELECT
    CASE
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'tenant_id') = 0
        THEN 'suggestion: execute 01-add-tenant-fields.sql'
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND INDEX_NAME LIKE '%tenant%') = 0
        THEN 'suggestion: execute 02-create-indexes.sql'
        WHEN NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'system_tenant')
        THEN 'suggestion: execute 03-migrate-data.sql'
        ELSE 'suggestion: ready for multi-tenant configuration'
    END as next_step;

SELECT '=== 状态检查完成 ===' AS message;
