-- =============================================
-- 足球彩票系统多租户改造 - 创建租户索引
-- 目的：提高多租户查询性能
-- 注意事项：索引创建可能需要较长时间，建议在业务低峰期执行
-- 版本：2.0 - 支持重复执行，自动检查索引是否存在
-- =============================================

-- 创建存储过程用于安全创建索引
DELIMITER $$

DROP PROCEDURE IF EXISTS CreateIndexIfNotExists$$

CREATE PROCEDURE CreateIndexIfNotExists(
    IN index_name VARCHAR(64),
    IN table_name VARCHAR(64),
    IN index_columns TEXT
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查索引是否已存在
    SELECT COUNT(*) INTO index_exists
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = table_name
      AND INDEX_NAME = index_name;

    -- 如果索引不存在，则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, '(', index_columns, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        SELECT CONCAT('✅ 索引 ', index_name, ' 已创建') AS result;
    ELSE
        SELECT CONCAT('⚠️  索引 ', index_name, ' 已存在，跳过') AS result;
    END IF;
END$$

DELIMITER ;

-- =============================================
-- 1. 会员模块索引
-- =============================================

SELECT '开始创建会员模块索引...' AS message;

-- 会员用户表索引
CALL CreateIndexIfNotExists('idx_member_user_tenant_id', 'member_user', 'tenant_id');
CALL CreateIndexIfNotExists('idx_member_user_tenant_mobile', 'member_user', 'tenant_id, mobile');
CALL CreateIndexIfNotExists('idx_member_user_tenant_status', 'member_user', 'tenant_id, status');
CALL CreateIndexIfNotExists('idx_member_user_tenant_create_time', 'member_user', 'tenant_id, create_time');

-- 会员等级表索引
CALL CreateIndexIfNotExists('idx_member_level_tenant_id', 'member_level', 'tenant_id');
CALL CreateIndexIfNotExists('idx_member_level_tenant_status', 'member_level', 'tenant_id, status');
CALL CreateIndexIfNotExists('idx_member_level_tenant_level', 'member_level', 'tenant_id, level');

-- 会员等级记录表索引
CALL CreateIndexIfNotExists('idx_member_level_record_tenant_id', 'member_level_record', 'tenant_id');
CALL CreateIndexIfNotExists('idx_member_level_record_tenant_user', 'member_level_record', 'tenant_id, user_id');
CALL CreateIndexIfNotExists('idx_member_level_record_tenant_create_time', 'member_level_record', 'tenant_id, create_time');

-- 会员积分记录表索引
CALL CreateIndexIfNotExists('idx_member_point_record_tenant_id', 'member_point_record', 'tenant_id');
CALL CreateIndexIfNotExists('idx_member_point_record_tenant_user', 'member_point_record', 'tenant_id, user_id');
CALL CreateIndexIfNotExists('idx_member_point_record_tenant_create_time', 'member_point_record', 'tenant_id, create_time');

-- 会员签到记录表索引
CALL CreateIndexIfNotExists('idx_member_sign_in_record_tenant_id', 'member_sign_in_record', 'tenant_id');
CALL CreateIndexIfNotExists('idx_member_sign_in_record_tenant_user', 'member_sign_in_record', 'tenant_id, user_id');
CALL CreateIndexIfNotExists('idx_member_sign_in_record_tenant_day', 'member_sign_in_record', 'tenant_id, day');

-- =============================================
-- 2. 支付模块索引
-- =============================================

-- 支付应用表索引
CREATE INDEX idx_pay_app_tenant_id ON pay_app(tenant_id);
CREATE INDEX idx_pay_app_tenant_status ON pay_app(tenant_id, status);
CREATE INDEX idx_pay_app_tenant_app_key ON pay_app(tenant_id, app_key);

-- 支付订单表索引
CREATE INDEX idx_pay_order_tenant_id ON pay_order(tenant_id);
CREATE INDEX idx_pay_order_tenant_status ON pay_order(tenant_id, status);
CREATE INDEX idx_pay_order_tenant_app_id ON pay_order(tenant_id, app_id);
CREATE INDEX idx_pay_order_tenant_merchant_order_id ON pay_order(tenant_id, merchant_order_id);
CREATE INDEX idx_pay_order_tenant_create_time ON pay_order(tenant_id, create_time);
CREATE INDEX idx_pay_order_tenant_success_time ON pay_order(tenant_id, success_time);

-- 支付退款表索引
CREATE INDEX idx_pay_refund_tenant_id ON pay_refund(tenant_id);
CREATE INDEX idx_pay_refund_tenant_status ON pay_refund(tenant_id, status);
CREATE INDEX idx_pay_refund_tenant_order_id ON pay_refund(tenant_id, order_id);
CREATE INDEX idx_pay_refund_tenant_create_time ON pay_refund(tenant_id, create_time);

-- 用户钱包表索引
CREATE INDEX idx_pay_wallet_tenant_id ON pay_wallet(tenant_id);
CREATE INDEX idx_pay_wallet_tenant_user ON pay_wallet(tenant_id, user_id, user_type);

-- 钱包交易记录表索引
CREATE INDEX idx_pay_wallet_transaction_tenant_id ON pay_wallet_transaction(tenant_id);
CREATE INDEX idx_pay_wallet_transaction_tenant_wallet ON pay_wallet_transaction(tenant_id, wallet_id);
CREATE INDEX idx_pay_wallet_transaction_tenant_create_time ON pay_wallet_transaction(tenant_id, create_time);

-- =============================================
-- 3. 业务模块索引
-- =============================================

-- 文章表索引
CALL CreateIndexIfNotExists('idx_author_article_tenant_id', 'author_article', 'tenant_id');
CALL CreateIndexIfNotExists('idx_author_article_tenant_author', 'author_article', 'tenant_id, author_id');
CALL CreateIndexIfNotExists('idx_author_article_tenant_status', 'author_article', 'tenant_id, status');
CALL CreateIndexIfNotExists('idx_author_article_tenant_start_time', 'author_article', 'tenant_id, start_time');
CALL CreateIndexIfNotExists('idx_author_article_tenant_create_time', 'author_article', 'tenant_id, create_time');

-- 文章追加表索引
CALL CreateIndexIfNotExists('idx_author_article_append_tenant_id', 'author_article_append', 'tenant_id');
CALL CreateIndexIfNotExists('idx_author_article_append_tenant_article', 'author_article_append', 'tenant_id, article_id');

-- 球队信息表索引
CREATE INDEX idx_match_team_tenant_id ON match_team(tenant_id);
CREATE INDEX idx_match_team_tenant_name ON match_team(tenant_id, name);

-- =============================================
-- 4. 基础设施模块索引
-- =============================================

-- 文件表索引
CREATE INDEX idx_infra_file_tenant_id ON infra_file(tenant_id);
CREATE INDEX idx_infra_file_tenant_type ON infra_file(tenant_id, type);
CREATE INDEX idx_infra_file_tenant_create_time ON infra_file(tenant_id, create_time);

-- 文件配置表索引
CREATE INDEX idx_infra_file_config_tenant_id ON infra_file_config(tenant_id);

-- 代码生成表索引
CREATE INDEX idx_infra_codegen_table_tenant_id ON infra_codegen_table(tenant_id);

-- =============================================
-- 5. 微信公众号模块索引
-- =============================================

-- 微信账号表索引
CREATE INDEX idx_mp_account_tenant_id ON mp_account(tenant_id);
CREATE INDEX idx_mp_account_tenant_app_id ON mp_account(tenant_id, app_id);

-- 微信消息表索引
CREATE INDEX idx_mp_message_tenant_id ON mp_message(tenant_id);
CREATE INDEX idx_mp_message_tenant_account ON mp_message(tenant_id, account_id);
CREATE INDEX idx_mp_message_tenant_create_time ON mp_message(tenant_id, create_time);

-- 微信用户表索引
CREATE INDEX idx_mp_user_tenant_id ON mp_user(tenant_id);
CREATE INDEX idx_mp_user_tenant_account ON mp_user(tenant_id, account_id);
CREATE INDEX idx_mp_user_tenant_openid ON mp_user(tenant_id, openid);

-- 微信标签表索引
CREATE INDEX idx_mp_tag_tenant_id ON mp_tag(tenant_id);
CREATE INDEX idx_mp_tag_tenant_account ON mp_tag(tenant_id, account_id);

-- 微信菜单表索引
CREATE INDEX idx_mp_menu_tenant_id ON mp_menu(tenant_id);
CREATE INDEX idx_mp_menu_tenant_account ON mp_menu(tenant_id, account_id);

-- 微信自动回复表索引
CREATE INDEX idx_mp_auto_reply_tenant_id ON mp_auto_reply(tenant_id);
CREATE INDEX idx_mp_auto_reply_tenant_account ON mp_auto_reply(tenant_id, account_id);

-- =============================================
-- 6. 复合索引优化（根据业务查询模式）
-- =============================================

-- 会员相关复合索引
CREATE INDEX idx_member_user_tenant_mobile_status ON member_user(tenant_id, mobile, status);
CREATE INDEX idx_member_user_tenant_status_create_time ON member_user(tenant_id, status, create_time);

-- 支付相关复合索引
CREATE INDEX idx_pay_order_tenant_app_status ON pay_order(tenant_id, app_id, status);
CREATE INDEX idx_pay_order_tenant_status_create_time ON pay_order(tenant_id, status, create_time);

-- 文章相关复合索引
CREATE INDEX idx_article_tenant_author_status ON article(tenant_id, author_id, status);
CREATE INDEX idx_article_tenant_status_start_time ON article(tenant_id, status, start_time);

-- =============================================
-- 7. 验证索引创建结果
-- =============================================

-- 查看所有tenant_id相关的索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    NON_UNIQUE
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME LIKE '%tenant%'
ORDER BY 
    TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 检查索引大小和使用情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    ROUND(((INDEX_LENGTH) / 1024 / 1024), 2) AS 'Index Size (MB)'
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_LENGTH > 0
ORDER BY 
    INDEX_LENGTH DESC;

-- =============================================
-- 执行完成提示
-- =============================================
SELECT 'tenant_id索引创建完成，请执行下一步：数据迁移' AS message;
