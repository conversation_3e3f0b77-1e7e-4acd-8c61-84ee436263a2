#!/bin/bash

# =============================================
# 遗漏表分析脚本
# 检查重构方案中遗漏的表
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "遗漏表分析工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database/restructure

log_info "分析遗漏的表..."

# 获取所有表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

# 定义已包含在重构方案中的表
INCLUDED_TABLES=(
    # 框架表
    "system_tenant" "system_users" "system_role" "system_menu" "system_user_role" "system_role_menu"
    "system_dict_type" "system_dict_data"
    
    # 后台前端表
    "system_dept" "system_post" "system_notice" "infra_file" "infra_file_config"
    
    # 应用功能表
    "member_user" "member_level" "pay_app" "pay_order" "pay_wallet" "author_article" "banner"
    
    # 微信表
    "mp_account" "mp_user" "mp_message" "mp_menu" "mp_template_config" "mp_tag"
    
    # 微信扩展表
    "mp_mini_user" "mp_material" "mp_auto_reply" "mp_click_logs" "mp_other_even_logs" "mp_pay_config_log"
    "wx_work_setting" "wx_external_contact" "wx_external_contact_way_config"
)

# 生成遗漏表分析报告
cat > docs/database/restructure/missing-tables-analysis.md << 'EOF'
# 遗漏表分析报告

## 📊 概览

本报告分析重构方案中遗漏的数据库表，按重要性和功能分类。

## 🔍 分析结果

EOF

echo "**分析时间**: $(date)" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md

# 分析遗漏的表
> /tmp/missing_tables.txt

while read table_name; do
    if [ -n "$table_name" ]; then
        # 检查是否已包含在重构方案中
        IS_INCLUDED=0
        for included_table in "${INCLUDED_TABLES[@]}"; do
            if [ "$table_name" = "$included_table" ]; then
                IS_INCLUDED=1
                break
            fi
        done
        
        if [ $IS_INCLUDED -eq 0 ]; then
            echo "$table_name" >> /tmp/missing_tables.txt
        fi
    fi
done < /tmp/all_tables.txt

# 按类别分析遗漏的表
echo "## 🏗️ 遗漏表分类" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md

# 1. 模板相关表
echo "### 1. 模板相关表（Template Tables）" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "**重要性**: 🔴 高 - 系统通知功能必需" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md

TEMPLATE_TABLES=$(grep -E "template" /tmp/missing_tables.txt 2>/dev/null || echo "")
if [ -n "$TEMPLATE_TABLES" ]; then
    echo "$TEMPLATE_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            case "$table_name" in
                "system_mail_template") echo "- **$table_name**: $TABLE_INFO - 邮件模板配置" >> docs/database/restructure/missing-tables-analysis.md ;;
                "system_notify_template") echo "- **$table_name**: $TABLE_INFO - 站内信模板配置" >> docs/database/restructure/missing-tables-analysis.md ;;
                "system_sms_template") echo "- **$table_name**: $TABLE_INFO - 短信模板配置" >> docs/database/restructure/missing-tables-analysis.md ;;
                *) echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/missing-tables-analysis.md ;;
            esac
        fi
    done
fi

echo "" >> docs/database/restructure/missing-tables-analysis.md

# 2. 日志相关表
echo "### 2. 日志相关表（Log Tables）" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "**重要性**: 🟡 中 - 审计和监控功能" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md

LOG_TABLES=$(grep -E "log|logs" /tmp/missing_tables.txt 2>/dev/null || echo "")
if [ -n "$LOG_TABLES" ]; then
    echo "$LOG_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/missing-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/restructure/missing-tables-analysis.md

# 3. 系统配置表
echo "### 3. 系统配置表（System Config Tables）" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "**重要性**: 🟡 中 - 系统配置功能" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md

CONFIG_TABLES=$(grep -E "^system_" /tmp/missing_tables.txt | grep -vE "template|log|demo" 2>/dev/null || echo "")
if [ -n "$CONFIG_TABLES" ]; then
    echo "$CONFIG_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/missing-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/restructure/missing-tables-analysis.md

# 4. 基础设施表
echo "### 4. 基础设施表（Infrastructure Tables）" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "**重要性**: 🟡 中 - 基础功能支持" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md

INFRA_TABLES=$(grep -E "^infra_" /tmp/missing_tables.txt | grep -vE "file" 2>/dev/null || echo "")
if [ -n "$INFRA_TABLES" ]; then
    echo "$INFRA_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/missing-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/restructure/missing-tables-analysis.md

# 5. 业务扩展表
echo "### 5. 业务扩展表（Business Extension Tables）" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "**重要性**: 🟡 中 - 业务功能扩展" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md

BUSINESS_TABLES=$(grep -E "^member_|^pay_|^author_|^match_|^gold_" /tmp/missing_tables.txt 2>/dev/null || echo "")
if [ -n "$BUSINESS_TABLES" ]; then
    echo "$BUSINESS_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/missing-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/restructure/missing-tables-analysis.md

# 6. Demo和测试表
echo "### 6. Demo和测试表（Demo/Test Tables）" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "**重要性**: ❌ 低 - 可以删除" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md

DEMO_TABLES=$(grep -iE "demo|test|yudao" /tmp/missing_tables.txt 2>/dev/null || echo "")
if [ -n "$DEMO_TABLES" ]; then
    echo "$DEMO_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO ❌ 建议删除" >> docs/database/restructure/missing-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/restructure/missing-tables-analysis.md

# 7. 其他表
echo "### 7. 其他表（Other Tables）" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "**重要性**: 🟢 待确认" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md

OTHER_TABLES=$(grep -vE "template|log|logs|^system_|^infra_|^member_|^pay_|^author_|^match_|^gold_|^mp_|demo|test|yudao" /tmp/missing_tables.txt 2>/dev/null || echo "")
if [ -n "$OTHER_TABLES" ]; then
    echo "$OTHER_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/missing-tables-analysis.md
        fi
    done
fi

# 统计信息
TOTAL_MISSING=$(wc -l < /tmp/missing_tables.txt 2>/dev/null || echo "0")
TOTAL_TABLES=$(wc -l < /tmp/all_tables.txt)
INCLUDED_COUNT=${#INCLUDED_TABLES[@]}

echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "## 📊 统计信息" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "- **数据库总表数**: $TOTAL_TABLES" >> docs/database/restructure/missing-tables-analysis.md
echo "- **已包含表数**: $INCLUDED_COUNT" >> docs/database/restructure/missing-tables-analysis.md
echo "- **遗漏表数**: $TOTAL_MISSING" >> docs/database/restructure/missing-tables-analysis.md
echo "- **覆盖率**: $(( INCLUDED_COUNT * 100 / TOTAL_TABLES ))%" >> docs/database/restructure/missing-tables-analysis.md

echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "## 🎯 补充建议" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "### 高优先级补充" >> docs/database/restructure/missing-tables-analysis.md
echo "1. **模板表**: system_mail_template, system_notify_template, system_sms_template" >> docs/database/restructure/missing-tables-analysis.md
echo "2. **配置表**: system_mail_account, system_sms_channel, infra_config" >> docs/database/restructure/missing-tables-analysis.md
echo "3. **OAuth2表**: system_oauth2_* 系列（如果使用第三方登录）" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "### 中优先级补充" >> docs/database/restructure/missing-tables-analysis.md
echo "1. **日志表**: 根据监控需求选择性添加" >> docs/database/restructure/missing-tables-analysis.md
echo "2. **业务扩展表**: 根据具体业务需求添加" >> docs/database/restructure/missing-tables-analysis.md
echo "" >> docs/database/restructure/missing-tables-analysis.md
echo "### 可删除的表" >> docs/database/restructure/missing-tables-analysis.md
echo "1. **Demo表**: yudao_demo*, pay_demo_*" >> docs/database/restructure/missing-tables-analysis.md
echo "2. **测试表**: 包含test关键词的表" >> docs/database/restructure/missing-tables-analysis.md

log_success "遗漏表分析完成: docs/database/restructure/missing-tables-analysis.md"

# 清理临时文件
rm -f /tmp/all_tables.txt /tmp/missing_tables.txt

echo
echo "========================================"
echo "遗漏表分析完成！"
echo "========================================"
echo "📋 docs/database/restructure/missing-tables-analysis.md - 遗漏表分析报告"
echo "📊 遗漏表数: $TOTAL_MISSING"
echo "📊 覆盖率: $(( INCLUDED_COUNT * 100 / TOTAL_TABLES ))%"
echo "========================================"
