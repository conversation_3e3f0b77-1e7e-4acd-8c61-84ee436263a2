#!/bin/bash

# =============================================
# 简化表结构生成器
# 只生成表结构详情，避免fork错误
# =============================================

echo "========================================"
echo "简化表结构生成器"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p simple-analysis

echo "[INFO] 分析数据库: $DB_NAME"

# 获取所有表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

TOTAL_TABLES=$(wc -l < /tmp/all_tables.txt)
echo "[INFO] 发现 $TOTAL_TABLES 个表"

# 生成表结构详情
echo "[INFO] 生成表结构详情..."

cat > simple-analysis/table-structure-simple.md << EOF
# 数据库表结构详情

## 📊 数据库: $DB_NAME

**生成时间**: $(date)
**总表数**: $TOTAL_TABLES

EOF

# 只处理前10个表，避免fork错误
head -10 /tmp/all_tables.txt | while read table_name; do
    if [ -n "$table_name" ]; then
        echo "处理表: $table_name"
        
        echo "### 表: $table_name" >> simple-analysis/table-structure-simple.md
        echo "" >> simple-analysis/table-structure-simple.md
        
        # 获取表注释
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        [ -z "$TABLE_COMMENT" ] && TABLE_COMMENT="无注释"
        echo "**表说明**: $TABLE_COMMENT" >> simple-analysis/table-structure-simple.md
        echo "" >> simple-analysis/table-structure-simple.md
        
        # 生成字段信息表格
        echo "| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |" >> simple-analysis/table-structure-simple.md
        echo "|--------|------|----------|-----|--------|------|" >> simple-analysis/table-structure-simple.md
        
        # 获取字段详情
        temp_file="/tmp/columns_${table_name}_simple.txt"
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT 
                COLUMN_NAME,
                COLUMN_TYPE,
                IS_NULLABLE,
                IFNULL(COLUMN_KEY, '-'),
                IFNULL(COLUMN_DEFAULT, '-'),
                IFNULL(COLUMN_COMMENT, '-')
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' 
            ORDER BY ORDINAL_POSITION;" \
            --batch --skip-column-names 2>/dev/null > "$temp_file"
        
        if [ -s "$temp_file" ]; then
            while IFS=$'\t' read -r col_name col_type is_null col_key col_default col_comment; do
                # 跳过空行
                [ -z "$col_name" ] && continue
                
                # 处理空值
                [ -z "$col_type" ] && col_type="-"
                [ -z "$is_null" ] && is_null="-"
                [ -z "$col_key" ] && col_key=""
                [ -z "$col_default" ] && col_default=""
                [ -z "$col_comment" ] && col_comment=""
                
                # 处理特殊字符
                col_default=$(echo "$col_default" | sed 's/|/\\|/g' | sed 's/NULL//' | sed 's/-//')
                col_comment=$(echo "$col_comment" | sed 's/|/\\|/g' | sed 's/NULL//' | sed 's/-//')
                
                # 格式化键类型
                case "$col_key" in
                    "PRI") col_key="🔑 PK" ;;
                    "UNI") col_key="🔒 UK" ;;
                    "MUL") col_key="📇 FK" ;;
                    *) col_key="" ;;
                esac
                
                # 格式化是否为空
                case "$is_null" in
                    "NO") is_null="❌" ;;
                    "YES") is_null="✅" ;;
                    *) is_null="?" ;;
                esac
                
                echo "| $col_name | $col_type | $is_null | $col_key | $col_default | $col_comment |" >> simple-analysis/table-structure-simple.md
            done < "$temp_file"
        else
            echo "| - | 无法获取字段信息 | - | - | - | 请检查数据库权限 |" >> simple-analysis/table-structure-simple.md
        fi
        
        rm -f "$temp_file"
        
        echo "" >> simple-analysis/table-structure-simple.md
        echo "---" >> simple-analysis/table-structure-simple.md
        echo "" >> simple-analysis/table-structure-simple.md
    fi
done

# 清理临时文件
rm -f /tmp/all_tables.txt

echo
echo "========================================"
echo "简化表结构生成完成！"
echo "========================================"
echo "📊 表结构详情: simple-analysis/table-structure-simple.md"
echo "📊 处理表数: 前10个表"
echo "========================================"
