-- =============================================
-- 足球彩票系统 - 基础数据
-- 系统初始化数据和配置
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 默认租户数据
-- =============================================

-- 插入默认租户
INSERT IGNORE INTO `system_tenant` (`id`, `name`, `contact_name`, `contact_mobile`, `status`, `package_id`, `expire_time`, `account_count`, `creator`, `create_time`) VALUES
(1, '足球彩票平台', '系统管理员', '***********', 0, 1, '2099-12-31 23:59:59', 9999, '1', NOW());

-- =============================================
-- 系统菜单数据
-- =============================================

-- 插入系统菜单
INSERT IGNORE INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`) VALUES
(1, '系统管理', '', 1, 10, 0, '/system', 'ep:tools', NULL, 0),
(2, '用户管理', 'system:user:list', 2, 1, 1, 'user', 'ep:avatar', 'system/user/index', 0),
(3, '角色管理', 'system:role:list', 2, 2, 1, 'role', 'ep:user', 'system/role/index', 0),
(4, '菜单管理', 'system:menu:list', 2, 3, 1, 'menu', 'ep:menu', 'system/menu/index', 0),
(5, '租户管理', 'system:tenant:list', 2, 4, 1, 'tenant', 'ep:house', 'system/tenant/index', 0),

(100, '会员管理', '', 1, 20, 0, '/member', 'ep:user-filled', NULL, 0),
(101, '会员用户', 'member:user:list', 2, 1, 100, 'user', 'ep:avatar', 'member/user/index', 0),
(102, '会员等级', 'member:level:list', 2, 2, 100, 'level', 'ep:trophy', 'member/level/index', 0),

(200, '支付管理', '', 1, 30, 0, '/pay', 'ep:money', NULL, 0),
(201, '支付应用', 'pay:app:list', 2, 1, 200, 'app', 'ep:postcard', 'pay/app/index', 0),
(202, '支付订单', 'pay:order:list', 2, 2, 200, 'order', 'ep:list', 'pay/order/index', 0),
(203, '用户钱包', 'pay:wallet:list', 2, 3, 200, 'wallet', 'ep:wallet', 'pay/wallet/index', 0),

(300, '内容管理', '', 1, 40, 0, '/content', 'ep:document', NULL, 0),
(301, '文章管理', 'content:article:list', 2, 1, 300, 'article', 'ep:document', 'content/article/index', 0),
(302, '轮播图', 'content:banner:list', 2, 2, 300, 'banner', 'ep:picture', 'content/banner/index', 0),

(400, '微信管理', '', 1, 50, 0, '/mp', 'ep:chat-dot-round', NULL, 0),
(401, '微信账号', 'mp:account:list', 2, 1, 400, 'account', 'ep:postcard', 'mp/account/index', 0),
(402, '微信用户', 'mp:user:list', 2, 2, 400, 'user', 'ep:avatar', 'mp/user/index', 0),
(403, '微信菜单', 'mp:menu:list', 2, 3, 400, 'menu', 'ep:menu', 'mp/menu/index', 0),
(404, '微信消息', 'mp:message:list', 2, 4, 400, 'message', 'ep:chat-line-round', 'mp/message/index', 0),
(405, '模板配置', 'mp:template:list', 2, 5, 400, 'template', 'ep:document-copy', 'mp/template/index', 0);

-- =============================================
-- 系统角色数据
-- =============================================

-- 插入系统角色
INSERT IGNORE INTO `system_role` (`id`, `tenant_id`, `name`, `code`, `sort`, `data_scope`, `status`, `type`, `remark`, `creator`, `create_time`) VALUES
(1, 1, '超级管理员', 'super_admin', 1, 1, 0, 1, '超级管理员', '1', NOW()),
(2, 1, '普通角色', 'common', 2, 2, 0, 1, '普通角色', '1', NOW());

-- =============================================
-- 系统用户数据
-- =============================================

-- 插入系统用户 (密码: admin123)
INSERT IGNORE INTO `system_users` (`id`, `tenant_id`, `username`, `password`, `nickname`, `remark`, `email`, `mobile`, `sex`, `status`, `creator`, `create_time`) VALUES
(1, 1, 'admin', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH.ulu33dHOiBE/Q/V9DdqoCm2', '系统管理员', '管理员', '<EMAIL>', '***********', 1, 0, 'admin', NOW());

-- 用户角色关联
INSERT IGNORE INTO `system_user_role` (`user_id`, `role_id`, `tenant_id`, `creator`, `create_time`) VALUES
(1, 1, 1, '1', NOW());

-- 角色菜单关联
INSERT IGNORE INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `creator`, `create_time`) VALUES
(1, 1, 1, '1', NOW()),
(1, 2, 1, '1', NOW()),
(1, 3, 1, '1', NOW()),
(1, 4, 1, '1', NOW()),
(1, 5, 1, '1', NOW()),
(1, 100, 1, '1', NOW()),
(1, 101, 1, '1', NOW()),
(1, 102, 1, '1', NOW()),
(1, 200, 1, '1', NOW()),
(1, 201, 1, '1', NOW()),
(1, 202, 1, '1', NOW()),
(1, 203, 1, '1', NOW()),
(1, 300, 1, '1', NOW()),
(1, 301, 1, '1', NOW()),
(1, 302, 1, '1', NOW()),
(1, 400, 1, '1', NOW()),
(1, 401, 1, '1', NOW()),
(1, 402, 1, '1', NOW()),
(1, 403, 1, '1', NOW()),
(1, 404, 1, '1', NOW()),
(1, 405, 1, '1', NOW());

-- =============================================
-- 字典数据
-- =============================================

-- 字典类型
INSERT IGNORE INTO `system_dict_type` (`id`, `name`, `type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '用户性别', 'system_user_sex', 0, '用户性别列表', 'admin', NOW()),
(2, '系统状态', 'common_status', 0, '登录状态列表', 'admin', NOW()),
(3, '会员状态', 'member_status', 0, '会员状态列表', 'admin', NOW()),
(4, '支付状态', 'pay_order_status', 0, '支付订单状态', 'admin', NOW());

-- 字典数据
INSERT IGNORE INTO `system_dict_data` (`id`, `sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, 1, '男', '1', 'system_user_sex', 0, '性别男', 'admin', NOW()),
(2, 2, '女', '2', 'system_user_sex', 0, '性别女', 'admin', NOW()),
(3, 1, '正常', '0', 'common_status', 0, '正常状态', 'admin', NOW()),
(4, 2, '停用', '1', 'common_status', 0, '停用状态', 'admin', NOW()),
(5, 1, '正常', '0', 'member_status', 0, '正常状态', 'admin', NOW()),
(6, 2, '禁用', '1', 'member_status', 0, '禁用状态', 'admin', NOW()),
(7, 1, '待支付', '0', 'pay_order_status', 0, '待支付', 'admin', NOW()),
(8, 2, '已支付', '10', 'pay_order_status', 0, '已支付', 'admin', NOW()),
(9, 3, '已关闭', '20', 'pay_order_status', 0, '已关闭', 'admin', NOW());

-- =============================================
-- 默认会员等级
-- =============================================

INSERT IGNORE INTO `member_level` (`id`, `tenant_id`, `name`, `experience`, `level`, `discount_percent`, `status`, `creator`, `create_time`) VALUES
(1, 1, '青铜会员', 0, 1, 100, 0, 'admin', NOW()),
(2, 1, '白银会员', 1000, 2, 95, 0, 'admin', NOW()),
(3, 1, '黄金会员', 3000, 3, 90, 0, 'admin', NOW()),
(4, 1, '钻石会员', 10000, 4, 85, 0, 'admin', NOW());

-- =============================================
-- 默认支付应用
-- =============================================

INSERT IGNORE INTO `pay_app` (`id`, `tenant_id`, `name`, `status`, `remark`, `pay_notify_url`, `refund_notify_url`, `merchant_name`, `merchant_short_name`, `creator`, `create_time`) VALUES
(1, 1, '足球彩票支付', 0, '默认支付应用', 'http://localhost:8080/api/pay/notify', 'http://localhost:8080/api/pay/refund-notify', '足球彩票平台', '足彩平台', 'admin', NOW());

SET FOREIGN_KEY_CHECKS = 1;
