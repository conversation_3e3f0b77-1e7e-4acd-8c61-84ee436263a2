-- =============================================
-- 足球彩票系统多租户改造 - 数据迁移
-- 目的：为现有数据设置租户ID
-- 注意事项：执行前请确保已备份数据库
-- =============================================

-- 设置SQL模式，确保安全执行
SET SQL_SAFE_UPDATES = 0;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 创建默认租户
-- =============================================

-- 插入默认租户（如果不存在）
INSERT IGNORE INTO system_tenant (
    id, name, contact_name, contact_mobile, status, 
    website, package_id, expire_time, account_count,
    creator, create_time, updater, update_time, deleted
) VALUES (
    1, '默认租户', '系统管理员', '***********', 0,
    'localhost', 0, '2099-12-31 23:59:59', 100,
    'system', NOW(), 'system', NOW(), 0
);

-- 确保默认租户套餐存在
INSERT IGNORE INTO system_tenant_package (
    id, name, status, remark, menu_ids,
    creator, create_time, updater, update_time, deleted
) VALUES (
    0, '系统套餐', 0, '系统内置套餐，拥有所有权限', '[]',
    'system', NOW(), 'system', NOW(), 0
);

-- =============================================
-- 2. 会员模块数据迁移
-- =============================================

-- 更新会员用户表
UPDATE member_user 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新会员等级表
UPDATE member_level 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新会员等级记录表
UPDATE member_level_record 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新会员积分记录表
UPDATE member_point_record 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新会员签到记录表
UPDATE member_sign_in_record 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新会员分组表（如果存在）
UPDATE member_group 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- =============================================
-- 3. 支付模块数据迁移
-- =============================================

-- 更新支付应用表
UPDATE pay_app 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新支付订单表
UPDATE pay_order 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新支付退款表
UPDATE pay_refund 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新用户钱包表
UPDATE pay_wallet 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新钱包交易记录表
UPDATE pay_wallet_transaction 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新支付转账表
UPDATE pay_transfer 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- =============================================
-- 4. 业务模块数据迁移
-- =============================================

-- 更新文章表
UPDATE article 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新文章追加表
UPDATE author_article_append 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新球队信息表
UPDATE match_team 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新轮播图表（如果存在）
UPDATE banner 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新鱼币充值订单表（如果存在）
UPDATE gold_order 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- =============================================
-- 5. 基础设施模块数据迁移
-- =============================================

-- 更新文件表
UPDATE infra_file 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新文件配置表
UPDATE infra_file_config 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新代码生成表
UPDATE infra_codegen_table 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新代码生成字段表
UPDATE infra_codegen_column 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- =============================================
-- 6. 微信公众号模块数据迁移
-- =============================================

-- 更新微信账号表
UPDATE mp_account 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新微信消息表
UPDATE mp_message 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新微信用户表
UPDATE mp_user 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新微信标签表
UPDATE mp_tag 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新微信菜单表
UPDATE mp_menu 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- 更新微信自动回复表
UPDATE mp_auto_reply 
SET tenant_id = 1 
WHERE tenant_id = 0 OR tenant_id IS NULL;

-- =============================================
-- 7. 数据完整性验证
-- =============================================

-- 检查是否还有未设置租户ID的数据
SELECT 'member_user' as table_name, COUNT(*) as zero_tenant_count 
FROM member_user WHERE tenant_id = 0
UNION ALL
SELECT 'member_level' as table_name, COUNT(*) as zero_tenant_count 
FROM member_level WHERE tenant_id = 0
UNION ALL
SELECT 'pay_app' as table_name, COUNT(*) as zero_tenant_count 
FROM pay_app WHERE tenant_id = 0
UNION ALL
SELECT 'pay_order' as table_name, COUNT(*) as zero_tenant_count 
FROM pay_order WHERE tenant_id = 0
UNION ALL
SELECT 'pay_wallet' as table_name, COUNT(*) as zero_tenant_count 
FROM pay_wallet WHERE tenant_id = 0
UNION ALL
SELECT 'article' as table_name, COUNT(*) as zero_tenant_count 
FROM article WHERE tenant_id = 0;

-- 统计各表的租户数据分布
SELECT 'member_user' as table_name, tenant_id, COUNT(*) as record_count 
FROM member_user GROUP BY tenant_id
UNION ALL
SELECT 'pay_order' as table_name, tenant_id, COUNT(*) as record_count 
FROM pay_order GROUP BY tenant_id
UNION ALL
SELECT 'article' as table_name, tenant_id, COUNT(*) as record_count 
FROM article GROUP BY tenant_id
ORDER BY table_name, tenant_id;

-- =============================================
-- 8. 设置租户ID字段约束
-- =============================================

-- 为关键表的tenant_id字段添加非空约束（如果需要）
-- 注意：只有在确认所有数据都已正确设置tenant_id后才执行

-- ALTER TABLE member_user MODIFY COLUMN tenant_id BIGINT NOT NULL;
-- ALTER TABLE pay_order MODIFY COLUMN tenant_id BIGINT NOT NULL;
-- ALTER TABLE article MODIFY COLUMN tenant_id BIGINT NOT NULL;

-- 恢复SQL模式
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- =============================================
-- 执行完成提示
-- =============================================
SELECT 'tenant_id数据迁移完成，请检查验证结果' AS message;
