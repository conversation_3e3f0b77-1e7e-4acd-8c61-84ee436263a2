# 足球彩票系统前端开发文档

## 1. 项目概述

### 1.1 项目简介
足球彩票系统前端管理后台是基于现代化前端技术栈开发的企业级管理系统，提供完整的系统管理、会员管理、支付管理等功能模块。

### 1.2 技术特点
- **多版本支持**：提供Vue2、Vue3多个技术栈版本
- **组件化开发**：高度组件化，易于维护和扩展
- **响应式设计**：支持多种屏幕尺寸，兼容移动端
- **权限管理**：细粒度的权限控制和动态路由
- **国际化支持**：多语言切换功能
- **主题定制**：支持多主题切换和自定义主题

## 2. 技术栈

### 2.1 前端技术栈对比
| 版本 | 框架 | UI组件库 | 特点 | 推荐场景 |
|------|------|----------|------|----------|
| Vue3 + Element Plus | Vue 3.x | Element Plus | 现代化、性能优秀 | 新项目首选 |
| Vue3 + Ant Design | Vue 3.x | Ant Design Vue | 企业级、功能丰富 | 大型企业项目 |
| Vue2 + Element UI | Vue 2.x | Element UI | 稳定、兼容性好 | 维护项目 |
| uni-app | Vue 2.x | uni-ui | 跨平台、移动优先 | 移动端管理 |

### 2.2 核心依赖
| 技术 | 版本 | 说明 |
|------|------|------|
| Vue | 3.x/2.x | 渐进式JavaScript框架 |
| Vue Router | 4.x/3.x | 官方路由管理器 |
| Vuex/Pinia | 4.x/2.x | 状态管理 |
| Axios | 1.x | HTTP客户端 |
| Element Plus/UI | 2.x/2.x | UI组件库 |
| TypeScript | 4.x | 类型安全 |
| Vite/Webpack | 4.x/5.x | 构建工具 |

### 2.3 开发工具
| 工具 | 说明 |
|------|------|
| VS Code | 推荐IDE |
| Vue DevTools | Vue调试工具 |
| ESLint | 代码规范检查 |
| Prettier | 代码格式化 |
| Sass/Less | CSS预处理器 |

## 3. 项目结构

### 3.1 目录结构
```
mir-ui/
├── mir-ui-admin-vue3/          # Vue3 + Element Plus版本
├── mir-ui-admin-vben/          # Vue3 + Ant Design版本  
├── mir-ui-admin-vue2/          # Vue2 + Element UI版本
├── mir-ui-admin-uniapp/        # uni-app移动端版本
└── mir-ui-mall-uniapp/         # 商城uni-app版本
```

### 3.2 单个项目结构（以Vue3版本为例）
```
mir-ui-admin-vue3/
├── public/                     # 静态资源
│   ├── index.html             # HTML模板
│   └── favicon.ico            # 网站图标
├── src/                       # 源代码
│   ├── api/                   # API接口
│   │   ├── system/           # 系统管理接口
│   │   ├── member/           # 会员管理接口
│   │   ├── pay/              # 支付管理接口
│   │   └── common/           # 公共接口
│   ├── assets/               # 静态资源
│   │   ├── images/           # 图片资源
│   │   ├── icons/            # 图标资源
│   │   └── styles/           # 样式文件
│   ├── components/           # 公共组件
│   │   ├── Layout/           # 布局组件
│   │   ├── Form/             # 表单组件
│   │   ├── Table/            # 表格组件
│   │   └── Upload/           # 上传组件
│   ├── hooks/                # 组合式API
│   ├── router/               # 路由配置
│   │   ├── index.js          # 路由主文件
│   │   └── modules/          # 路由模块
│   ├── store/                # 状态管理
│   │   ├── index.js          # Store主文件
│   │   └── modules/          # Store模块
│   ├── utils/                # 工具函数
│   │   ├── request.js        # HTTP请求封装
│   │   ├── auth.js           # 认证工具
│   │   └── common.js         # 通用工具
│   ├── views/                # 页面组件
│   │   ├── login/            # 登录页面
│   │   ├── dashboard/        # 仪表盘
│   │   ├── system/           # 系统管理
│   │   ├── member/           # 会员管理
│   │   ├── pay/              # 支付管理
│   │   └── infra/            # 基础设施
│   ├── App.vue               # 根组件
│   └── main.js               # 入口文件
├── .env                      # 环境变量
├── .env.development          # 开发环境变量
├── .env.production           # 生产环境变量
├── package.json              # 项目配置
├── vite.config.js            # Vite配置
└── README.md                 # 项目说明
```

## 4. 核心功能模块

### 4.1 系统管理模块
**路径**: `/src/views/system/`

- **用户管理** (`/system/user`)
  - 用户列表、新增、编辑、删除
  - 用户状态管理、密码重置
  - 用户角色分配

- **角色管理** (`/system/role`)
  - 角色列表、新增、编辑、删除
  - 权限分配、数据权限设置

- **菜单管理** (`/system/menu`)
  - 菜单树形结构管理
  - 动态路由配置

- **部门管理** (`/system/dept`)
  - 部门树形结构
  - 组织架构管理

- **字典管理** (`/system/dict`)
  - 字典类型和数据管理
  - 系统配置项维护

### 4.2 会员管理模块
**路径**: `/src/views/member/`

- **会员用户** (`/member/user`)
  - 会员列表查询、详情查看
  - 会员状态管理、余额操作

- **会员等级** (`/member/level`)
  - 等级体系配置
  - 等级权益设置

- **积分管理** (`/member/point`)
  - 积分记录查询
  - 积分规则配置

- **签到管理** (`/member/sign`)
  - 签到记录查询
  - 签到奖励配置

### 4.3 支付管理模块
**路径**: `/src/views/pay/`

- **支付应用** (`/pay/app`)
  - 支付渠道配置
  - 支付参数设置

- **支付订单** (`/pay/order`)
  - 订单列表查询
  - 订单状态跟踪

- **退款管理** (`/pay/refund`)
  - 退款订单处理
  - 退款流程管理

- **钱包管理** (`/pay/wallet`)
  - 用户钱包查询
  - 交易记录管理

### 4.4 基础设施模块
**路径**: `/src/views/infra/`

- **文件管理** (`/infra/file`)
  - 文件上传下载
  - 文件存储管理

- **配置管理** (`/infra/config`)
  - 系统参数配置
  - 配置项管理

- **定时任务** (`/infra/job`)
  - 任务调度管理
  - 任务执行监控

- **代码生成** (`/infra/codegen`)
  - 代码自动生成
  - 模板管理

## 5. 开发规范

### 5.1 命名规范
- **文件命名**: 使用kebab-case（短横线分隔）
- **组件命名**: 使用PascalCase（大驼峰）
- **变量命名**: 使用camelCase（小驼峰）
- **常量命名**: 使用UPPER_SNAKE_CASE（大写下划线）

### 5.2 目录规范
```javascript
// 页面组件
views/
├── system/
│   ├── user/
│   │   ├── index.vue          # 列表页面
│   │   ├── form.vue           # 表单页面
│   │   └── detail.vue         # 详情页面
│   └── role/
│       └── index.vue

// API接口
api/
├── system/
│   ├── user.js                # 用户相关接口
│   └── role.js                # 角色相关接口
```

### 5.3 组件规范
```vue
<template>
  <div class="page-container">
    <!-- 页面内容 -->
  </div>
</template>

<script>
export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  methods: {},
  created() {},
  mounted() {}
}
</script>

<style lang="scss" scoped>
.page-container {
  // 样式定义
}
</style>
```

### 5.4 API调用规范
```javascript
// api/system/user.js
import request from '@/utils/request'

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/admin-api/system/user/page',
    method: 'get',
    params
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/admin-api/system/user',
    method: 'post',
    data
  })
}
```

## 6. 状态管理

### 6.1 Vuex/Pinia结构
```javascript
// store/modules/user.js
export default {
  namespaced: true,
  state: {
    userInfo: null,
    permissions: [],
    roles: []
  },
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
    }
  },
  actions: {
    async getUserInfo({ commit }) {
      const response = await getUserInfo()
      commit('SET_USER_INFO', response.data)
      return response.data
    }
  }
}
```

### 6.2 状态管理最佳实践
- 只在Vuex中存储需要跨组件共享的状态
- 使用模块化管理，按功能划分模块
- 异步操作统一在actions中处理
- 使用getters计算衍生状态

## 7. 路由配置

### 7.1 路由结构
```javascript
// router/index.js
const routes = [
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        name: 'Dashboard',
        meta: { title: '仪表盘', icon: 'dashboard' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    name: 'System',
    meta: { title: '系统管理', icon: 'system' },
    children: [
      {
        path: 'user',
        component: () => import('@/views/system/user/index.vue'),
        name: 'User',
        meta: { title: '用户管理', icon: 'user' }
      }
    ]
  }
]
```

### 7.2 动态路由
```javascript
// 根据用户权限动态生成路由
export function generateRoutes(permissions) {
  const accessedRoutes = filterAsyncRoutes(asyncRoutes, permissions)
  return accessedRoutes
}

function filterAsyncRoutes(routes, permissions) {
  const res = []
  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(permissions, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, permissions)
      }
      res.push(tmp)
    }
  })
  return res
}
```

## 8. 组件开发

### 8.1 公共组件
```vue
<!-- components/Table/index.vue -->
<template>
  <el-table
    :data="data"
    :loading="loading"
    @selection-change="handleSelectionChange"
  >
    <el-table-column
      v-for="column in columns"
      :key="column.prop"
      v-bind="column"
    />
  </el-table>
</template>

<script>
export default {
  name: 'BaseTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    }
  }
}
</script>
```

### 8.2 表单组件
```vue
<!-- components/Form/index.vue -->
<template>
  <el-form
    ref="form"
    :model="form"
    :rules="rules"
    label-width="120px"
  >
    <el-form-item
      v-for="item in formItems"
      :key="item.prop"
      :label="item.label"
      :prop="item.prop"
    >
      <component
        :is="item.component"
        v-model="form[item.prop]"
        v-bind="item.attrs"
      />
    </el-form-item>
  </el-form>
</template>
```

## 9. 样式规范

### 9.1 SCSS变量
```scss
// assets/styles/variables.scss
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

$font-size-base: 14px;
$font-size-small: 12px;
$font-size-large: 16px;

$border-radius-base: 4px;
$border-color-base: #DCDFE6;
```

### 9.2 样式组织
```scss
// 页面样式
.user-management {
  .search-form {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 0;
    }
  }
  
  .table-container {
    .operation-buttons {
      margin-bottom: 15px;
    }
  }
}
```

## 10. 构建配置

### 10.1 环境变量
```bash
# .env.development
NODE_ENV = 'development'
VUE_APP_TITLE = '足球彩票管理系统'
VUE_APP_BASE_API = '/dev-api'
VUE_APP_TENANT_ENABLE = true
VUE_APP_CAPTCHA_ENABLE = true

# .env.production
NODE_ENV = 'production'
VUE_APP_TITLE = '足球彩票管理系统'
VUE_APP_BASE_API = '/prod-api'
VUE_APP_TENANT_ENABLE = true
VUE_APP_CAPTCHA_ENABLE = true
```

### 10.2 Vite配置
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/dev-api': {
        target: 'http://localhost:48080',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/dev-api/, '/admin-api')
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'static',
    rollupOptions: {
      output: {
        chunkFileNames: 'static/js/[name]-[hash].js',
        entryFileNames: 'static/js/[name]-[hash].js',
        assetFileNames: 'static/[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
```

## 11. 开发调试

### 11.1 本地开发环境搭建
```bash
# 1. 克隆项目
git clone <repository-url>
cd mir-ui-admin-vue3

# 2. 安装依赖
npm install
# 或使用yarn
yarn install

# 3. 启动开发服务器
npm run dev
# 或
yarn dev

# 4. 访问应用
# 浏览器打开 http://localhost:3000
```

### 11.2 开发工具配置
```json
// .vscode/settings.json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "emmet.includeLanguages": {
    "vue": "html"
  }
}
```

### 11.3 调试技巧
- 使用Vue DevTools调试组件状态
- 使用浏览器Network面板调试API请求
- 使用console.log和debugger进行代码调试
- 使用Vue的错误边界处理组件错误

## 12. 性能优化

### 12.1 代码分割
```javascript
// 路由懒加载
const routes = [
  {
    path: '/system/user',
    component: () => import(
      /* webpackChunkName: "system" */
      '@/views/system/user/index.vue'
    )
  }
]

// 组件懒加载
export default {
  components: {
    UserForm: () => import('./components/UserForm.vue')
  }
}
```

### 12.2 图片优化
```vue
<template>
  <!-- 使用webp格式 -->
  <img
    :src="imageUrl"
    loading="lazy"
    alt="描述"
  />

  <!-- 响应式图片 -->
  <picture>
    <source srcset="image.webp" type="image/webp">
    <source srcset="image.jpg" type="image/jpeg">
    <img src="image.jpg" alt="描述">
  </picture>
</template>
```

### 12.3 缓存策略
```javascript
// HTTP请求缓存
const cache = new Map()

export function getCachedData(key, fetcher, ttl = 5 * 60 * 1000) {
  const cached = cache.get(key)
  if (cached && Date.now() - cached.timestamp < ttl) {
    return Promise.resolve(cached.data)
  }

  return fetcher().then(data => {
    cache.set(key, {
      data,
      timestamp: Date.now()
    })
    return data
  })
}
```

## 13. 测试策略

### 13.1 单元测试
```javascript
// tests/unit/components/UserForm.spec.js
import { mount } from '@vue/test-utils'
import UserForm from '@/components/UserForm.vue'

describe('UserForm.vue', () => {
  it('renders props.title when passed', () => {
    const title = '新增用户'
    const wrapper = mount(UserForm, {
      props: { title }
    })
    expect(wrapper.text()).toMatch(title)
  })

  it('emits submit event when form is submitted', async () => {
    const wrapper = mount(UserForm)
    await wrapper.find('form').trigger('submit')
    expect(wrapper.emitted()).toHaveProperty('submit')
  })
})
```

### 13.2 E2E测试
```javascript
// tests/e2e/user-management.spec.js
describe('用户管理', () => {
  it('应该能够创建新用户', () => {
    cy.visit('/system/user')
    cy.get('[data-cy=add-user-btn]').click()
    cy.get('[data-cy=username-input]').type('testuser')
    cy.get('[data-cy=submit-btn]').click()
    cy.contains('创建成功')
  })
})
```

## 14. 部署指南

### 14.1 构建生产版本
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 分析构建包大小
npm run build:analyze
```

### 14.2 Docker部署
```dockerfile
# Dockerfile
FROM node:16-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:stable-alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 14.3 Nginx配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /prod-api/ {
        proxy_pass http://backend-server:48080/admin-api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 15. 国际化

### 15.1 i18n配置
```javascript
// src/i18n/index.js
import { createI18n } from 'vue-i18n'
import zh from './locales/zh-CN'
import en from './locales/en-US'

const i18n = createI18n({
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages: {
    'zh-CN': zh,
    'en-US': en
  }
})

export default i18n
```

### 15.2 语言文件
```javascript
// src/i18n/locales/zh-CN.js
export default {
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '新增'
  },
  user: {
    username: '用户名',
    password: '密码',
    email: '邮箱',
    phone: '手机号'
  }
}
```

### 15.3 使用方式
```vue
<template>
  <div>
    <h1>{{ $t('user.management') }}</h1>
    <el-button>{{ $t('common.add') }}</el-button>
  </div>
</template>

<script>
export default {
  methods: {
    showMessage() {
      this.$message.success(this.$t('common.saveSuccess'))
    }
  }
}
</script>
```

## 16. 主题定制

### 16.1 Element Plus主题
```scss
// styles/element-variables.scss
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #409eff,
    ),
    'success': (
      'base': #67c23a,
    ),
    'warning': (
      'base': #e6a23c,
    ),
    'danger': (
      'base': #f56c6c,
    ),
  )
);
```

### 16.2 动态主题切换
```javascript
// utils/theme.js
export function changeTheme(theme) {
  const root = document.documentElement

  const themes = {
    light: {
      '--bg-color': '#ffffff',
      '--text-color': '#303133',
      '--border-color': '#dcdfe6'
    },
    dark: {
      '--bg-color': '#1d1e1f',
      '--text-color': '#ffffff',
      '--border-color': '#4c4d4f'
    }
  }

  const themeVars = themes[theme]
  Object.keys(themeVars).forEach(key => {
    root.style.setProperty(key, themeVars[key])
  })
}
```

## 17. 错误处理

### 17.1 全局错误处理
```javascript
// main.js
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)

  // 发送错误到监控系统
  if (process.env.NODE_ENV === 'production') {
    sendErrorToMonitoring(err, info)
  }
}
```

### 17.2 HTTP错误处理
```javascript
// utils/request.js
import axios from 'axios'
import { ElMessage } from 'element-plus'

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 5000
})

service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 0) {
      ElMessage.error(res.message || '请求失败')
      return Promise.reject(new Error(res.message || '请求失败'))
    }
    return res
  },
  error => {
    const { response } = error
    if (response) {
      switch (response.status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          // 跳转到登录页
          break
        case 403:
          ElMessage.error('没有权限访问')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error('网络错误')
      }
    }
    return Promise.reject(error)
  }
)
```

## 18. 安全最佳实践

### 18.1 XSS防护
```javascript
// utils/security.js
export function escapeHtml(text) {
  const map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  }
  return text.replace(/[&<>"']/g, m => map[m])
}

// 在模板中使用
export default {
  methods: {
    safeHtml(html) {
      return this.escapeHtml(html)
    }
  }
}
```

### 18.2 CSRF防护
```javascript
// utils/request.js
service.interceptors.request.use(config => {
  // 添加CSRF token
  const token = document.querySelector('meta[name="csrf-token"]')?.content
  if (token) {
    config.headers['X-CSRF-TOKEN'] = token
  }
  return config
})
```

## 19. 常见问题

### 19.1 开发环境问题
**Q: 启动项目时报错 "Module not found"**
A: 检查依赖是否正确安装，尝试删除node_modules重新安装

**Q: 热更新不生效**
A: 检查Vite配置，确保文件路径正确

### 19.2 构建问题
**Q: 构建后页面空白**
A: 检查路由配置，确保使用history模式时服务器配置正确

**Q: 静态资源路径错误**
A: 检查publicPath配置，确保与部署路径一致

### 19.3 兼容性问题
**Q: IE浏览器不兼容**
A: 添加polyfill，使用babel转译ES6语法

---

**文档维护**: 本技术文档需要随着项目发展持续更新，建议每个版本发布时同步更新文档内容。

**技术支持**: 如有技术问题，请参考项目README或联系开发团队。
