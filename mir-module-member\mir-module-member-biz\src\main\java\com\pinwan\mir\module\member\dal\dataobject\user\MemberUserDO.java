package com.pinwan.mir.module.member.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.*;
import com.pinwan.mir.framework.common.enums.CommonStatusEnum;
import com.pinwan.mir.framework.common.enums.TerminalEnum;
import com.pinwan.mir.framework.ip.core.Area;
import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;
import com.pinwan.mir.framework.mybatis.core.type.LongListTypeHandler;
import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;
import com.pinwan.mir.module.member.dal.dataobject.group.MemberGroupDO;
import com.pinwan.mir.module.member.dal.dataobject.level.MemberLevelDO;
import com.pinwan.mir.module.system.enums.common.SexEnum;
import lombok.*;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 会员用户 DO
 * <p>
 * uk_mobile 索引：基于 {@link #mobile} 字段
 *
 * <AUTHOR>
 */
@TableName(value = "member_user", autoResultMap = true)
@KeySequence("member_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberUserDO extends TenantBaseDO {

    /**
     * 用户ID
     */
    @TableId
    private Long id;

    /**
     * 用户登录账号
     */
    private String username;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 微信公众号关联id
     */
    private String correlationId;
    /**
     * 手机
     */
    private String mobile;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 鱼币
     */
    private BigDecimal gold;

    /**
     * 余额
     */
    private BigDecimal balance;

    private String avatarUrl;
    /**
     * 充值总额
     */
    private BigDecimal rechargeNum;
    /**
     * 消费金额
     */
    private BigDecimal consumeAmount;
    /**
     * 帐号状态 枚举
     */
    private Integer status;
    /**
     * 注册 IP
     */
    private String registerIp;
    /**
     * 注册时间
     */
    private Date registerTime;
    /**
     * 登录IP
     */
    private String lastLoginIp;
    /**
     * 登录时间
     */
    private Date lastLoginTime;
    /**
     * 是否是作者 0 否 1 是
     */
    private Integer author;

    /**
     * 总收入
     */
    private BigDecimal totalIncome;

    /**
     * 粉丝数
     */
    private Integer fans;

    /**
     * 文章总数
     */
    private Integer articleNum;

    /**
     * 企微号主键
     */
    private Long wxWorkId;

    /**
     * 欢迎语
     */
    private String welcomeMsg;

    /**
     * QQ
     */
    private String qq;

    @TableField(exist = false)
    private String wxWorkName;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 作者专属公众号
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS) // 可以为空
    private Long captivePushAccount;

    @TableField(exist = false)
    private String captivePushAccountName;

    /**
     * 用户关联qq邮箱
     */
    private String qqMail;
}
