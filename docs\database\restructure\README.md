# 数据库重构文档

## 📁 文件结构

```
docs/database/restructure/
├── database-restructure-analysis.md    # 重构分析报告
├── modular-er-diagram.mmd              # 完整模块化ER图
├── core-business-er.mmd                # 核心业务ER图
├── restructure-summary.md              # 重构总结报告
└── README.md                           # 使用说明

sql/restructure/
├── 01-framework-tables.sql             # 平台框架表
├── 02-admin-frontend-tables.sql        # 后台前端表
├── 03-application-tables.sql           # 应用功能表
├── 04-basic-data.sql                   # 基础数据
└── install.sql                         # 主安装脚本
```

## 🚀 快速开始

### 1. 全新安装
```bash
# 创建新数据库并安装
cd sql/restructure
mysql -u root -p < install.sql
```

### 2. 查看ER图
```bash
# 在线查看ER图
# 访问 https://mermaid.live/
# 复制 modular-er-diagram.mmd 内容
```

### 3. 查看分析报告
```bash
cat docs/database/restructure/database-restructure-analysis.md
```

## 📊 默认账号

- **管理员账号**: admin
- **管理员密码**: admin123
- **默认租户**: 足球彩票平台

## 🔧 自定义配置

### 修改数据库名
编辑 `install.sql` 第一行：
```sql
CREATE DATABASE IF NOT EXISTS `your_database_name` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 修改默认数据
编辑 `04-basic-data.sql` 中的相关数据。

## ⚠️ 注意事项

1. 确保MySQL版本 >= 5.7
2. 确保字符集为 utf8mb4
3. 执行前请备份现有数据
4. 建议先在测试环境验证

## 🆘 问题排查

### 常见问题
1. **字符集问题**: 确保数据库和表都使用 utf8mb4
2. **权限问题**: 确保用户有创建数据库和表的权限
3. **外键约束**: 如果有外键错误，检查表的创建顺序

### 获取帮助
- 查看详细的分析报告
- 检查SQL脚本的注释
- 联系系统管理员
