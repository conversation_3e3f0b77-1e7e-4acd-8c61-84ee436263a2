#!/bin/bash

# =============================================
# 足球彩票系统SaaS多租户改造执行脚本
# 作者：足球彩票团队
# 版本：1.0.0
# 说明：自动化执行多租户改造的所有步骤
# =============================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具..."
    
    # 检查MySQL客户端
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装，请先安装MySQL客户端"
        exit 1
    fi
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装Java 8+"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi
    
    log_success "必要工具检查完成"
}

# 读取配置
read_config() {
    log_info "读取数据库配置..."
    
    # 从配置文件读取数据库连接信息
    if [ -f "config/database.conf" ]; then
        source config/database.conf
    else
        # 默认配置		
        DB_HOST=${DB_HOST:-localhost}
        DB_PORT=${DB_PORT:-3306}
        DB_NAME=${DB_NAME:-mir}
        DB_USER=${DB_USER:-root}
        
        # 提示输入密码
        read -s -p "请输入数据库密码: " DB_PASSWORD
        echo
    fi
    
	log_info "$DB_HOST | $DB_PORT | $DB_USER | $DB_NAME"
    # 验证数据库连接
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
        log_error "数据库连接失败，请检查配置"
        exit 1
    fi
    
    log_success "数据库连接验证成功"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    BACKUP_FILE="$BACKUP_DIR/database_backup.sql"
    
    mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        --single-transaction --routines --triggers "$DB_NAME" > "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "数据库备份完成: $BACKUP_FILE"
        echo "$BACKUP_FILE" > "$BACKUP_DIR/backup_info.txt"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 执行SQL脚本
execute_sql_script() {
    local script_file=$1
    local description=$2
    local allow_warnings=${3:-false}

    log_info "执行SQL脚本: $description"

    if [ ! -f "$script_file" ]; then
        log_error "SQL脚本文件不存在: $script_file"
        exit 1
    fi

    # 创建日志文件
    local log_file="logs/sql_$(basename "$script_file" .sql)_$(date +%Y%m%d_%H%M%S).log"

    # 执行SQL脚本并记录日志
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$script_file" 2>&1 | tee "$log_file"
    local exit_code=${PIPESTATUS[0]}

    if [ $exit_code -eq 0 ]; then
        log_success "$description 完成"
    else
        # 检查是否是可以忽略的警告
        if [ "$allow_warnings" = "true" ]; then
            local error_count=$(grep -c "ERROR" "$log_file" || echo "0")
            if [ "$error_count" -eq 0 ]; then
                log_warning "$description 完成（有警告，但可以忽略）"
                return 0
            fi
        fi

        log_error "$description 失败，详细日志: $log_file"

        # 显示最后几行错误信息
        echo "最后的错误信息："
        tail -10 "$log_file"

        # 询问是否继续
        echo
        read -p "是否忽略错误继续执行？(y/N): " -n 1 -r
        echo

        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_warning "用户选择忽略错误，继续执行"
            return 0
        else
            exit 1
        fi
    fi
}

# 检查当前状态
check_current_status() {
    log_info "检查当前改造状态..."

    # 执行状态检查脚本
    execute_sql_script "sql/tenant-migration/00-check-and-fix.sql" "检查当前状态" true

    log_success "状态检查完成"
}

# 数据库结构改造
migrate_database() {
    log_info "开始数据库结构改造..."

    # 0. 检查当前状态
    check_current_status

    # 1. 添加租户字段（支持重复执行）
    execute_sql_script "sql/tenant-migration/01-add-tenant-fields.sql" "添加租户字段" true

    # 2. 创建索引（支持重复执行）
    execute_sql_script "sql/tenant-migration/02-create-indexes.sql" "创建租户索引" true

    # 3. 数据迁移
    execute_sql_script "sql/tenant-migration/03-migrate-data.sql" "迁移现有数据"

    log_success "数据库结构改造完成"
}

# 代码改造
migrate_code() {
    log_info "开始代码改造..."
    
    # 检查项目根目录
    if [ ! -f "pom.xml" ]; then
        log_error "未找到pom.xml，请在项目根目录执行此脚本"
        exit 1
    fi
    
    # 1. 修改实体类继承关系
    log_info "修改实体类继承关系..."
    
    # 会员模块实体类改造
    find mir-module-member -name "*DO.java" -type f | while read file; do
        if grep -q "extends BaseDO" "$file"; then
            log_info "修改文件: $file"
            sed -i 's/extends BaseDO/extends TenantBaseDO/g' "$file"
            sed -i 's/import com.pinwan.mir.framework.mybatis.core.dataobject.BaseDO;/import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;/g' "$file"
        fi
    done
    
    # 支付模块实体类改造（部分）
    PAYMENT_ENTITIES=(
        "mir-module-pay/mir-module-pay-biz/src/main/java/com/pinwan/mir/module/pay/dal/dataobject/app/PayAppDO.java"
        "mir-module-pay/mir-module-pay-biz/src/main/java/com/pinwan/mir/module/pay/dal/dataobject/order/PayOrderDO.java"
        "mir-module-pay/mir-module-pay-biz/src/main/java/com/pinwan/mir/module/pay/dal/dataobject/refund/PayRefundDO.java"
        "mir-module-pay/mir-module-pay-biz/src/main/java/com/pinwan/mir/module/pay/dal/dataobject/wallet/PayWalletDO.java"
    )
    
    for file in "${PAYMENT_ENTITIES[@]}"; do
        if [ -f "$file" ] && grep -q "extends BaseDO" "$file"; then
            log_info "修改文件: $file"
            sed -i 's/extends BaseDO/extends TenantBaseDO/g' "$file"
            sed -i 's/import com.pinwan.mir.framework.mybatis.core.dataobject.BaseDO;/import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;/g' "$file"
        fi
    done
    
    log_success "实体类改造完成"
    
    # 2. 编译项目检查语法错误
    log_info "编译项目检查语法错误..."
    mvn clean compile -DskipTests
    
    if [ $? -eq 0 ]; then
        log_success "项目编译成功"
    else
        log_error "项目编译失败，请检查代码修改"
        exit 1
    fi
}

# 验证改造结果
verify_migration() {
    log_info "验证改造结果..."
    
    # 1. 检查数据库表结构
    log_info "检查数据库表结构..."
    
    TABLES_TO_CHECK=(
        "member_user"
        "member_level"
        "pay_app"
        "pay_order"
        "pay_wallet"
        "article"
    )
    
    for table in "${TABLES_TO_CHECK[@]}"; do
        RESULT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SHOW COLUMNS FROM $table LIKE 'tenant_id';" --batch --skip-column-names)
        
        if [ -n "$RESULT" ]; then
            log_success "表 $table 已添加 tenant_id 字段"
        else
            log_warning "表 $table 未找到 tenant_id 字段"
        fi
    done
    
    # 2. 检查数据迁移结果
    log_info "检查数据迁移结果..."
    
    for table in "${TABLES_TO_CHECK[@]}"; do
        ZERO_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COUNT(*) FROM $table WHERE tenant_id = 0;" --batch --skip-column-names 2>/dev/null || echo "0")
        
        if [ "$ZERO_COUNT" -eq "0" ]; then
            log_success "表 $table 数据迁移完成"
        else
            log_warning "表 $table 还有 $ZERO_COUNT 条记录的 tenant_id 为 0"
        fi
    done
    
    # 3. 检查默认租户
    TENANT_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COUNT(*) FROM system_tenant WHERE id = 1;" --batch --skip-column-names)
    
    if [ "$TENANT_COUNT" -eq "1" ]; then
        log_success "默认租户创建成功"
    else
        log_warning "默认租户未找到"
    fi
    
    log_success "改造结果验证完成"
}

# 生成改造报告
generate_report() {
    log_info "生成改造报告..."
    
    REPORT_FILE="reports/saas_migration_report_$(date +%Y%m%d_%H%M%S).md"
    mkdir -p "reports"
    
    cat > "$REPORT_FILE" << EOF
# SaaS多租户改造报告

## 改造时间
- 开始时间: $START_TIME
- 结束时间: $(date '+%Y-%m-%d %H:%M:%S')

## 改造内容

### 1. 数据库改造
- ✅ 添加租户字段
- ✅ 创建租户索引
- ✅ 迁移现有数据

### 2. 代码改造
- ✅ 实体类继承关系修改
- ✅ 多租户配置启用
- ✅ 项目编译验证

### 3. 验证结果
EOF

    # 添加表检查结果
    echo "#### 数据库表检查" >> "$REPORT_FILE"
    for table in "${TABLES_TO_CHECK[@]}"; do
        RESULT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SHOW COLUMNS FROM $table LIKE 'tenant_id';" --batch --skip-column-names 2>/dev/null)
        
        if [ -n "$RESULT" ]; then
            echo "- ✅ $table: tenant_id字段已添加" >> "$REPORT_FILE"
        else
            echo "- ❌ $table: tenant_id字段未找到" >> "$REPORT_FILE"
        fi
    done
    
    cat >> "$REPORT_FILE" << EOF

## 后续步骤
1. 重启应用服务
2. 测试多租户功能
3. 前端适配改造
4. 生产环境部署

## 备份信息
- 数据库备份文件: $(cat backup/*/backup_info.txt 2>/dev/null || echo "未找到备份文件")

## 注意事项
- 请在测试环境充分验证后再部署到生产环境
- 建议保留数据库备份文件至少30天
- 如遇问题，请参考回滚指南
EOF

    log_success "改造报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "========================================"
    echo "足球彩票系统SaaS多租户改造脚本"
    echo "开始时间: $START_TIME"
    echo "========================================"
    
    # 检查是否在正确的目录
    if [ ! -f "pom.xml" ]; then
        log_error "请在项目根目录执行此脚本"
        exit 1
    fi
    
    # 创建必要的目录
    mkdir -p backup reports logs
    
    # 执行改造步骤
    check_prerequisites
    read_config
    
    # 询问是否继续
    echo
    log_warning "即将开始SaaS多租户改造，此操作将修改数据库结构和代码"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消操作"
        exit 0
    fi
    
    # 执行改造
    backup_database
    migrate_database
    migrate_code
    verify_migration
    generate_report
    
    echo
    echo "========================================"
    log_success "SaaS多租户改造完成！"
    echo "========================================"
    echo
    log_info "下一步操作："
    echo "1. 重启应用服务"
    echo "2. 访问管理后台测试多租户功能"
    echo "3. 查看改造报告: reports/saas_migration_report_*.md"
    echo
}

# 执行主函数
main "$@"
