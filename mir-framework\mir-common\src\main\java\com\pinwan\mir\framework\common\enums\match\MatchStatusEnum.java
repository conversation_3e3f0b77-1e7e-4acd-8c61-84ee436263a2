package com.pinwan.mir.framework.common.enums.match;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MatchStatusEnum {
    /**
     * 比赛状态
     */

    EXCEPTION(0, "比赛异常"),
    NOT_STARTED(1, "未开赛"),
    FIRST_HALF(2, "上半场"),
    HALF_TIME(3, "中场"),
    SECOND_HALF(4, "下半场"),
    EXTRA_TIME(5, "加时赛"),
    EXTRA_TIME_DEPRECATED(6, "加时赛(弃用)"),
    PENALTY_SHOOTOUT(7, "点球决战"),
    FULL_TIME(8, "完场"),
    DELAYED(9, "推迟"),
    INTERRUPTED(10, "中断"),
    ABANDONED(11, "腰斩"),
    CANCELLED(12, "取消"),
    PENDING(13, "待定");

    private Integer value;

    private String desc;

    // 根据value获取对应的desc
    public static String getDesc(Integer value) {
        for (MatchStatusEnum item : MatchStatusEnum.values()) {
            if (item.getValue().equals(value)) {
                return item.getDesc();
            }
        }
        return "";
    }

}
