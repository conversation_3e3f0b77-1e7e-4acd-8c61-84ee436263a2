erDiagram
    %% 数据库ER图: sports_gaming
    %% 生成时间: 2025年07月10日 21:30:46

    account_statistic
 {
        string error "无法获取字段信息"
    }

    account_users
 {
        string error "无法获取字段信息"
    }

    article_push_config
 {
        string error "无法获取字段信息"
    }

    author_accomplishment
 {
        string error "无法获取字段信息"
    }

    author_article
 {
        string error "无法获取字段信息"
    }

    author_article_append
 {
        string error "无法获取字段信息"
    }

    author_article_pv_logs
 {
        string error "无法获取字段信息"
    }

    author_audit
 {
        string error "无法获取字段信息"
    }

    author_audit_copy1
 {
        string error "无法获取字段信息"
    }

    author_commission_rate
 {
        string error "无法获取字段信息"
    }

    author_day_report
 {
        string error "无法获取字段信息"
    }

    author_hit_rate
 {
        string error "无法获取字段信息"
    }

    author_info
 {
        string error "无法获取字段信息"
    }

    author_privilege_set
 {
        string error "无法获取字段信息"
    }

    author_withdraw_logs
 {
        string error "无法获取字段信息"
    }

    authors
 {
        string error "无法获取字段信息"
    }

    broomer_match_scheme
 {
        string error "无法获取字段信息"
    }

    broomers
 {
        string error "无法获取字段信息"
    }

    football_bd_odds
 {
        string error "无法获取字段信息"
    }

    football_bd_result
 {
        string error "无法获取字段信息"
    }

    football_bd_sf_odds
 {
        string error "无法获取字段信息"
    }

    football_bd_sf_result
 {
        string error "无法获取字段信息"
    }

    football_bo_live_odds
 {
        string error "无法获取字段信息"
    }

    football_company
 {
        string error "无法获取字段信息"
    }

    football_half_live_odds
 {
        string error "无法获取字段信息"
    }

    football_issue
 {
        string error "无法获取字段信息"
    }

    football_jc_odds
 {
        string error "无法获取字段信息"
    }

    football_jc_result
 {
        string error "无法获取字段信息"
    }

    football_live_odds
 {
        string error "无法获取字段信息"
    }

    football_match
 {
        string error "无法获取字段信息"
    }

    football_match_result
 {
        string error "无法获取字段信息"
    }

    football_tc_match
 {
        string error "无法获取字段信息"
    }

    gold_order
 {
        string error "无法获取字段信息"
    }

    guess_records
 {
        string error "无法获取字段信息"
    }

    home_banner
 {
        string error "无法获取字段信息"
    }

    infra_api_access_log
 {
        string error "无法获取字段信息"
    }

    infra_api_error_log
 {
        string error "无法获取字段信息"
    }

    infra_codegen_column
 {
        string error "无法获取字段信息"
    }

    infra_codegen_table
 {
        string error "无法获取字段信息"
    }

    infra_config
 {
        string error "无法获取字段信息"
    }

    infra_data_source_config
 {
        string error "无法获取字段信息"
    }

    infra_file
 {
        string error "无法获取字段信息"
    }

    infra_file_config
 {
        string error "无法获取字段信息"
    }

    infra_file_content
 {
        string error "无法获取字段信息"
    }

    infra_job
 {
        string error "无法获取字段信息"
    }

    infra_job_log
 {
        string error "无法获取字段信息"
    }

    match_category
 {
        string error "无法获取字段信息"
    }

    match_coach
 {
        string error "无法获取字段信息"
    }

    match_competition
 {
        string error "无法获取字段信息"
    }

    match_country
 {
        string error "无法获取字段信息"
    }

    match_future_record
 {
        string error "无法获取字段信息"
    }

    match_history
 {
        string error "无法获取字段信息"
    }

    match_lineup_detail
 {
        string error "无法获取字段信息"
    }

    match_list
 {
        string error "无法获取字段信息"
    }

    match_live_info
 {
        string error "无法获取字段信息"
    }

    match_odds
 {
        string error "无法获取字段信息"
    }

    match_player
 {
        string error "无法获取字段信息"
    }

    match_player_info
 {
        string error "无法获取字段信息"
    }

    match_player_transfer
 {
        string error "无法获取字段信息"
    }

    match_point_rank
 {
        string error "无法获取字段信息"
    }

    match_referee
 {
        string error "无法获取字段信息"
    }

    match_season
 {
        string error "无法获取字段信息"
    }

    match_stage
 {
        string error "无法获取字段信息"
    }

    match_stats
 {
        string error "无法获取字段信息"
    }

    match_team
 {
        string error "无法获取字段信息"
    }

    matches
 {
        string error "无法获取字段信息"
    }

    member_address
 {
        string error "无法获取字段信息"
    }

    member_attention
 {
        string error "无法获取字段信息"
    }

    member_author_privilege
 {
        string error "无法获取字段信息"
    }

    member_bind_record
 {
        string error "无法获取字段信息"
    }

    member_config
 {
        string error "无法获取字段信息"
    }

    member_experience_record
 {
        string error "无法获取字段信息"
    }

    member_group
 {
        string error "无法获取字段信息"
    }

    member_level
 {
        string error "无法获取字段信息"
    }

    member_level_record
 {
        string error "无法获取字段信息"
    }

    member_match_attention
 {
        string error "无法获取字段信息"
    }

    member_match_chat
 {
        string error "无法获取字段信息"
    }

    member_match_vote
 {
        string error "无法获取字段信息"
    }

    member_point_record
 {
        string error "无法获取字段信息"
    }

    member_privilege_log
 {
        string error "无法获取字段信息"
    }

    member_settlement_info
 {
        string error "无法获取字段信息"
    }

    member_sign_in_config
 {
        string error "无法获取字段信息"
    }

    member_sign_in_record
 {
        string error "无法获取字段信息"
    }

    member_tag
 {
        string error "无法获取字段信息"
    }

    member_user
 {
        string error "无法获取字段信息"
    }

    member_user_bak
 {
        string error "无法获取字段信息"
    }

    member_user_balance_logs
 {
        string error "无法获取字段信息"
    }

    member_user_ex_balance_logs
 {
        string error "无法获取字段信息"
    }

    member_user_gold_logs
 {
        string error "无法获取字段信息"
    }

    mp_account
 {
        string error "无法获取字段信息"
    }

    mp_auto_reply
 {
        string error "无法获取字段信息"
    }

    mp_click_logs
 {
        string error "无法获取字段信息"
    }

    mp_material
 {
        string error "无法获取字段信息"
    }

    mp_menu
 {
        string error "无法获取字段信息"
    }

    mp_message
 {
        string error "无法获取字段信息"
    }

    mp_mini_user
 {
        string error "无法获取字段信息"
    }

    mp_other_even_logs
 {
        string error "无法获取字段信息"
    }

    mp_pay_config_log
 {
        string error "无法获取字段信息"
    }

    mp_tag
 {
        string error "无法获取字段信息"
    }

    mp_template_config
 {
        string error "无法获取字段信息"
    }

    mp_user
 {
        string error "无法获取字段信息"
    }

    partner_audit
 {
        string error "无法获取字段信息"
    }

    partner_commission_rate
 {
        string error "无法获取字段信息"
    }

    partner_divide_config
 {
        string error "无法获取字段信息"
    }

    partner_info
 {
        string error "无法获取字段信息"
    }

    partner_invite_logs
 {
        string error "无法获取字段信息"
    }

    partner_settlement_info
 {
        string error "无法获取字段信息"
    }

    partner_withdraw_logs
 {
        string error "无法获取字段信息"
    }

    pay_app
 {
        string error "无法获取字段信息"
    }

    pay_bank_info
 {
        string error "无法获取字段信息"
    }

    pay_channel
 {
        string error "无法获取字段信息"
    }

    pay_demo_order
 {
        string error "无法获取字段信息"
    }

    pay_demo_transfer
 {
        string error "无法获取字段信息"
    }

    pay_notify_log
 {
        string error "无法获取字段信息"
    }

    pay_notify_task
 {
        string error "无法获取字段信息"
    }

    pay_order
 {
        string error "无法获取字段信息"
    }

    pay_order_extension
 {
        string error "无法获取字段信息"
    }

    pay_refund
 {
        string error "无法获取字段信息"
    }

    pay_transfer
 {
        string error "无法获取字段信息"
    }

    pay_wallet
 {
        string error "无法获取字段信息"
    }

    pay_wallet_recharge
 {
        string error "无法获取字段信息"
    }

    pay_wallet_recharge_package
 {
        string error "无法获取字段信息"
    }

    pay_wallet_transaction
 {
        string error "无法获取字段信息"
    }

    play_type
 {
        string error "无法获取字段信息"
    }

    playing_method
 {
        string error "无法获取字段信息"
    }

    privilege_order
 {
        string error "无法获取字段信息"
    }

    push_amount_config
 {
        string error "无法获取字段信息"
    }

    qrtz_blob_triggers
 {
        string error "无法获取字段信息"
    }

    qrtz_calendars
 {
        string error "无法获取字段信息"
    }

    qrtz_cron_triggers
 {
        string error "无法获取字段信息"
    }

    qrtz_fired_triggers
 {
        string error "无法获取字段信息"
    }

    qrtz_job_details
 {
        string error "无法获取字段信息"
    }

    qrtz_locks
 {
        string error "无法获取字段信息"
    }

    qrtz_paused_trigger_grps
 {
        string error "无法获取字段信息"
    }

    qrtz_scheduler_state
 {
        string error "无法获取字段信息"
    }

    qrtz_simple_triggers
 {
        string error "无法获取字段信息"
    }

    qrtz_simprop_triggers
 {
        string error "无法获取字段信息"
    }

    qrtz_triggers
 {
        string error "无法获取字段信息"
    }

    recommend_author
 {
        string error "无法获取字段信息"
    }

    recommend_user_register_logs
 {
        string error "无法获取字段信息"
    }

    refund_order
 {
        string error "无法获取字段信息"
    }

    report_operation_day
 {
        string error "无法获取字段信息"
    }

    scheme_order
 {
        string error "无法获取字段信息"
    }

    scheme_play
 {
        string error "无法获取字段信息"
    }

    sys_configs
 {
        string error "无法获取字段信息"
    }

    system_command
 {
        string error "无法获取字段信息"
    }

    system_dept
 {
        string error "无法获取字段信息"
    }

    system_dict_data
 {
        string error "无法获取字段信息"
    }

    system_dict_type
 {
        string error "无法获取字段信息"
    }

    system_login_log
 {
        string error "无法获取字段信息"
    }

    system_mail_account
 {
        string error "无法获取字段信息"
    }

    system_mail_log
 {
        string error "无法获取字段信息"
    }

    system_mail_template
 {
        string error "无法获取字段信息"
    }

    system_menu
 {
        string error "无法获取字段信息"
    }

    system_notice
 {
        string error "无法获取字段信息"
    }

    system_notify_message
 {
        string error "无法获取字段信息"
    }

    system_notify_template
 {
        string error "无法获取字段信息"
    }

    system_oauth2_access_token
 {
        string error "无法获取字段信息"
    }

    system_oauth2_approve
 {
        string error "无法获取字段信息"
    }

    system_oauth2_client
 {
        string error "无法获取字段信息"
    }

    system_oauth2_code
 {
        string error "无法获取字段信息"
    }

    system_oauth2_refresh_token
 {
        string error "无法获取字段信息"
    }

    system_operate_log
 {
        string error "无法获取字段信息"
    }

    system_post
 {
        string error "无法获取字段信息"
    }

    system_role
 {
        string error "无法获取字段信息"
    }

    system_role_menu
 {
        string error "无法获取字段信息"
    }

    system_sensitive_word
 {
        string error "无法获取字段信息"
    }

    system_sms_channel
 {
        string error "无法获取字段信息"
    }

    system_sms_code
 {
        string error "无法获取字段信息"
    }

    system_sms_log
 {
        string error "无法获取字段信息"
    }

    system_sms_template
 {
        string error "无法获取字段信息"
    }

    system_social_client
 {
        string error "无法获取字段信息"
    }

    system_social_user
 {
        string error "无法获取字段信息"
    }

    system_social_user_bind
 {
        string error "无法获取字段信息"
    }

    system_tenant
 {
        string error "无法获取字段信息"
    }

    system_tenant_package
 {
        string error "无法获取字段信息"
    }

    system_user_post
 {
        string error "无法获取字段信息"
    }

    system_user_role
 {
        string error "无法获取字段信息"
    }

    system_users
 {
        string error "无法获取字段信息"
    }

    third_pay_channel
 {
        string error "无法获取字段信息"
    }

    third_pay_sqb_config
 {
        string error "无法获取字段信息"
    }

    wecom_setting
 {
        string error "无法获取字段信息"
    }

    wx_external_contact
 {
        string error "无法获取字段信息"
    }

    wx_external_contact_way_config
 {
        string error "无法获取字段信息"
    }

    wx_work_setting
 {
        string error "无法获取字段信息"
    }

    yudao_demo01_contact
 {
        string error "无法获取字段信息"
    }

    yudao_demo02_category
 {
        string error "无法获取字段信息"
    }

    yudao_demo03_course
 {
        string error "无法获取字段信息"
    }

    yudao_demo03_grade
 {
        string error "无法获取字段信息"
    }

    yudao_demo03_student
 {
        string error "无法获取字段信息"
    }

    %% 表关系（基于命名约定推断）
