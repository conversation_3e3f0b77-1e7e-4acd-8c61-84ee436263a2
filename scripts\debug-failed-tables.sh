#!/bin/bash

# =============================================
# 调试前4个失败表的问题
# =============================================

echo "========================================"
echo "调试前4个失败表的问题"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

# 失败的表
FAILED_TABLES=("account_statistic" "account_users" "article_push_config" "author_accomplishment")
SUCCESS_TABLE="author_article"

echo "数据库: $DB_NAME"
echo

# 1. 检查表是否存在
echo "=== 测试1: 检查表是否存在 ==="
for table_name in "${FAILED_TABLES[@]}" "$SUCCESS_TABLE"; do
    result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
        --batch --skip-column-names 2>/dev/null)
    
    if [ "$result" = "1" ]; then
        echo "✅ $table_name: 表存在"
    else
        echo "❌ $table_name: 表不存在 (结果: '$result')"
    fi
done

# 2. 检查表注释查询
echo
echo "=== 测试2: 检查表注释查询 ==="
for table_name in "${FAILED_TABLES[@]}" "$SUCCESS_TABLE"; do
    echo "测试表: $table_name"
    
    # 直接查询
    result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
        --batch --skip-column-names 2>/dev/null)
    
    echo "  表注释查询结果: '$result'"
    
    # 写入临时文件测试
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
        --batch --skip-column-names 2>/dev/null > ./debug_comment_${table_name}.txt
    
    if [ -s "./debug_comment_${table_name}.txt" ]; then
        file_content=$(cat ./debug_comment_${table_name}.txt)
        echo "  临时文件内容: '$file_content'"
        echo "  ✅ 临时文件写入成功"
    else
        echo "  ❌ 临时文件为空"
    fi
    
    rm -f ./debug_comment_${table_name}.txt
    echo
done

# 3. 检查字段查询
echo "=== 测试3: 检查字段查询 ==="
for table_name in "${FAILED_TABLES[@]}" "$SUCCESS_TABLE"; do
    echo "测试表: $table_name"
    
    # 简单字段查询
    result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
        --batch --skip-column-names 2>/dev/null)
    
    echo "  字段数量: '$result'"
    
    # 复杂字段查询
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COLUMN_NAME, COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' LIMIT 3;" \
        --batch --skip-column-names 2>/dev/null > ./debug_columns_${table_name}.txt
    
    file_size=$(wc -c < ./debug_columns_${table_name}.txt)
    file_lines=$(wc -l < ./debug_columns_${table_name}.txt)
    
    echo "  字段查询文件大小: $file_size 字节"
    echo "  字段查询文件行数: $file_lines 行"
    
    if [ -s "./debug_columns_${table_name}.txt" ]; then
        echo "  ✅ 字段查询成功"
        echo "  前3个字段:"
        cat ./debug_columns_${table_name}.txt
    else
        echo "  ❌ 字段查询失败"
    fi
    
    rm -f ./debug_columns_${table_name}.txt
    echo
done

# 4. 检查表名是否有特殊字符
echo "=== 测试4: 检查表名特殊字符 ==="
for table_name in "${FAILED_TABLES[@]}" "$SUCCESS_TABLE"; do
    echo "表名: '$table_name'"
    echo "  长度: ${#table_name}"
    echo "  十六进制: $(echo -n "$table_name" | xxd -p)"
    echo
done

# 5. 检查数据库名
echo "=== 测试5: 检查数据库名 ==="
current_db=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT DATABASE();" --batch --skip-column-names 2>/dev/null)
echo "当前数据库: '$current_db'"

# 6. 直接SHOW TABLES检查
echo
echo "=== 测试6: SHOW TABLES检查 ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null | head -10

echo
echo "========================================"
echo "调试完成！"
echo "========================================"
