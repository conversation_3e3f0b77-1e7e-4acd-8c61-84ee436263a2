package com.pinwan.mir.framework.common.util.date;


import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * 日期处理
 */
public class MirDateUtil {

    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * MM月dd日
     */
    public static final String DEFAULT_PATTERN_MMDD = "MM月dd日";

    /**
     * 日期格式化yyyyMM
     */
    public static final String DATE_FORMATER_YYYYMM = "yyyyMM";

    /**
     * 日期格式化hhmmss
     */
    public static final String DATE_FORMATER_HHMMSS = "HHmmss";

    /**
     * 日期格式化HH:mm:ss
     */
    public static final String DATE_FORMATER_HH_MM_SS = "HH:mm:ss";

    /**
     * 日期格式化HH:mm
     */
    public static final String DATE_FORMATER_HH_MM = "HH:mm";

    /**
     * 日期格式化yyyyMMdd
     */
    public static final String DATE_FORMATER_YYYYMMDD = "yyyyMMdd";

    /**
     * 日期格式化yyyyMMddHH
     */
    public static final String DATE_FORMATER_YYYYMMDDHH = "yyyyMMddHH";

    /**
     * 日期格式化MM-dd HH:mm:ss
     */
    public static final String DATE_FORMATER_MMDD_HH_MM_SS = "MM-dd HH:mm:ss";

    /**
     * 日期格式化yyyyMMddHHmmss
     */
    public static final String DATE_FORMATER_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    /**
     * 日期格式化yyyyMMddHHmmssSSS
     */
    public static final String DATE_FORMATER_YYYYMMDDHHMMSSSSS = "yyyyMMddHHmmssSSS";

    /**
     * 日期格式化yyMMddHHmmssSSS
     */
    public static final String DATE_FORMATER_YYMMDDHHMMSSSSS = "yyMMddHHmmssSSS";

    /**
     * 日期格式化yyyy-MM-dd HH:mm:ss
     */
    public static final String DATE_FORMATER_YYYYMMDD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式化yyyy-MM-dd
     */
    public static final String DATE_FORMATER_YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 日期格式化yyyy-MM
     */
    public static final String DATE_FORMATER_YYYY_MM = "yyyy-MM";

    /**
     * 日期格式化日期格式为：yyyy-MM-dd
     *
     * @param date 日期
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    /**
     * 字符串转换成日期
     *
     * @param strDate 日期字符串
     * @param pattern 日期的格式，如：DateUtils.DATE_TIME_PATTERN
     */
    public static Date stringToDate(String strDate, String pattern) {
        if (!StringUtils.hasLength(strDate)) {
            return null;
        }
        DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 根据周数，获取开始日期、结束日期
     *
     * @param week 周期  0本周，-1上周，-2上上周，1下周，2下下周
     * @return 返回date[0]开始日期、date[1]结束日期
     */
    public static Date[] getWeekStartAndEnd(int week) {
        DateTime dateTime = new DateTime();
        LocalDate date = new LocalDate(dateTime.plusWeeks(week));
        date = date.dayOfWeek().withMinimumValue();
        Date beginDate = date.toDate();
        Date endDate = date.plusDays(6).toDate();
        return new Date[]{beginDate, endDate};
    }

    /**
     * 对日期的【秒】进行加/减
     *
     * @param date    日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(Date date, int seconds) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusSeconds(seconds).toDate();
    }

    /**
     * 对日期的【分钟】进行加/减
     *
     * @param date    日期
     * @param minutes 分钟数，负数为减
     * @return 加/减几分钟后的日期
     */
    public static Date addDateMinutes(Date date, int minutes) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMinutes(minutes).toDate();
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 对日期的【小时】进行加/减
     *
     * @param date  日期
     * @param hours 小时数，负数为减
     * @return 加/减几小时后的日期
     */
    public static Date addDateHours(Date date, int hours) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusHours(hours).toDate();
    }

    /**
     * 对日期的【天】进行加/减
     *
     * @param date 日期
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */
    public static Date addDateDays(Date date, int days) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusDays(days).toDate();
    }

    /**
     * 对日期的【周】进行加/减
     *
     * @param date  日期
     * @param weeks 周数，负数为减
     * @return 加/减几周后的日期
     */
    public static Date addDateWeeks(Date date, int weeks) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusWeeks(weeks).toDate();
    }

    /**
     * 对日期的【月】进行加/减
     *
     * @param date   日期
     * @param months 月数，负数为减
     * @return 加/减几月后的日期
     */
    public static Date addDateMonths(Date date, int months) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMonths(months).toDate();
    }

    /**
     * 对日期的【年】进行加/减
     *
     * @param date  日期
     * @param years 年数，负数为减
     * @return 加/减几年后的日期
     */
    public static Date addDateYears(Date date, int years) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusYears(years).toDate();
    }


    /**
     * 获取指定日期几天前日期
     *
     * @param basic
     * @param days
     * @return
     */
    public static Date getDateBeforeDays(Date basic, int days) {
        if (days <= 0)
            return basic;
        Calendar c = Calendar.getInstance();
        c.setTime(basic);
        c.add(Calendar.DAY_OF_MONTH, -days);
        return c.getTime();
    }

    /**
     * 获取data类型的年月日
     *
     * @param date
     * @return
     */
    public static Date dateTOShort(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(date);
        Date date_ = null;
        try {
            date_ = formatter.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date_;
    }

    /**
     * 返回当前日期时间
     *
     * @param formatStr 格式化类型字符串
     */
    public static String getSysDate(String formatStr) {
        return getCurDateTime(formatStr);
    }

    /**
     * 根据给定的格式返回当前日期或时间 相当于调用getDateTime(formatStr,Calendar.getInstance()
     *
     * @param formatStr 日期时间格式 例如：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getCurDateTime(String formatStr) {
        return getDateTime(formatStr, Calendar.getInstance());
    }

    /**
     * 根据给定的格式、Calendar返回相应字符串
     *
     * @param formatStr 日期时间格式 例如：yyyy-MM-dd HH:mm:ss
     * @param c         Calendar实例
     * @return
     */
    public static String getDateTime(String formatStr, Calendar c) {
        SimpleDateFormat nowDate = new SimpleDateFormat(formatStr);
        String curTimeStr = nowDate.format(c.getTime());
        return curTimeStr;
    }

    /**
     * 日期Format
     *
     * @param date
     * @param format
     * @return
     */
    public static String dateFormat(Date date, String format) {
        DateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(date);
    }

    /**
     * 获取指定日期的 年、月、日、时
     */
    public static Map<String, Integer> getDateYearMonthDayTime(Date date) {
        // Date date = stringToDate(dateStr, "YYYY-MM-dd HH:mm:ss");
        Map<String, Integer> dateMap = new HashMap<>();
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        dateMap.put("year", ca.get(Calendar.YEAR));
        dateMap.put("month", (ca.get(Calendar.MONTH) + 1));
        dateMap.put("day", ca.get(Calendar.DAY_OF_MONTH));
        dateMap.put("hour", ca.get(Calendar.HOUR_OF_DAY));
        return dateMap;
    }

    /**
     * 获取两个日期之间的所有日期 (年月日)
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public static List<String> getBetweenDate(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 声明保存日期集合
        List<String> list = new ArrayList<String>();
        try {
            // 转化成日期类型
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);
            //用Calendar 进行日期比较判断
            Calendar calendar = Calendar.getInstance();
            while (startDate.getTime() <= endDate.getTime()) {
                // 把日期添加到集合
                list.add(sdf.format(startDate));
                // 设置日期
                calendar.setTime(startDate);
                //把日期增加一天
                calendar.add(Calendar.DATE, 1);
                // 获取增加后的日期
                startDate = calendar.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;
    }

    //获取昨日日期
    public static String getYesterdayString(Date date){

        SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH,-1);
        Date yesterday = calendar.getTime();
        String dateString = YYYYMMDD.format(yesterday);


        return dateString;
    }

    //获取最近3天
    public static List<String> getLast3Day(Date date){

        SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        ArrayList<String> strings = new ArrayList<>();

        for (int j=0;j<3;j++) {
            Date monday = calendar.getTime();
            strings.add(YYYYMMDD.format(monday));
            calendar.add(Calendar.DAY_OF_MONTH,-1);
        }
        Collections.reverse(strings);
        return strings;
    }


    //获取最近7天
    public static List<String> getLast7Day(Date date){

        SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        ArrayList<String> strings = new ArrayList<>();

        for (int j=0;j<7;j++) {
            Date monday = calendar.getTime();
            strings.add(YYYYMMDD.format(monday));
            calendar.add(Calendar.DAY_OF_MONTH,-1);
        }
        Collections.reverse(strings);
        return strings;
    }


    //获取本周日期
    public static List<String> getThisWeek(Date date){

        SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        int i = calendar.get(Calendar.DAY_OF_WEEK);

        calendar.add(Calendar.DAY_OF_MONTH,-i+2);
        if (i==1){
            calendar.add(Calendar.WEEK_OF_YEAR,-1);
        }

        ArrayList<String> strings = new ArrayList<>();
        //本周一
        for (int j=0;j<7;j++) {
            Date monday = calendar.getTime();
            strings.add(YYYYMMDD.format(monday));
            calendar.add(Calendar.DAY_OF_MONTH,1);
        }

        return strings;
    }

    //获取上周日期
    public static List<String> getLastWeek(Date date){

        SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        calendar.add(Calendar.WEEK_OF_YEAR,-1);
        int i = calendar.get(Calendar.DAY_OF_WEEK);

        calendar.add(Calendar.DAY_OF_MONTH,-i+2);
        if (i==1){
            calendar.add(Calendar.WEEK_OF_YEAR,-1);
        }

        ArrayList<String> strings = new ArrayList<>();
        //本周一
        for (int j=0;j<7;j++) {
            Date monday = calendar.getTime();
            strings.add(YYYYMMDD.format(monday));
            calendar.add(Calendar.DAY_OF_MONTH,1);
        }

        return strings;
    }

    //获取本月日期
    public static List<String> getThisMonth(Date date){

        SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH);
        calendar.set(Calendar.DAY_OF_MONTH,1);
        ArrayList<String> strings = new ArrayList<>();
        //本周一
        for (int j=0;j<31;j++) {
            int thisMonth = calendar.get(Calendar.MONTH);
            if (month!=thisMonth){
                break;
            }
            Date monday = calendar.getTime();
            strings.add(YYYYMMDD.format(monday));
            calendar.add(Calendar.DAY_OF_MONTH,1);
        }

        return strings;
    }

    //获取上月日期
    public static List<String> getLastMonth(Date date){

        SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);
        int month = calendar.get(Calendar.MONTH);
        calendar.set(Calendar.DAY_OF_MONTH,1);
        ArrayList<String> strings = new ArrayList<>();
        //本周一
        for (int j=0;j<31;j++) {
            int thisMonth = calendar.get(Calendar.MONTH);
            if (month!=thisMonth){
                break;
            }
            Date monday = calendar.getTime();
            strings.add(YYYYMMDD.format(monday));
            calendar.add(Calendar.DAY_OF_MONTH,1);
        }

        return strings;
    }

    //获取最近30天
    public static List<String> getLast30Day(Date date){

        SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        ArrayList<String> strings = new ArrayList<>();

        for (int j=0;j<30;j++) {
            Date monday = calendar.getTime();
            strings.add(YYYYMMDD.format(monday));
            calendar.add(Calendar.DAY_OF_MONTH,-1);
        }
        Collections.reverse(strings);
        return strings;
    }

    //获取本月日期
    public static List<Date> getThisMonthDate(Date date){

        SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH);
        calendar.set(Calendar.DAY_OF_MONTH,1);
        ArrayList<Date> strings = new ArrayList<>();
        //本周一
        for (int j=0;j<31;j++) {
            int thisMonth = calendar.get(Calendar.MONTH);
            if (month!=thisMonth){
                break;
            }
            Date monday = calendar.getTime();
            strings.add(monday);
            calendar.add(Calendar.DAY_OF_MONTH,1);
        }

        return strings;
    }


    public static long getDays(String startTime,String endTime) {
        DateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date star = dft.parse(startTime);//开始时间
            Date endDay=dft.parse(endTime);//结束时间
            Long starTimeLong=star.getTime();
            Long endTimeLong=endDay.getTime();
            Long num=endTimeLong-starTimeLong;//时间戳相差的毫秒数
            return num/24/60/60/1000;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0L;
    }

    public static String getSimpleDate(String date){
        String originalDateString = date;
        // 创建SimpleDateFormat对象，用于解析原始日期字符串
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            // 解析原始日期字符串为Date对象
            Date date1 = inputFormat.parse(originalDateString);

            // 创建另一个SimpleDateFormat对象，用于将Date对象格式化为新的字符串格式
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-M-d");

            // 将Date对象格式化为新的字符串
            originalDateString = outputFormat.format(date1);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return originalDateString;
    }

    /**
     * 获取一段时间内，每隔7天的日期
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> getDatesEverySevenDays(String startTime, String endTime) {
        List<String> dates = new ArrayList<>();

        Date endDate = stringToDate(endTime,DATE_PATTERN);

        long days = getDays(startTime,endTime);
        for (int i = 0; i <= days; i += 7) {
            Date everySevenDaysDate = addDateDays(stringToDate(startTime,DATE_PATTERN),i);
            if (everySevenDaysDate.before(endDate) || everySevenDaysDate.equals(endDate)) {
                dates.add(format(everySevenDaysDate));
            }
        }

        return dates;
    }

    public static List<String> getMonthsInRange(String startTime, String endTime) {
        List<String> months = new ArrayList<>();

        java.time.LocalDate currentDate = java.time.LocalDate.parse(startTime);
        java.time.LocalDate endDate = java.time.LocalDate.parse(endTime);

        while (!currentDate.isAfter(endDate)) {
            java.time.format.DateTimeFormatter formatter =  java.time.format.DateTimeFormatter.ofPattern("yyyy-MM");
            YearMonth yearMonth = YearMonth.from(currentDate);
            months.add(yearMonth.format(formatter));
            currentDate = currentDate.plus(1, ChronoUnit.MONTHS);
        }

        return months;
    }

    //获取今天到月末的剩余天数
    public static int daysUntilMonthEnd() {
        // 获取今天的日期
        java.time.LocalDate today = java.time.LocalDate.now();

        // 获取本月最后一天的日期
        java.time.LocalDate endOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());

        // 计算从今天到本月结束的天数

        return (int) ChronoUnit.DAYS.between(today, endOfMonth);
    }

    public static int getMonthDayNum(String month){
        // 解析日期字符串为 YearMonth 对象
        YearMonth yearMonth = YearMonth.parse(month);

        // 获取该月份的天数
        int days = yearMonth.lengthOfMonth();

        return days;
    }

    //获取月份的所有日期
    public static List<String> getMonthDays(String month, boolean untilToday){
        List<String> days = new ArrayList<>();
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
        java.time.LocalDate startDate = java.time.LocalDate.parse(month + "-01", formatter);
        java.time.LocalDate endDate;
        if (untilToday) {
            // 如果 untilToday 为 true，则使用今天的日期作为结束日期
            endDate = java.time.LocalDate.now();
        } else {
            // 否则，获取整个月份的最后一天作为结束日期
            endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
        }

        while (!startDate.isAfter(endDate)) {
            days.add(startDate.format(java.time.format.DateTimeFormatter.ISO_LOCAL_DATE));
            startDate = startDate.plusDays(1);
        }

        return days;
    }


    public static void main(String[] args) {

        System.out.println(daysUntilMonthEnd());
    }



}
