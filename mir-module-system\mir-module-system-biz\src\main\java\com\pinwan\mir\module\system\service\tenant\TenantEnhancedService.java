package com.pinwan.mir.module.system.service.tenant;

import com.pinwan.mir.framework.common.exception.ServiceException;
import com.pinwan.mir.framework.common.pojo.PageResult;
import com.pinwan.mir.framework.tenant.core.context.TenantContextHolder;
import com.pinwan.mir.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import com.pinwan.mir.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import com.pinwan.mir.module.system.dal.dataobject.tenant.TenantDO;
import com.pinwan.mir.module.system.service.tenant.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static com.pinwan.mir.module.system.enums.ErrorCodeConstants.*;

/**
 * 租户增强服务
 * 在原有租户服务基础上，增加SaaS化相关功能
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TenantEnhancedService {

    @Resource
    private TenantService tenantService;

    @Resource
    private TenantInitService tenantInitService;

    /**
     * 创建租户（增强版）
     * 自动初始化租户基础数据
     *
     * @param createReqVO 创建信息
     * @return 租户编号
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createTenantWithInit(TenantSaveReqVO createReqVO) {
        log.info("[createTenantWithInit][开始创建租户: {}]", createReqVO.getName());
        
        // 1. 创建租户
        Long tenantId = tenantService.createTenant(createReqVO);
        
        try {
            // 2. 初始化租户基础数据
            tenantInitService.initTenantData(tenantId);
            
            log.info("[createTenantWithInit][租户创建成功，ID: {}]", tenantId);
            return tenantId;
        } catch (Exception e) {
            log.error("[createTenantWithInit][租户基础数据初始化失败，回滚租户创建]", e);
            // 如果初始化失败，删除已创建的租户
            try {
                tenantService.deleteTenant(tenantId);
            } catch (Exception deleteException) {
                log.error("[createTenantWithInit][回滚删除租户失败]", deleteException);
            }
            throw new ServiceException(TENANT_INIT_FAILED, "租户基础数据初始化失败: " + e.getMessage());
        }
    }

    /**
     * 删除租户（增强版）
     * 清理所有租户相关数据
     *
     * @param id 租户编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTenantWithCleanup(Long id) {
        log.info("[deleteTenantWithCleanup][开始删除租户: {}]", id);
        
        // 1. 验证租户存在
        TenantDO tenant = tenantService.getTenant(id);
        if (tenant == null) {
            throw new ServiceException(TENANT_NOT_EXISTS);
        }
        
        try {
            // 2. 清理租户业务数据
            tenantInitService.cleanTenantData(id);
            
            // 3. 删除租户
            tenantService.deleteTenant(id);
            
            log.info("[deleteTenantWithCleanup][租户删除成功: {}]", id);
        } catch (Exception e) {
            log.error("[deleteTenantWithCleanup][租户删除失败: {}]", id, e);
            throw new ServiceException(TENANT_DELETE_FAILED, "租户删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用租户
     *
     * @param id 租户编号
     */
    public void enableTenant(Long id) {
        log.info("[enableTenant][启用租户: {}]", id);
        
        TenantDO tenant = tenantService.getTenant(id);
        if (tenant == null) {
            throw new ServiceException(TENANT_NOT_EXISTS);
        }
        
        // 检查租户是否过期
        if (tenant.getExpireTime() != null && tenant.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException(TENANT_EXPIRED);
        }
        
        // 更新租户状态
        TenantSaveReqVO updateReqVO = new TenantSaveReqVO();
        updateReqVO.setName(tenant.getName());
        updateReqVO.setContactName(tenant.getContactName());
        updateReqVO.setContactMobile(tenant.getContactMobile());
        updateReqVO.setStatus(0); // 启用状态
        updateReqVO.setWebsite(tenant.getWebsite());
        updateReqVO.setPackageId(tenant.getPackageId());
        updateReqVO.setExpireTime(tenant.getExpireTime());
        updateReqVO.setAccountCount(tenant.getAccountCount());
        
        tenantService.updateTenant(id, updateReqVO);
        
        log.info("[enableTenant][租户启用成功: {}]", id);
    }

    /**
     * 禁用租户
     *
     * @param id 租户编号
     */
    public void disableTenant(Long id) {
        log.info("[disableTenant][禁用租户: {}]", id);
        
        TenantDO tenant = tenantService.getTenant(id);
        if (tenant == null) {
            throw new ServiceException(TENANT_NOT_EXISTS);
        }
        
        // 更新租户状态
        TenantSaveReqVO updateReqVO = new TenantSaveReqVO();
        updateReqVO.setName(tenant.getName());
        updateReqVO.setContactName(tenant.getContactName());
        updateReqVO.setContactMobile(tenant.getContactMobile());
        updateReqVO.setStatus(1); // 禁用状态
        updateReqVO.setWebsite(tenant.getWebsite());
        updateReqVO.setPackageId(tenant.getPackageId());
        updateReqVO.setExpireTime(tenant.getExpireTime());
        updateReqVO.setAccountCount(tenant.getAccountCount());
        
        tenantService.updateTenant(id, updateReqVO);
        
        log.info("[disableTenant][租户禁用成功: {}]", id);
    }

    /**
     * 续费租户
     *
     * @param id 租户编号
     * @param months 续费月数
     */
    public void renewTenant(Long id, Integer months) {
        log.info("[renewTenant][租户续费: {}, 月数: {}]", id, months);
        
        TenantDO tenant = tenantService.getTenant(id);
        if (tenant == null) {
            throw new ServiceException(TENANT_NOT_EXISTS);
        }
        
        // 计算新的过期时间
        LocalDateTime newExpireTime;
        if (tenant.getExpireTime() == null || tenant.getExpireTime().isBefore(LocalDateTime.now())) {
            // 如果已过期或未设置过期时间，从当前时间开始计算
            newExpireTime = LocalDateTime.now().plusMonths(months);
        } else {
            // 如果未过期，从原过期时间开始计算
            newExpireTime = tenant.getExpireTime().plusMonths(months);
        }
        
        // 更新租户信息
        TenantSaveReqVO updateReqVO = new TenantSaveReqVO();
        updateReqVO.setName(tenant.getName());
        updateReqVO.setContactName(tenant.getContactName());
        updateReqVO.setContactMobile(tenant.getContactMobile());
        updateReqVO.setStatus(0); // 续费后自动启用
        updateReqVO.setWebsite(tenant.getWebsite());
        updateReqVO.setPackageId(tenant.getPackageId());
        updateReqVO.setExpireTime(newExpireTime);
        updateReqVO.setAccountCount(tenant.getAccountCount());
        
        tenantService.updateTenant(id, updateReqVO);
        
        log.info("[renewTenant][租户续费成功: {}, 新过期时间: {}]", id, newExpireTime);
    }

    /**
     * 检查租户状态
     *
     * @param tenantId 租户编号
     * @return 租户状态信息
     */
    public TenantStatusInfo checkTenantStatus(Long tenantId) {
        TenantDO tenant = tenantService.getTenant(tenantId);
        if (tenant == null) {
            return TenantStatusInfo.builder()
                .exists(false)
                .enabled(false)
                .expired(true)
                .message("租户不存在")
                .build();
        }
        
        boolean enabled = tenant.getStatus() == 0;
        boolean expired = tenant.getExpireTime() != null && tenant.getExpireTime().isBefore(LocalDateTime.now());
        
        String message = "";
        if (!enabled) {
            message = "租户已禁用";
        } else if (expired) {
            message = "租户已过期";
        } else {
            message = "租户正常";
        }
        
        return TenantStatusInfo.builder()
            .exists(true)
            .enabled(enabled)
            .expired(expired)
            .expireTime(tenant.getExpireTime())
            .message(message)
            .build();
    }

    /**
     * 获取当前租户信息
     *
     * @return 当前租户信息
     */
    public TenantDO getCurrentTenant() {
        Long tenantId = TenantContextHolder.getTenantId();
        if (tenantId == null) {
            throw new ServiceException(TENANT_NOT_EXISTS, "未找到租户上下文");
        }
        
        return tenantService.getTenant(tenantId);
    }

    /**
     * 租户状态信息
     */
    @lombok.Data
    @lombok.Builder
    public static class TenantStatusInfo {
        /**
         * 租户是否存在
         */
        private Boolean exists;
        
        /**
         * 租户是否启用
         */
        private Boolean enabled;
        
        /**
         * 租户是否过期
         */
        private Boolean expired;
        
        /**
         * 过期时间
         */
        private LocalDateTime expireTime;
        
        /**
         * 状态描述
         */
        private String message;
    }
}
