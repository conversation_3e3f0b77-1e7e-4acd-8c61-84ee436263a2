#!/bin/bash

# =============================================
# 前端项目使用表分析脚本
# 基于后端API分析前端管理后台可能使用的数据库表
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "前端项目使用表分析工具"
echo "========================================"

# 检查是否在项目根目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 请在项目根目录执行此脚本"
    exit 1
fi

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database

log_info "分析后端API和Controller..."

# 获取所有表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

log_info "基于后端模块分析前端使用的表..."

# 生成前端使用表分析报告
cat > docs/database/frontend-tables-analysis.md << 'EOF'
# 前端管理后台使用表分析

## 📊 概览

本报告分析football-lottery-frontend（后台管理前端）项目可能使用的数据库表。
基于后端API模块和业务功能进行分析。

## 🎯 分析依据

1. **后端Controller分析**: 检查各模块的Controller接口
2. **业务模块划分**: 基于系统功能模块
3. **管理后台特性**: 后台管理通常需要的功能表

## 📋 前端使用的表分类

### 1. 系统管理模块

后台管理系统的核心功能，前端必须使用：

EOF

echo "" >> docs/database/frontend-tables-analysis.md

# 分析系统管理相关表
SYSTEM_TABLES=$(grep "^system_" /tmp/all_tables.txt 2>/dev/null || echo "")

if [ -n "$SYSTEM_TABLES" ]; then
    echo "#### 系统核心表" >> docs/database/frontend-tables-analysis.md
    echo "" >> docs/database/frontend-tables-analysis.md
    
    echo "$SYSTEM_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            # 判断表的重要性
            IMPORTANCE=""
            case "$table_name" in
                "system_users") IMPORTANCE="🔴 核心 - 管理员登录" ;;
                "system_role") IMPORTANCE="🔴 核心 - 角色管理" ;;
                "system_menu") IMPORTANCE="🔴 核心 - 菜单权限" ;;
                "system_tenant") IMPORTANCE="🔴 核心 - 租户管理" ;;
                "system_dept") IMPORTANCE="🟡 重要 - 部门管理" ;;
                "system_post") IMPORTANCE="🟡 重要 - 岗位管理" ;;
                "system_dict_type") IMPORTANCE="🟡 重要 - 字典管理" ;;
                "system_dict_data") IMPORTANCE="🟡 重要 - 字典数据" ;;
                "system_notice") IMPORTANCE="🟢 一般 - 通知公告" ;;
                *) IMPORTANCE="🟢 一般" ;;
            esac
            
            echo "- **$table_name**: $TABLE_INFO $IMPORTANCE" >> docs/database/frontend-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/frontend-tables-analysis.md

# 分析会员管理相关表
echo "### 2. 会员管理模块" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md
echo "前端需要管理会员信息、等级、积分等：" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md

MEMBER_TABLES=$(grep "^member_" /tmp/all_tables.txt 2>/dev/null || echo "")

if [ -n "$MEMBER_TABLES" ]; then
    echo "$MEMBER_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            IMPORTANCE=""
            case "$table_name" in
                "member_user") IMPORTANCE="🔴 核心 - 会员列表管理" ;;
                "member_level") IMPORTANCE="🟡 重要 - 会员等级配置" ;;
                "member_point_record") IMPORTANCE="🟡 重要 - 积分记录查询" ;;
                "member_sign_in_record") IMPORTANCE="🟢 一般 - 签到记录" ;;
                *) IMPORTANCE="🟢 一般" ;;
            esac
            
            echo "- **$table_name**: $TABLE_INFO $IMPORTANCE" >> docs/database/frontend-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/frontend-tables-analysis.md

# 分析支付管理相关表
echo "### 3. 支付管理模块" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md
echo "前端需要管理支付配置、订单、钱包等：" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md

PAY_TABLES=$(grep "^pay_" /tmp/all_tables.txt 2>/dev/null || echo "")

if [ -n "$PAY_TABLES" ]; then
    echo "$PAY_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            IMPORTANCE=""
            case "$table_name" in
                "pay_app") IMPORTANCE="🔴 核心 - 支付应用配置" ;;
                "pay_order") IMPORTANCE="🔴 核心 - 支付订单管理" ;;
                "pay_refund") IMPORTANCE="🟡 重要 - 退款管理" ;;
                "pay_wallet") IMPORTANCE="🟡 重要 - 钱包管理" ;;
                "pay_wallet_transaction") IMPORTANCE="🟡 重要 - 交易记录" ;;
                *) IMPORTANCE="🟢 一般" ;;
            esac
            
            echo "- **$table_name**: $TABLE_INFO $IMPORTANCE" >> docs/database/frontend-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/frontend-tables-analysis.md

# 分析业务管理相关表
echo "### 4. 业务管理模块" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md
echo "前端需要管理文章、球队、活动等业务数据：" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md

BUSINESS_TABLES=$(grep -E "^author_|^match_|^banner|^gold_" /tmp/all_tables.txt 2>/dev/null || echo "")

if [ -n "$BUSINESS_TABLES" ]; then
    echo "$BUSINESS_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            IMPORTANCE=""
            case "$table_name" in
                "author_article") IMPORTANCE="🔴 核心 - 文章管理" ;;
                "author_article_append") IMPORTANCE="🟡 重要 - 文章追加" ;;
                "match_team") IMPORTANCE="🟡 重要 - 球队管理" ;;
                "banner") IMPORTANCE="🟡 重要 - 轮播图管理" ;;
                "gold_order") IMPORTANCE="🟡 重要 - 鱼币充值管理" ;;
                *) IMPORTANCE="🟢 一般" ;;
            esac
            
            echo "- **$table_name**: $TABLE_INFO $IMPORTANCE" >> docs/database/frontend-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/frontend-tables-analysis.md

# 分析基础设施相关表
echo "### 5. 基础设施模块" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md
echo "前端需要的系统配置和工具：" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md

INFRA_TABLES=$(grep "^infra_" /tmp/all_tables.txt 2>/dev/null || echo "")

if [ -n "$INFRA_TABLES" ]; then
    echo "$INFRA_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            IMPORTANCE=""
            case "$table_name" in
                "infra_file") IMPORTANCE="🟡 重要 - 文件管理" ;;
                "infra_file_config") IMPORTANCE="🟡 重要 - 文件配置" ;;
                "infra_codegen_table") IMPORTANCE="🟢 一般 - 代码生成" ;;
                "infra_codegen_column") IMPORTANCE="🟢 一般 - 代码生成" ;;
                *) IMPORTANCE="🟢 一般" ;;
            esac
            
            echo "- **$table_name**: $TABLE_INFO $IMPORTANCE" >> docs/database/frontend-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/frontend-tables-analysis.md

# 分析微信管理相关表
echo "### 6. 微信管理模块" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md
echo "前端需要管理微信公众号相关功能：" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md

MP_TABLES=$(grep "^mp_" /tmp/all_tables.txt 2>/dev/null || echo "")

if [ -n "$MP_TABLES" ]; then
    echo "$MP_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            IMPORTANCE=""
            case "$table_name" in
                "mp_account") IMPORTANCE="🟡 重要 - 微信账号配置" ;;
                "mp_user") IMPORTANCE="🟡 重要 - 微信用户管理" ;;
                "mp_message") IMPORTANCE="🟢 一般 - 消息记录" ;;
                "mp_menu") IMPORTANCE="🟡 重要 - 菜单配置" ;;
                *) IMPORTANCE="🟢 一般" ;;
            esac
            
            echo "- **$table_name**: $TABLE_INFO $IMPORTANCE" >> docs/database/frontend-tables-analysis.md
        fi
    done
fi

echo "" >> docs/database/frontend-tables-analysis.md

# 生成前端核心表清单
echo "## 🎯 前端核心表清单" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md

echo "### 必须保留的表（前端核心功能）" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md

# 定义前端核心表
FRONTEND_CORE_TABLES=(
    "system_users"
    "system_role" 
    "system_menu"
    "system_tenant"
    "system_dept"
    "system_dict_type"
    "system_dict_data"
    "member_user"
    "member_level"
    "pay_app"
    "pay_order"
    "pay_wallet"
    "author_article"
    "infra_file"
    "infra_file_config"
)

echo '```' >> docs/database/frontend-tables-analysis.md
echo "# 前端管理后台核心表（不能删除）" >> docs/database/frontend-tables-analysis.md
for table in "${FRONTEND_CORE_TABLES[@]}"; do
    if grep -q "^$table$" /tmp/all_tables.txt; then
        echo "$table" >> docs/database/frontend-tables-analysis.md
    fi
done
echo '```' >> docs/database/frontend-tables-analysis.md

echo "" >> docs/database/frontend-tables-analysis.md

# 生成前端不使用的表
echo "## 🗑️ 前端不使用的表" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md

echo "### 可能可以删除的表（前端不直接使用）" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md

# 查找前端不使用的表
> /tmp/frontend_unused_tables.txt

while read table_name; do
    if [ -n "$table_name" ]; then
        # 检查是否是前端核心表
        IS_FRONTEND_CORE=0
        for core_table in "${FRONTEND_CORE_TABLES[@]}"; do
            if [ "$table_name" = "$core_table" ]; then
                IS_FRONTEND_CORE=1
                break
            fi
        done
        
        # 检查是否是demo表
        IS_DEMO=0
        if echo "$table_name" | grep -qiE "demo|test|sample|temp"; then
            IS_DEMO=1
        fi
        
        # 检查是否是日志表
        IS_LOG=0
        if echo "$table_name" | grep -qE "^infra_api|^infra_job|log"; then
            IS_LOG=1
        fi
        
        if [ $IS_FRONTEND_CORE -eq 0 ]; then
            if [ $IS_DEMO -eq 1 ]; then
                echo "$table_name|Demo/测试表" >> /tmp/frontend_unused_tables.txt
            elif [ $IS_LOG -eq 1 ]; then
                echo "$table_name|日志表" >> /tmp/frontend_unused_tables.txt
            else
                echo "$table_name|其他" >> /tmp/frontend_unused_tables.txt
            fi
        fi
    fi
done < /tmp/all_tables.txt

# 按类型显示不使用的表
if [ -s /tmp/frontend_unused_tables.txt ]; then
    echo "#### Demo/测试表（优先删除）" >> docs/database/frontend-tables-analysis.md
    grep "|Demo/测试表$" /tmp/frontend_unused_tables.txt | cut -d'|' -f1 | while read table; do
        TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        echo "- **$table**: $TABLE_INFO" >> docs/database/frontend-tables-analysis.md
    done
    
    echo "" >> docs/database/frontend-tables-analysis.md
    echo "#### 日志表（可考虑删除）" >> docs/database/frontend-tables-analysis.md
    grep "|日志表$" /tmp/frontend_unused_tables.txt | cut -d'|' -f1 | while read table; do
        TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        echo "- **$table**: $TABLE_INFO" >> docs/database/frontend-tables-analysis.md
    done
    
    echo "" >> docs/database/frontend-tables-analysis.md
    echo "#### 其他表（需确认）" >> docs/database/frontend-tables-analysis.md
    grep "|其他$" /tmp/frontend_unused_tables.txt | cut -d'|' -f1 | while read table; do
        TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
            --batch --skip-column-names 2>/dev/null)
        echo "- **$table**: $TABLE_INFO" >> docs/database/frontend-tables-analysis.md
    done
fi

echo "" >> docs/database/frontend-tables-analysis.md

# 统计信息
TOTAL_TABLES=$(wc -l < /tmp/all_tables.txt)
FRONTEND_CORE_COUNT=$(printf '%s\n' "${FRONTEND_CORE_TABLES[@]}" | wc -l)
UNUSED_COUNT=$(wc -l < /tmp/frontend_unused_tables.txt 2>/dev/null || echo "0")

echo "## 📊 统计信息" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md
echo "- **数据库总表数**: $TOTAL_TABLES" >> docs/database/frontend-tables-analysis.md
echo "- **前端核心表数**: $FRONTEND_CORE_COUNT" >> docs/database/frontend-tables-analysis.md
echo "- **前端不使用表数**: $UNUSED_COUNT" >> docs/database/frontend-tables-analysis.md
echo "- **前端使用率**: $(( FRONTEND_CORE_COUNT * 100 / TOTAL_TABLES ))%" >> docs/database/frontend-tables-analysis.md

echo "" >> docs/database/frontend-tables-analysis.md
echo "## ⚠️ 注意事项" >> docs/database/frontend-tables-analysis.md
echo "" >> docs/database/frontend-tables-analysis.md
echo "1. **前端核心表不能删除**: 这些表是后台管理系统正常运行的基础" >> docs/database/frontend-tables-analysis.md
echo "2. **Demo表可以安全删除**: 这些表只是框架示例，不影响业务功能" >> docs/database/frontend-tables-analysis.md
echo "3. **日志表可考虑删除**: 根据运维需求决定是否保留" >> docs/database/frontend-tables-analysis.md
echo "4. **其他表需要确认**: 可能被其他系统或定时任务使用" >> docs/database/frontend-tables-analysis.md

echo "" >> docs/database/frontend-tables-analysis.md
echo "---" >> docs/database/frontend-tables-analysis.md
echo "**生成时间**: $(date)" >> docs/database/frontend-tables-analysis.md
echo "**项目**: football-lottery-frontend (后台管理前端)" >> docs/database/frontend-tables-analysis.md

log_success "前端使用表分析完成: docs/database/frontend-tables-analysis.md"

# 生成前端核心表保护清单
cat > docs/database/frontend-core-tables.txt << 'EOF'
# 前端管理后台核心表清单
# 这些表是后台管理系统必须的，不能删除

EOF

echo "# 系统管理核心表" >> docs/database/frontend-core-tables.txt
for table in "${FRONTEND_CORE_TABLES[@]}"; do
    if grep -q "^$table$" /tmp/all_tables.txt; then
        echo "$table" >> docs/database/frontend-core-tables.txt
    fi
done

log_success "前端核心表清单已生成: docs/database/frontend-core-tables.txt"

# 清理临时文件
rm -f /tmp/all_tables.txt /tmp/frontend_unused_tables.txt

echo
echo "========================================"
echo "前端使用表分析完成！"
echo "========================================"
echo "📋 docs/database/frontend-tables-analysis.md - 详细分析报告"
echo "📝 docs/database/frontend-core-tables.txt - 核心表清单"
echo "📊 前端核心表数: $FRONTEND_CORE_COUNT"
echo "📊 前端不使用表数: $UNUSED_COUNT"
echo "========================================"
