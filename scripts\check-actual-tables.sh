#!/bin/bash

# =============================================
# 检查数据库中实际存在的表
# 用于确认哪些表需要添加tenant_id字段
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "========================================"
echo "检查数据库中实际存在的表"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

echo
echo "=== 数据库中所有的表 ==="

# 获取所有表名
ALL_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null)

echo "数据库中的所有表："
echo "$ALL_TABLES" | while read table; do
    echo "  📋 $table"
done

TOTAL_TABLES=$(echo "$ALL_TABLES" | wc -l)
echo "总计: $TOTAL_TABLES 个表"

echo
echo "=== 按模块分类的表 ==="

# 会员模块
echo "🧑‍🤝‍🧑 会员模块相关表："
echo "$ALL_TABLES" | grep -E "^member" | while read table; do
    echo "  📋 $table"
done

# 支付模块
echo
echo "💰 支付模块相关表："
echo "$ALL_TABLES" | grep -E "^pay" | while read table; do
    echo "  📋 $table"
done

# 系统模块
echo
echo "⚙️ 系统模块相关表："
echo "$ALL_TABLES" | grep -E "^system" | while read table; do
    echo "  📋 $table"
done

# 基础设施模块
echo
echo "🏗️ 基础设施模块相关表："
echo "$ALL_TABLES" | grep -E "^infra" | while read table; do
    echo "  📋 $table"
done

# 微信模块
echo
echo "💬 微信模块相关表："
echo "$ALL_TABLES" | grep -E "^mp" | while read table; do
    echo "  📋 $table"
done

# 业务相关表
echo
echo "📝 业务相关表："
echo "$ALL_TABLES" | grep -E "^author|^article|^match|^banner|^gold" | while read table; do
    echo "  📋 $table"
done

# 其他表
echo
echo "📦 其他表："
echo "$ALL_TABLES" | grep -vE "^member|^pay|^system|^infra|^mp|^author|^article|^match|^banner|^gold" | while read table; do
    echo "  📋 $table"
done

echo
echo "=== 需要添加tenant_id字段的表建议 ==="

# 定义需要多租户支持的表模式
TENANT_PATTERNS=("^member" "^pay" "^author" "^match" "^banner" "^gold" "^infra_file" "^infra_codegen" "^mp")

echo "建议添加tenant_id字段的表："
for pattern in "${TENANT_PATTERNS[@]}"; do
    MATCHING_TABLES=$(echo "$ALL_TABLES" | grep -E "$pattern")
    if [ -n "$MATCHING_TABLES" ]; then
        echo "$MATCHING_TABLES" | while read table; do
            # 检查是否已有tenant_id字段
            HAS_TENANT_ID=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table' AND COLUMN_NAME = 'tenant_id';" \
                --batch --skip-column-names 2>/dev/null)
            
            if [ "$HAS_TENANT_ID" = "1" ]; then
                echo "  ✅ $table (已有tenant_id字段)"
            else
                echo "  ⚠️  $table (需要添加tenant_id字段)"
            fi
        done
    fi
done

echo
echo "=== 不需要tenant_id字段的表 ==="
echo "以下表通常不需要租户隔离："

# 系统核心表
SYSTEM_CORE_TABLES=$(echo "$ALL_TABLES" | grep -E "^system_tenant|^system_dict|^system_menu|^system_error|^system_sms|^system_oauth2|^system_sensitive")
if [ -n "$SYSTEM_CORE_TABLES" ]; then
    echo "系统核心表（不需要租户隔离）："
    echo "$SYSTEM_CORE_TABLES" | while read table; do
        echo "  🔒 $table"
    done
fi

# 基础设施日志表
INFRA_LOG_TABLES=$(echo "$ALL_TABLES" | grep -E "^infra_api|^infra_job")
if [ -n "$INFRA_LOG_TABLES" ]; then
    echo "基础设施日志表（不需要租户隔离）："
    echo "$INFRA_LOG_TABLES" | while read table; do
        echo "  📊 $table"
    done
fi

echo
echo "========================================"
echo "表检查完成"
echo "========================================"
