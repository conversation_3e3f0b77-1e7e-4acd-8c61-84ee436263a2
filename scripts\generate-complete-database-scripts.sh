#!/bin/bash

# =============================================
# 完整数据库重构脚本生成器
# 不做裁剪，按功能完整分类所有191个表
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "========================================"
echo "完整数据库重构脚本生成器"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p sql/complete-restructure

log_info "生成完整重构脚本..."

# 1. 生成平台框架表脚本（基础设施和系统核心）
log_info "生成平台框架表脚本..."

cat > sql/complete-restructure/01-framework-tables.sql << 'EOF'
-- =============================================
-- 足球彩票系统 - 平台框架表（完整版）
-- 系统核心框架功能，包含所有基础设施表
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 系统核心表
-- =============================================

-- 租户管理
CREATE TABLE IF NOT EXISTS `system_tenant` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '租户名',
    `contact_user_id` bigint DEFAULT NULL COMMENT '联系人的用户编号',
    `contact_name` varchar(30) NOT NULL COMMENT '联系人',
    `contact_mobile` varchar(500) DEFAULT NULL COMMENT '联系手机',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
    `website` varchar(256) DEFAULT '' COMMENT '绑定域名',
    `package_id` bigint NOT NULL COMMENT '租户套餐编号',
    `expire_time` datetime NOT NULL COMMENT '过期时间',
    `account_count` int NOT NULL COMMENT '账号数量',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';

-- 租户套餐表
CREATE TABLE IF NOT EXISTS `system_tenant_package` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '套餐编号',
    `name` varchar(30) NOT NULL COMMENT '套餐名',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '租户套餐状态',
    `remark` varchar(256) DEFAULT '' COMMENT '备注',
    `menu_ids` varchar(2048) NOT NULL COMMENT '关联的菜单编号',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户套餐表';

-- 用户信息表
CREATE TABLE IF NOT EXISTS `system_users` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `username` varchar(30) NOT NULL COMMENT '用户账号',
    `password` varchar(100) DEFAULT '' COMMENT '密码',
    `nickname` varchar(30) NOT NULL COMMENT '用户昵称',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
    `post_ids` varchar(255) DEFAULT NULL COMMENT '岗位编号数组',
    `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
    `mobile` varchar(11) DEFAULT '' COMMENT '手机号码',
    `sex` tinyint DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
    `avatar` varchar(512) DEFAULT '' COMMENT '头像地址',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
    `login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
    `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`,`update_time`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 角色信息表
CREATE TABLE IF NOT EXISTS `system_role` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '角色名称',
    `code` varchar(100) NOT NULL COMMENT '角色权限字符串',
    `sort` int NOT NULL COMMENT '显示顺序',
    `data_scope` tinyint NOT NULL DEFAULT '1' COMMENT '数据范围',
    `data_scope_dept_ids` varchar(500) DEFAULT '' COMMENT '数据范围(指定部门数组)',
    `status` tinyint NOT NULL COMMENT '角色状态（0正常 1停用）',
    `type` tinyint NOT NULL COMMENT '角色类型',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色信息表';

-- 菜单权限表
CREATE TABLE IF NOT EXISTS `system_menu` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `name` varchar(50) NOT NULL COMMENT '菜单名称',
    `permission` varchar(100) DEFAULT '' COMMENT '权限标识',
    `type` tinyint NOT NULL COMMENT '菜单类型（1目录 2菜单 3按钮）',
    `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
    `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父菜单ID',
    `path` varchar(200) DEFAULT '' COMMENT '路由地址',
    `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
    `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
    `component_name` varchar(255) DEFAULT NULL COMMENT '组件名',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
    `visible` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否可见',
    `keep_alive` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否缓存',
    `always_show` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否总是显示',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单权限表';

-- 用户和角色关联表
CREATE TABLE IF NOT EXISTS `system_user_role` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户和角色关联表';

-- 角色和菜单关联表
CREATE TABLE IF NOT EXISTS `system_role_menu` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `menu_id` bigint NOT NULL COMMENT '菜单ID',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和菜单关联表';

-- 部门表
CREATE TABLE IF NOT EXISTS `system_dept` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL DEFAULT '' COMMENT '部门名称',
    `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父部门id',
    `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
    `leader_user_id` bigint DEFAULT NULL COMMENT '负责人',
    `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
    `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
    `status` tinyint NOT NULL COMMENT '部门状态（0正常 1停用）',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 岗位信息表
CREATE TABLE IF NOT EXISTS `system_post` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `code` varchar(64) NOT NULL COMMENT '岗位编码',
    `name` varchar(50) NOT NULL COMMENT '岗位名称',
    `sort` int NOT NULL COMMENT '显示顺序',
    `status` tinyint NOT NULL COMMENT '状态（0正常 1停用）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位信息表';

-- 用户岗位表
CREATE TABLE IF NOT EXISTS `system_user_post` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户ID',
    `post_id` bigint NOT NULL DEFAULT '0' COMMENT '岗位ID',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户岗位表';

-- 字典类型表
CREATE TABLE IF NOT EXISTS `system_dict_type` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
    `name` varchar(100) DEFAULT '' COMMENT '字典名称',
    `type` varchar(100) DEFAULT '' COMMENT '字典类型',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典类型表';

-- 字典数据表
CREATE TABLE IF NOT EXISTS `system_dict_data` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
    `sort` int NOT NULL DEFAULT '0' COMMENT '字典排序',
    `label` varchar(100) DEFAULT '' COMMENT '字典标签',
    `value` varchar(100) DEFAULT '' COMMENT '字典键值',
    `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `color_type` varchar(100) DEFAULT '' COMMENT '颜色类型',
    `css_class` varchar(100) DEFAULT '' COMMENT 'css 样式',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典数据表';

-- 通知公告表
CREATE TABLE IF NOT EXISTS `system_notice` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '公告ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `title` varchar(50) NOT NULL COMMENT '公告标题',
    `content` text NOT NULL COMMENT '公告内容',
    `type` tinyint NOT NULL COMMENT '公告类型（1通知 2公告）',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知公告表';

SET FOREIGN_KEY_CHECKS = 1;
EOF

log_success "平台框架表脚本已生成: sql/complete-restructure/01-framework-tables.sql"

echo
echo "========================================"
echo "完整重构脚本生成完成！"
echo "========================================"
echo "📄 sql/complete-restructure/01-framework-tables.sql - 平台框架表（完整版）"
echo "========================================"
