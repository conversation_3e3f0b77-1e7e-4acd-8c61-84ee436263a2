# 数据库表分析报告

## 📊 数据库概览

- **数据库名称**: sports_gaming
- **总表数**: 190
- **数据库大小**: 391.13MB
- **分析时间**: Thu Jul 10 19:58:31     2025
- **分析工具**: 通用数据库表分析脚本

## 🔍 分析方法

本报告通过以下方式自动分析数据库表结构：

1. **前缀分析**: 基于表名前缀进行智能分类
2. **模式识别**: 识别常见的数据库设计模式
3. **功能推断**: 根据表名和字段推断功能用途
4. **关系分析**: 分析表之间的关联关系

## 📋 表分类结果

### 1. 未分类模块 (account_*)

**表数量**: 2

- **account_statistic**:  (54行)
- **account_users**: 公众号用户信息 (32行)

### 2. 内容管理模块 (article_*)

**表数量**: 1

- **article_push_config**: 文章推送设置 (62行)

### 3. 未分类模块 (author_*)

**表数量**: 12

- **author_accomplishment**: 作者战绩 (29行)
- **author_article**: 文章 (600行)
- **author_article_append**: 文章 (15行)
- **author_article_pv_logs**: 文章PV记录 (11268行)
- **author_audit**: 作者审核列表 (18行)
- **author_audit_copy1**: 作者审核列表 (9行)
- **author_commission_rate**: 作者分成提现设置表 (3行)
- **author_day_report**: 作者日报 (2654行)
- **author_hit_rate**: 作者命中率记录表 (33行)
- **author_info**:  (5行)
- **author_privilege_set**: 作者特权设置 (63行)
- **author_withdraw_logs**: 作者提现表 (33行)

### 4. 未分类模块 (broomer_*)

**表数量**: 1

- **broomer_match_scheme**: 扫盘师方案 (5行)

### 5. 未分类模块 (football_*)

**表数量**: 14

- **football_bd_odds**: 北单指数 (3283行)
- **football_bd_result**: 北单开奖结果 (4605行)
- **football_bd_sf_odds**: 北单胜负过关指数 (3774行)
- **football_bd_sf_result**: 北单胜负过关结果 (5398行)
- **football_bo_live_odds**: 实时指数 (198行)
- **football_company**: 公司列表 (494040行)
- **football_half_live_odds**: 半场实时指数 (304行)
- **football_issue**: 足球期数表 (0行)
- **football_jc_odds**: 竞彩比赛指数 (947行)
- **football_jc_result**: 竞彩比赛结果 (1174行)
- **football_live_odds**: 实时指数 (334647行)
- **football_match**: 传统足彩比赛列表 (1092行)
- **football_match_result**: 传统足彩开奖结果 (0行)
- **football_tc_match**: 体彩比赛关联列表 (5485行)

### 6. 未分类模块 (gold_*)

**表数量**: 1

- **gold_order**: 金币订单表 (183行)

### 7. 未分类模块 (guess_*)

**表数量**: 1

- **guess_records**: 用户竞猜记录表 (0行)

### 8. 未分类模块 (home_*)

**表数量**: 1

- **home_banner**: 首页banner (18行)

### 9. 基础设施模块 (infra_*)

**表数量**: 11

- **infra_api_access_log**: API 访问日志表 (299703行)
- **infra_api_error_log**: 系统异常日志 (1254行)
- **infra_codegen_column**: 代码生成表字段定义 (396行)
- **infra_codegen_table**: 代码生成表定义 (38行)
- **infra_config**: 参数配置表 (7行)
- **infra_data_source_config**: 数据源配置表 (0行)
- **infra_file**: 文件表 (16行)
- **infra_file_config**: 文件配置表 (3行)
- **infra_file_content**: 文件表 (0行)
- **infra_job**: 定时任务表 (24行)
- **infra_job_log**: 定时任务日志表 (243412行)

### 10. 未分类模块 (match_*)

**表数量**: 19

- **match_category**: 赛事分类 (8行)
- **match_coach**: 教练 (127695行)
- **match_competition**: 赛事信息 (2325行)
- **match_country**: 国家 (212行)
- **match_future_record**: 未来赛程 (6032行)
- **match_history**: 比赛记录及盘口信息 (9138行)
- **match_lineup_detail**: 赛事阵容详情 (2517行)
- **match_list**: 比赛场次信息 (61651行)
- **match_live_info**: 比赛直播信息 (100行)
- **match_odds**: 比赛开盘信息 (42行)
- **match_player**: 球员 (0行)
- **match_player_info**: 比赛球队球员信息 (159行)
- **match_player_transfer**: 球队转会信息 (2928行)
- **match_point_rank**: 联赛积分排名 (4032行)
- **match_referee**: 裁判表 (4144行)
- **match_season**: 赛季信息 (0行)
- **match_stage**: 赛事阶段表 (38753行)
- **match_stats**: 比赛数据统计表 (7084行)
- **match_team**: 球队信息 (70924行)

### 11. 用户管理模块 (member_*)

**表数量**: 23

- **member_address**:  (0行)
- **member_attention**: 用户关注表 (63行)
- **member_author_privilege**: 用户作者特权 (49行)
- **member_bind_record**: 会员绑定记录表 (0行)
- **member_config**:  (0行)
- **member_experience_record**:  (0行)
- **member_group**:  (0行)
- **member_level**:  (0行)
- **member_level_record**:  (0行)
- **member_match_attention**: 用户关注比赛信息 (14行)
- **member_match_chat**: 比赛场次聊天记录 (3行)
- **member_match_vote**: 比赛场次投票 (0行)
- **member_point_record**:  (0行)
- **member_privilege_log**: 用户特权使用记录 (11行)
- **member_settlement_info**: 用户提现账号信息 (10行)
- **member_sign_in_config**:  (0行)
- **member_sign_in_record**:  (0行)
- **member_tag**:  (0行)
- **member_user**: 用户端-用户表 (45行)
- **member_user_bak**:  (0行)
- **member_user_balance_logs**: 用户余额变更记录 (303行)
- **member_user_ex_balance_logs**: 用户推广余额变更记录 (27行)
- **member_user_gold_logs**: 用户金币变更记录 (345行)

### 12. 微信集成模块 (mp_*)

**表数量**: 12

- **mp_account**: 公众号账号表 (7行)
- **mp_auto_reply**: 公众号消息自动回复表 (0行)
- **mp_click_logs**: 公众号关注点击记录了表 (6行)
- **mp_material**: 公众号素材表 (0行)
- **mp_menu**: 公众号菜单表 (4行)
- **mp_message**: 公众号消息表  (23行)
- **mp_mini_user**: 小程序用户表 (7行)
- **mp_other_even_logs**: 1w1公众扫码记录 (0行)
- **mp_pay_config_log**: 微信配置记录日志 (4行)
- **mp_tag**: 公众号标签表 (0行)
- **mp_template_config**: 微信模板配置表 (9行)
- **mp_user**: 公众号粉丝表 (25行)

### 13. 未分类模块 (partner_*)

**表数量**: 7

- **partner_audit**: 作者审核列表 (11行)
- **partner_commission_rate**: 作者分成提现设置表 (2行)
- **partner_divide_config**: 合伙人分成比例配置 (4行)
- **partner_info**: 合作伙伴信息 (5行)
- **partner_invite_logs**: 合作伙伴邀请记录表 (3行)
- **partner_settlement_info**: 用户提现账号信息 (8行)
- **partner_withdraw_logs**: 作者提现表 (9行)

### 14. 交易支付模块 (pay_*)

**表数量**: 15

- **pay_app**:  (0行)
- **pay_bank_info**: 银行信息 (4行)
- **pay_channel**:  (0行)
- **pay_demo_order**:  (0行)
- **pay_demo_transfer**:  (0行)
- **pay_notify_log**:  (0行)
- **pay_notify_task**:  (0行)
- **pay_order**:  (0行)
- **pay_order_extension**:  (0行)
- **pay_refund**:  (0行)
- **pay_transfer**:  (0行)
- **pay_wallet**:  (0行)
- **pay_wallet_recharge**:  (0行)
- **pay_wallet_recharge_package**:  (2行)
- **pay_wallet_transaction**:  (0行)

### 15. 未分类模块 (play_*)

**表数量**: 1

- **play_type**: 玩法类型 (6行)

### 16. 未分类模块 (playing_*)

**表数量**: 1

- **playing_method**: 玩法 (7行)

### 17. 未分类模块 (privilege_*)

**表数量**: 1

- **privilege_order**: 方案订单表 (82行)

### 18. 未分类模块 (push_*)

**表数量**: 1

- **push_amount_config**: 平台配置 (0行)

### 19. 任务调度模块 (qrtz_*)

**表数量**: 11

- **qrtz_blob_triggers**:  (0行)
- **qrtz_calendars**:  (0行)
- **qrtz_cron_triggers**:  (13行)
- **qrtz_fired_triggers**:  (0行)
- **qrtz_job_details**:  (13行)
- **qrtz_locks**:  (2行)
- **qrtz_paused_trigger_grps**:  (0行)
- **qrtz_scheduler_state**:  (2行)
- **qrtz_simple_triggers**:  (0行)
- **qrtz_simprop_triggers**:  (0行)
- **qrtz_triggers**:  (13行)

### 20. 未分类模块 (recommend_*)

**表数量**: 2

- **recommend_author**: 作者推荐列表 (6行)
- **recommend_user_register_logs**: 文章推荐用户注册记录 (0行)

### 21. 未分类模块 (refund_*)

**表数量**: 1

- **refund_order**: 支付退款订单 (27行)

### 22. 未分类模块 (report_*)

**表数量**: 1

- **report_operation_day**: 每日运营日报 (16行)

### 23. 未分类模块 (scheme_*)

**表数量**: 2

- **scheme_order**: 方案订单表 (292行)
- **scheme_play**: 方案管理 (8行)

### 24. 系统管理模块 (sys_*)

**表数量**: 1

- **sys_configs**: 系统配置表 (0行)

### 25. 系统管理模块 (system_*)

**表数量**: 34

- **system_command**: 关键词与接口关联表 (0行)
- **system_dept**: 部门表 (17行)
- **system_dict_data**: 字典数据表 (451行)
- **system_dict_type**: 字典类型表 (101行)
- **system_login_log**: 系统访问记录 (828行)
- **system_mail_account**: 邮箱账号表 (4行)
- **system_mail_log**: 邮件日志表 (0行)
- **system_mail_template**: 邮件模版表 (3行)
- **system_menu**: 菜单权限表 (887行)
- **system_notice**: 通知公告表 (3行)
- **system_notify_message**: 站内信消息表 (9行)
- **system_notify_template**: 站内信模板表 (0行)
- **system_oauth2_access_token**: OAuth2 访问令牌 (3250行)
- **system_oauth2_approve**: OAuth2 批准表 (0行)
- **system_oauth2_client**: OAuth2 客户端表 (4行)
- **system_oauth2_code**: OAuth2 授权码表 (0行)
- **system_oauth2_refresh_token**: OAuth2 刷新令牌 (1748行)
- **system_operate_log**: 操作日志记录 V2 版本 (0行)
- **system_post**: 岗位信息表 (5行)
- **system_role**: 角色信息表 (10行)
- **system_role_menu**: 角色和菜单关联表 (1510行)
- **system_sensitive_word**: 敏感词 (0行)
- **system_sms_channel**: 短信渠道 (0行)
- **system_sms_code**: 手机验证码 (11行)
- **system_sms_log**: 短信日志 (5行)
- **system_sms_template**: 短信模板 (13行)
- **system_social_client**: 社交客户端表 (5行)
- **system_social_user**: 社交用户表 (0行)
- **system_social_user_bind**: 社交绑定表 (0行)
- **system_tenant**: 租户表 (3行)
- **system_tenant_package**: 租户套餐表 (3行)
- **system_user_post**: 用户岗位表 (14行)
- **system_user_role**: 用户和角色关联表 (24行)
- **system_users**: 用户信息表 (22行)

### 26. 未分类模块 (third_*)

**表数量**: 2

- **third_pay_channel**: 第三方支付渠道设置 (5行)
- **third_pay_sqb_config**: 收钱吧支付账号表 (3行)

### 27. 未分类模块 (wecom_*)

**表数量**: 1

- **wecom_setting**: 企业微信配置 (0行)

### 28. 微信集成模块 (wx_*)

**表数量**: 3

- **wx_external_contact**: 企微客服客户信息对照表 (9287行)
- **wx_external_contact_way_config**:  (0行)
- **wx_work_setting**: 企微号配置 (2行)

### 29. 未分类模块 (yudao_*)

**表数量**: 5

- **yudao_demo01_contact**: 示例联系人表 (0行)
- **yudao_demo02_category**: 示例分类表 (6行)
- **yudao_demo03_course**: 学生课程表 (10行)
- **yudao_demo03_grade**: 学生班级表 (3行)
- **yudao_demo03_student**: 学生表 (3行)

### 30. 单表模块

**说明**: 没有明确前缀的独立表

- **authors**: 作者表 (0行)
- **broomers**: 扫盘师列表 (3行)
- **matches**: 竞猜赛事表 (0行)

## 📊 统计信息

| 指标 | 数值 |
|------|------|
| 总表数 | 190 |
| 数据库大小 | 391.13MB |
| 有前缀的表 | 187 |
| 无前缀的表 | 3 |
| 前缀类型数 | 29 |

## 🎯 建议

### 数据库设计建议
1. **命名规范**: 建议使用统一的表名前缀来标识功能模块
2. **注释完善**: 为表和字段添加清晰的注释说明
3. **模块化**: 按功能模块组织表结构，便于维护

### 重构建议
1. **模块分离**: 可以考虑按前缀将表分组到不同的SQL文件中
2. **清理优化**: 检查是否有废弃的表可以清理
3. **索引优化**: 检查表的索引设计是否合理

---

**分析完成时间**: Thu Jul 10 19:59:48     2025
**工具版本**: 通用数据库表分析脚本 v1.0
