#!/bin/bash

# =============================================
# 手动ER图生成脚本
# 使用最简单的方法，避免复杂SQL查询
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "========================================"
echo "手动ER图生成工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database

log_info "获取表列表..."

# 使用最简单的方法获取表列表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

if [ $? -ne 0 ]; then
    echo "❌ 获取表列表失败"
    exit 1
fi

TOTAL_TABLES=$(wc -l < /tmp/all_tables.txt)
log_success "发现 $TOTAL_TABLES 个表"

log_info "生成基于实际表的ER图..."

# 生成ER图
cat > docs/database/er-diagram-manual.mmd << 'EOF'
erDiagram
    %% 足球彩票系统数据库ER图
    %% 基于实际数据库表生成
    
    %% 系统租户表
    system_tenant {
        bigint id PK "租户ID"
        varchar name "租户名称"
        varchar contact_name "联系人"
        varchar contact_mobile "联系电话"
        tinyint status "状态"
        datetime expire_time "过期时间"
        datetime create_time "创建时间"
    }
    
EOF

# 检查并添加会员模块表
if grep -q "^member_user$" /tmp/all_tables.txt; then
    cat >> docs/database/er-diagram-manual.mmd << 'EOF'
    %% 会员模块
    member_user {
        bigint id PK "用户ID"
        bigint tenant_id "租户ID"
        varchar username "用户名"
        varchar mobile "手机号"
        varchar nickname "昵称"
        decimal gold "鱼币"
        decimal balance "余额"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
EOF
fi

if grep -q "^member_level$" /tmp/all_tables.txt; then
    cat >> docs/database/er-diagram-manual.mmd << 'EOF'
    member_level {
        bigint id PK "等级ID"
        bigint tenant_id "租户ID"
        varchar name "等级名称"
        int level "等级值"
        decimal discount_percent "折扣比例"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
EOF
fi

# 检查并添加支付模块表
if grep -q "^pay_order$" /tmp/all_tables.txt; then
    cat >> docs/database/er-diagram-manual.mmd << 'EOF'
    %% 支付模块
    pay_order {
        bigint id PK "订单ID"
        bigint tenant_id "租户ID"
        bigint app_id "应用ID"
        varchar merchant_order_id "商户订单号"
        varchar subject "订单标题"
        int price "支付金额(分)"
        tinyint status "订单状态"
        datetime success_time "支付成功时间"
        datetime create_time "创建时间"
    }
    
EOF
fi

if grep -q "^pay_wallet$" /tmp/all_tables.txt; then
    cat >> docs/database/er-diagram-manual.mmd << 'EOF'
    pay_wallet {
        bigint id PK "钱包ID"
        bigint tenant_id "租户ID"
        bigint user_id "用户ID"
        varchar user_type "用户类型"
        int balance "余额(分)"
        int total_expense "总支出"
        int total_recharge "总充值"
        datetime create_time "创建时间"
    }
    
EOF
fi

if grep -q "^pay_app$" /tmp/all_tables.txt; then
    cat >> docs/database/er-diagram-manual.mmd << 'EOF'
    pay_app {
        bigint id PK "应用ID"
        bigint tenant_id "租户ID"
        varchar name "应用名称"
        tinyint status "状态"
        varchar pay_notify_url "支付回调地址"
        varchar refund_notify_url "退款回调地址"
        datetime create_time "创建时间"
    }
    
EOF
fi

# 检查并添加业务模块表
if grep -q "^author_article$" /tmp/all_tables.txt; then
    cat >> docs/database/er-diagram-manual.mmd << 'EOF'
    %% 业务模块
    author_article {
        bigint id PK "文章ID"
        bigint tenant_id "租户ID"
        bigint author_id "作者ID"
        varchar title "标题"
        text intro "简介"
        text free_contents "免费内容"
        text contents "付费内容"
        decimal price "价格"
        tinyint status "状态"
        datetime start_time "开始时间"
        datetime create_time "创建时间"
    }
    
EOF
fi

if grep -q "^author_article_append$" /tmp/all_tables.txt; then
    cat >> docs/database/er-diagram-manual.mmd << 'EOF'
    author_article_append {
        bigint id PK "追加ID"
        bigint tenant_id "租户ID"
        bigint article_id "文章ID"
        text content "追加内容"
        datetime create_time "创建时间"
    }
    
EOF
fi

# 检查并添加系统管理表
if grep -q "^system_users$" /tmp/all_tables.txt; then
    cat >> docs/database/er-diagram-manual.mmd << 'EOF'
    %% 系统管理
    system_users {
        bigint id PK "用户ID"
        bigint tenant_id "租户ID"
        varchar username "用户名"
        varchar nickname "昵称"
        varchar mobile "手机号"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
EOF
fi

# 添加表关系
cat >> docs/database/er-diagram-manual.mmd << 'EOF'
    %% 表关系定义
EOF

# 根据实际存在的表添加关系
if grep -q "^system_tenant$" /tmp/all_tables.txt && grep -q "^member_user$" /tmp/all_tables.txt; then
    echo "    system_tenant ||--o{ member_user : \"tenant_id\"" >> docs/database/er-diagram-manual.mmd
fi

if grep -q "^system_tenant$" /tmp/all_tables.txt && grep -q "^pay_order$" /tmp/all_tables.txt; then
    echo "    system_tenant ||--o{ pay_order : \"tenant_id\"" >> docs/database/er-diagram-manual.mmd
fi

if grep -q "^system_tenant$" /tmp/all_tables.txt && grep -q "^pay_wallet$" /tmp/all_tables.txt; then
    echo "    system_tenant ||--o{ pay_wallet : \"tenant_id\"" >> docs/database/er-diagram-manual.mmd
fi

if grep -q "^system_tenant$" /tmp/all_tables.txt && grep -q "^author_article$" /tmp/all_tables.txt; then
    echo "    system_tenant ||--o{ author_article : \"tenant_id\"" >> docs/database/er-diagram-manual.mmd
fi

if grep -q "^member_user$" /tmp/all_tables.txt && grep -q "^pay_wallet$" /tmp/all_tables.txt; then
    echo "    member_user ||--|| pay_wallet : \"user_id\"" >> docs/database/er-diagram-manual.mmd
fi

if grep -q "^member_user$" /tmp/all_tables.txt && grep -q "^pay_order$" /tmp/all_tables.txt; then
    echo "    member_user ||--o{ pay_order : \"user_id\"" >> docs/database/er-diagram-manual.mmd
fi

if grep -q "^member_user$" /tmp/all_tables.txt && grep -q "^author_article$" /tmp/all_tables.txt; then
    echo "    member_user ||--o{ author_article : \"author_id\"" >> docs/database/er-diagram-manual.mmd
fi

if grep -q "^pay_app$" /tmp/all_tables.txt && grep -q "^pay_order$" /tmp/all_tables.txt; then
    echo "    pay_app ||--o{ pay_order : \"app_id\"" >> docs/database/er-diagram-manual.mmd
fi

if grep -q "^author_article$" /tmp/all_tables.txt && grep -q "^author_article_append$" /tmp/all_tables.txt; then
    echo "    author_article ||--o{ author_article_append : \"article_id\"" >> docs/database/er-diagram-manual.mmd
fi

log_success "ER图已生成: docs/database/er-diagram-manual.mmd"

# 生成表统计
log_info "生成表统计信息..."

cat > docs/database/table-list.txt << 'EOF'
=== 数据库表列表 ===

EOF

echo "总表数: $TOTAL_TABLES" >> docs/database/table-list.txt
echo "" >> docs/database/table-list.txt

# 按模块分类
echo "=== 按模块分类 ===" >> docs/database/table-list.txt
echo "" >> docs/database/table-list.txt

echo "会员模块表:" >> docs/database/table-list.txt
grep "^member_" /tmp/all_tables.txt | while read table; do
    echo "  - $table" >> docs/database/table-list.txt
done
echo "" >> docs/database/table-list.txt

echo "支付模块表:" >> docs/database/table-list.txt
grep "^pay_" /tmp/all_tables.txt | while read table; do
    echo "  - $table" >> docs/database/table-list.txt
done
echo "" >> docs/database/table-list.txt

echo "系统模块表:" >> docs/database/table-list.txt
grep "^system_" /tmp/all_tables.txt | while read table; do
    echo "  - $table" >> docs/database/table-list.txt
done
echo "" >> docs/database/table-list.txt

echo "业务模块表:" >> docs/database/table-list.txt
grep -E "^author_|^match_|^banner|^gold_" /tmp/all_tables.txt | while read table; do
    echo "  - $table" >> docs/database/table-list.txt
done
echo "" >> docs/database/table-list.txt

echo "基础设施表:" >> docs/database/table-list.txt
grep "^infra_" /tmp/all_tables.txt | while read table; do
    echo "  - $table" >> docs/database/table-list.txt
done
echo "" >> docs/database/table-list.txt

echo "微信模块表:" >> docs/database/table-list.txt
grep "^mp_" /tmp/all_tables.txt | while read table; do
    echo "  - $table" >> docs/database/table-list.txt
done
echo "" >> docs/database/table-list.txt

echo "其他表:" >> docs/database/table-list.txt
grep -vE "^member_|^pay_|^system_|^author_|^match_|^banner|^gold_|^infra_|^mp_" /tmp/all_tables.txt | while read table; do
    echo "  - $table" >> docs/database/table-list.txt
done

log_success "表统计已生成: docs/database/table-list.txt"

# 清理临时文件
rm -f /tmp/all_tables.txt

echo
echo "========================================"
echo "手动ER图生成完成！"
echo "========================================"
echo "🎨 docs/database/er-diagram-manual.mmd - 手动生成的ER图"
echo "📋 docs/database/table-list.txt - 表列表和统计"
echo "========================================"
echo
echo "查看ER图："
echo "1. 访问 https://mermaid.live/ 粘贴内容"
echo "2. 在VSCode中安装Mermaid Preview插件"

log_success "手动ER图生成完成！"
