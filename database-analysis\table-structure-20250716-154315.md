# 数据库表结构详情

## 📊 数据库: sports_gaming

**生成时间**: 2025年07月16日 15:44:32

### 表: account_statistic

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| day | date | ❌ |  |  |  |
| account_id | bigint(20) | ❌ |  |  |  |
| user_id | bigint(20) | ❌ |  |  |  |
| article_num | int(11) | ❌ |  | 发布文章数量 |  |
| read_num | int(11) | ❌ |  | 阅读人数 |  |
| read_percent | decimal(10,2) | ❌ |  | 阅读率 |  |
| total_pay_num | int(11) | ❌ |  | 总付费人数 |  |
| total_pay_percent | decimal(10,2) | ❌ |  | 总付费率 |  |
| new_pay_num | int(11) | ❌ |  | 新人付费人数 |  |
| new_pay_percent | decimal(10,2) | ❌ |  | 新人付费率 |  |
| new_pay_price_avg | decimal(10,2) | ❌ |  | 新人客单价 |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: account_users

**表说明**: 公众号用户信息

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| account_id | bigint(20) | ❌ |  |  |  |
| app_id | varchar(255) | ❌ |  |  |  |
| open_id | varchar(255) | ❌ | 📇 FK | 用户openid |  |
| union_id | varchar(255) | ❌ | 📇 FK | 用户unionid |  |
| subscribe_time | datetime | ❌ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

**索引信息**:
- openid
- unionid

---

### 表: article_push_config

**表说明**: 文章推送设置

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| article_id | bigint(20) | ❌ |  |  |  |
| author_id | bigint(20) | ❌ |  |  |  |
| consume_status | int(10) | ❌ |  | 消费状态 0 全部 -1 不推送 1 有条件推送 |  |
| consume_min_num | int(11) | ✅ |  |  |  |
| consume_max_num | int(11) | ✅ |  |  |  |
| consume_min_amount | decimal(10,2) | ✅ |  |  |  |
| consume_max_amount | decimal(10,2) | ✅ |  |  |  |
| push_time | datetime | ❌ |  |  |  |
| template_id | varchar(255) | ✅ |  |  |  |
| idx | int(3) | ✅ |  | 标识位 |  |

---

### 表: author_accomplishment

**表说明**: 作者战绩

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| author_id | bigint(20) | ❌ |  |  |  |
| show_data | mediumtext | ❌ |  |  |  |
| show_pic | varchar(255) | ✅ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: author_article

**表说明**: 文章

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK | ID |  |
| author_id | bigint(20) | ❌ | 📇 FK | 作者ID |  |
| title | varchar(255) | ✅ |  |  |  |
| intro | varchar(512) | ✅ |  |  |  |
| free_contents | longtext | ✅ |  |  |  |
| contents | longtext | ✅ |  |  |  |
| start_time | datetime | ✅ |  |  |  |
| second_push_time | datetime | ✅ |  |  |  |
| end_time | datetime | ✅ |  |  |  |
| price | decimal(10,2) | ❌ |  | 价格 |  |
| refund_type | int(11) | ❌ |  | 不中退款：0：否  1：是 |  |
| win | int(11) | ❌ |  | 红黑：0：未知  1：红  2：黑 3:走水 4:2中1 5:3中2 6:4中3 7:被绝杀 |  |
| win_name | varchar(50) | ✅ |  |  |  |
| win_exc | int(11) | ❌ |  | 0:待处理 1:收款 2：退款 |  |
| state | tinyint(4) | ❌ |  | 状态：0：正常 1：封禁 |  |
| conclusion | varchar(255) | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| create_time | timestamp | ✅ |  |  |  |
| update_time | timestamp | ✅ |  |  |  |
| deleted | tinyint(4) | ✅ |  | 删除标志 |  |
| tenant_id | bigint(20) | ✅ |  | 租户ID |  |
| share_pic_url | varchar(255) | ✅ |  |  |  |
| top | int(10) | ✅ |  | 是否置顶 |  |
| top_time | datetime | ✅ |  |  |  |
| status | int(11) | ❌ |  | 上架状态 0:已下架，1：已上架 |  |
| consume_status | int(11) | ❌ |  | 是否有消费条件 0 无 -1 有但是不公开 1 有条件 |  |
| consume_min_num | int(11) | ✅ |  |  |  |
| consume_max_num | int(11) | ✅ |  |  |  |
| consume_min_amount | decimal(10,2) | ✅ |  |  |  |
| consume_max_amount | decimal(10,2) | ✅ |  |  |  |
| add_free_content | longtext | ✅ |  |  |  |
| add_content | longtext | ✅ |  |  |  |
| scheme_play | bigint(20) | ✅ |  |  |  |
| match_scheme | text | ✅ |  |  |  |
| auto_replacement | tinyint(4) | ✅ |  | 自动补单 0:否 1:是 |  |
| match_ids | varchar(255) | ✅ |  |  |  |
| draft | tinyint(4) | ✅ |  | 0 非草稿  1.草稿 |  |
| recommend_win | int(10) | ✅ |  |  |  |
| recommend_win_name | varchar(100) | ✅ |  |  |  |
| fourteen | tinyint(3) | ✅ |  | 是否14场 0.否  1.是 |  |
| issue | varchar(100) | ✅ |  |  |  |
| accomplishment | varchar(255) | ✅ |  |  |  |
| top_bg | varchar(255) | ✅ |  |  |  |

**索引信息**:
- author_Id_index

---

### 表: author_article_append

**表说明**: 文章

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK | ID |  |
| article_id | bigint(20) | ❌ |  |  |  |
| type | int(4) | ✅ |  | 追加类型：0：免费 1：付费 |  |
| contents | longtext | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| create_time | timestamp | ✅ |  |  |  |
| update_time | timestamp | ✅ |  |  |  |
| deleted | tinyint(4) | ✅ |  | 删除标志 |  |
| tenant_id | bigint(20) | ✅ |  | 租户ID |  |

---

### 表: author_article_pv_logs

**表说明**: 文章PV记录

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | ID |  |
| article_id | bigint(20) | ❌ | 📇 FK | 文章ID |  |
| ip | varchar(255) | ✅ |  |  |  |
| user_agent | varchar(255) | ✅ |  |  |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| user_id | bigint(20) | ✅ |  |  |  |
| deleted | tinyint(3) | ✅ |  | 删除标识 |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| tenant_id | bigint(20) | ✅ |  | 租户ID |  |

**索引信息**:
- article_id_index

---

### 表: author_audit

**表说明**: 作者审核列表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| author_id | bigint(20) | ❌ |  |  |  |
| img_url | varchar(255) | ✅ |  |  |  |
| nickname | varchar(255) | ✅ |  |  |  |
| phone | varchar(255) | ✅ |  |  |  |
| desc | varchar(500) | ✅ |  |  |  |
| id_card | varchar(255) | ✅ |  |  |  |
| id_card_front | varchar(255) | ✅ |  |  |  |
| id_card_back | varchar(255) | ✅ |  |  |  |
| name | varchar(255) | ✅ |  |  |  |
| bank_no | varchar(255) | ✅ |  |  |  |
| bank_name | varchar(255) | ✅ |  |  |  |
| bank_real_name | varchar(255) | ✅ |  |  |  |
| pro_desc | varchar(255) | ✅ |  |  |  |
| pro_img_urls | varchar(1000) | ✅ |  |  |  |
| status | int(11) | ❌ |  | 审核状态 0 待审核 -1 审核不通过 1 审核通过 |  |
| fail_reason | varchar(255) | ✅ |  |  |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| deleted | tinyint(1) | ✅ |  | 是否删除 |  |

---

### 表: author_audit_copy1

**表说明**: 作者审核列表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| author_id | bigint(20) | ❌ |  |  |  |
| img_url | varchar(255) | ✅ |  |  |  |
| id_card | varchar(255) | ✅ |  |  |  |
| name | varchar(255) | ✅ |  |  |  |
| status | int(11) | ❌ |  | 审核状态 0 待审核 -1 审核不通过 1 审核通过 |  |
| fail_reason | varchar(255) | ✅ |  |  |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| deleted | tinyint(1) | ✅ |  | 是否删除 |  |

---

### 表: author_commission_rate

**表说明**: 作者分成提现设置表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 用户ID |  |
| author_id | bigint(20) | ❌ |  |  |  |
| rate | decimal(8,2) | ❌ |  |  |  |
| limit | int(11) | ❌ |  | 每日提现次数 默认1 |  |
| max | decimal(10,2) | ❌ |  |  |  |
| min | decimal(10,2) | ❌ |  |  |  |
| days | int(10) | ❌ |  | 提现金额日期 |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| deleted | tinyint(1) | ✅ |  | 是否删除 |  |

---

### 表: author_day_report

**表说明**: 作者日报

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| date | date | ❌ |  |  |  |
| author_id | bigint(20) | ❌ |  |  |  |
| article_num | int(11) | ❌ |  | 发布文章数量 |  |
| pay_user_num | int(11) | ❌ |  | 付费用户数 |  |
| pay_amount | decimal(10,2) | ❌ |  | 线上支付金额 |  |
| refund_amount | decimal(10,2) | ❌ |  | 线上退款金额 |  |
| income_amount | decimal(10,2) | ❌ |  | 收入金额 |  |
| gold_pay_amount | decimal(10,2) | ❌ |  | 鱼币支付金额 |  |
| gold_refund_amount | decimal(10,2) | ❌ |  | 鱼币退款金额 |  |
| income_gold_amount | decimal(10,2) | ❌ |  | 收入鱼币 |  |
| income_total_amount | decimal(10,2) | ❌ |  | 总收入 |  |
| new_user_pay_amount | decimal(10,2) | ❌ |  | 新用户付费金额 |  |
| pay_amount_per_new_user | decimal(10,2) | ❌ |  | 新用户付费单价 |  |
| pay_new_user_percent | decimal(10,2) | ❌ |  | 新用户付费比例 |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| create_time | timestamp | ✅ |  |  |  |
| update_time | timestamp | ✅ |  |  |  |
| deleted | tinyint(4) | ✅ |  | 删除标志 |  |

---

### 表: author_hit_rate

**表说明**: 作者命中率记录表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| author_id | bigint(20) | ❌ |  |  |  |
| hit_rate | decimal(10,4) | ❌ |  | 命中率 |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| create_time | timestamp | ✅ |  |  |  |
| update_time | timestamp | ✅ |  |  |  |
| deleted | tinyint(4) | ✅ |  | 删除标志 |  |

---

### 表: author_info

**表说明**: 无注释

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| author_id | bigint(20) | ❌ |  |  |  |
| phone | varchar(255) | ❌ |  |  |  |
| id_card | varchar(255) | ❌ |  |  |  |
| name | varchar(255) | ❌ |  |  |  |
| idcard_img_front | varchar(500) | ✅ |  |  |  |
| idcard_img_back | varchar(500) | ✅ |  |  |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| deleted | tinyint(1) | ✅ |  | 是否删除 |  |

---

### 表: author_privilege_set

**表说明**: 作者特权设置

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | ID |  |
| author_id | bigint(20) | ❌ |  |  |  |
| type | tinyint(4) | ❌ |  | 特权类型：0：包时套餐 1：次数套餐 |  |
| days | int(11) | ✅ |  |  |  |
| price | decimal(20,6) | ✅ |  |  |  |
| deleted | int(4) | ❌ |  | 是否删除 |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| update_time | datetime | ❌ |  | 修改时间 |  |
| match_type | int(3) | ✅ |  | 方案类型 0.全部  1.14场 2.任9  3.单关  4.二串一  5.多串一 |  |

---

### 表: author_withdraw_logs

**表说明**: 作者提现表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| author_id | bigint(20) | ❌ |  |  |  |
| settlement_id | bigint(20) | ❌ |  |  |  |
| amount | decimal(10,2) | ❌ |  |  |  |
| commission | decimal(10,2) | ❌ |  |  |  |
| received | decimal(10,2) | ❌ |  |  |  |
| status | int(10) | ❌ |  | 提现状态 |  |
| remark | varchar(255) | ✅ |  |  |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| deleted | tinyint(1) | ✅ |  | 是否删除 |  |

---

### 表: authors

**表说明**: 作者表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 用户ID |  |
| correlation_id | varchar(255) | ✅ |  |  |  |
| nickname | varchar(255) | ✅ |  |  |  |
| avatar_url | varchar(255) | ✅ |  |  |  |
| total_income | decimal(10,2) | ✅ |  | 总收益 |  |
| fans | int(10) | ✅ |  | 粉丝数 |  |
| article_num | int(10) | ✅ |  | 文章总数 |  |
| status | int(11) | ✅ |  | 帐号状态 -2 待审核 -1 冻结 0 正常 |  |
| register_ip | varchar(255) | ✅ |  |  |  |
| register_time | datetime | ✅ |  |  |  |
| last_login_ip | varchar(255) | ✅ |  |  |  |
| last_login_time | datetime | ✅ |  |  |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| deleted | tinyint(1) | ✅ |  | 是否删除 |  |

---

### 表: broomer_match_scheme

**表说明**: 扫盘师方案

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| user_id | bigint(20) | ❌ |  |  |  |
| match_id | bigint(20) | ❌ |  |  |  |
| data_type | int(11) | ❌ |  |  |  |
| match_scheme | varchar(2000) | ❌ |  |  |  |
| contents | mediumtext | ❌ |  |  |  |
| price | decimal(10,2) | ❌ |  |  |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| deleted | tinyint(1) | ✅ |  | 是否删除 |  |

---

### 表: broomers

**表说明**: 扫盘师列表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| user_id | bigint(20) | ❌ |  |  |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| deleted | tinyint(1) | ✅ |  | 是否删除 |  |

---

### 表: football_bd_odds

**表说明**: 北单指数

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| comp | varchar(100) | ✅ |  |  |  |
| home | varchar(100) | ✅ |  |  |  |
| away | varchar(100) | ✅ |  |  |  |
| issue | int(11) | ✅ |  |  |  |
| issue_num | int(10) | ✅ |  |  |  |
| match_time | bigint(20) | ✅ |  |  |  |
| sell_status | varchar(255) | ✅ |  |  |  |
| odds | longtext | ✅ |  |  |  |

---

### 表: football_bd_result

**表说明**: 北单开奖结果

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| comp | varchar(100) | ✅ |  |  |  |
| home | varchar(100) | ✅ |  |  |  |
| away | varchar(100) | ✅ |  |  |  |
| issue | int(11) | ✅ |  |  |  |
| issue_num | int(10) | ✅ |  |  |  |
| match_time | bigint(20) | ✅ |  |  |  |
| sell_status | varchar(255) | ✅ |  |  |  |
| odds | longtext | ✅ |  |  |  |
| home_score | int(3) | ✅ |  |  |  |
| away_score | int(3) | ✅ |  |  |  |

---

### 表: football_bd_sf_odds

**表说明**: 北单胜负过关指数

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| sport_id | tinyint(4) | ✅ |  |  |  |
| comp | varchar(100) | ✅ |  |  |  |
| home | varchar(100) | ✅ |  |  |  |
| away | varchar(100) | ✅ |  |  |  |
| issue | int(10) | ✅ |  |  |  |
| issue_num | int(10) | ✅ |  |  |  |
| match_time | bigint(20) | ✅ |  |  |  |
| sell_status | varchar(100) | ✅ |  |  |  |
| odds | longtext | ✅ |  |  |  |

---

### 表: football_bd_sf_result

**表说明**: 北单胜负过关结果

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| sport_id | tinyint(4) | ✅ |  |  |  |
| comp | varchar(100) | ✅ |  |  |  |
| home | varchar(100) | ✅ |  |  |  |
| away | varchar(100) | ✅ |  |  |  |
| issue | int(10) | ✅ |  |  |  |
| issue_num | int(10) | ✅ |  |  |  |
| match_time | bigint(20) | ✅ |  |  |  |
| sell_status | varchar(100) | ✅ |  |  |  |
| odds | longtext | ✅ |  |  |  |
| home_score | int(3) | ✅ |  |  |  |
| away_score | int(3) | ✅ |  |  |  |

---

### 表: football_bo_live_odds

**表说明**: 实时指数

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| company_id | bigint(20) | ❌ |  |  |  |
| match_id | bigint(20) | ❌ |  |  |  |
| odds_update_times | bigint(20) | ✅ |  |  |  |
| home_odds | decimal(10,2) | ❌ |  | 主胜/大球/大 赔率 |  |
| draw_odds | decimal(10,2) | ❌ |  | 和局/盘口 赔率 |  |
| away_odds | decimal(10,2) | ❌ |  | 客胜/小球/小 赔率 |  |
| status | int(10) | ❌ |  | 盘口状态 0 未封盘 1 封盘 |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| create_time | timestamp | ✅ |  |  |  |
| update_time | timestamp | ✅ |  |  |  |
| deleted | tinyint(4) | ✅ |  | 删除标志 |  |

---

### 表: football_company

**表说明**: 公司列表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 主键 |  |
| name_zh | varchar(100) | ✅ |  |  |  |
| name_en | varchar(255) | ✅ |  |  |  |

---

### 表: football_half_live_odds

**表说明**: 半场实时指数

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| company_id | bigint(20) | ❌ |  |  |  |
| match_id | bigint(20) | ❌ |  |  |  |
| match_status | int(10) | ❌ |  | 比赛状态 |  |
| type | varchar(255) | ❌ |  |  |  |
| odds_update_times | bigint(20) | ✅ |  |  |  |
| match_time | varchar(10) | ✅ |  |  |  |
| home_odds | decimal(10,2) | ❌ |  | 主胜/大球/大 赔率 |  |
| draw_odds | decimal(10,2) | ❌ |  | 和局/盘口 赔率 |  |
| away_odds | decimal(10,2) | ❌ |  | 客胜/小球/小 赔率 |  |
| status | int(10) | ❌ |  | 盘口状态 0 未封盘 1 封盘 |  |
| home_score | int(10) | ❌ |  | 主队比分 |  |
| away_score | int(10) | ❌ |  | 客队比分 |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| create_time | timestamp | ✅ |  |  |  |
| update_time | timestamp | ✅ |  |  |  |
| deleted | tinyint(4) | ✅ |  | 删除标志 |  |

---

### 表: football_issue

**表说明**: 足球期数表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 主键 |  |
| issue | int(11) | ✅ |  |  |  |
| start_time | varchar(100) | ✅ |  |  |  |
| end_time | varchar(100) | ✅ |  |  |  |
| draw_time | varchar(100) | ✅ |  |  |  |

---

### 表: football_jc_odds

**表说明**: 竞彩比赛指数

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 主键 |  |
| comp | varchar(100) | ✅ |  |  |  |
| home | varchar(100) | ✅ |  |  |  |
| away | varchar(100) | ✅ |  |  |  |
| short_comp | varchar(100) | ✅ |  |  |  |
| short_home | varchar(100) | ✅ |  |  |  |
| short_away | varchar(100) | ✅ |  |  |  |
| issue_num | varchar(100) | ✅ |  |  |  |
| sell_status | varchar(100) | ✅ |  |  |  |
| match_time | bigint(20) | ✅ |  |  |  |
| spf | varchar(100) | ✅ |  |  |  |
| rq | varchar(100) | ✅ |  |  |  |
| bf | text | ✅ |  |  |  |
| jq | varchar(255) | ✅ |  |  |  |
| bqc | varchar(255) | ✅ |  |  |  |

---

### 表: football_jc_result

**表说明**: 竞彩比赛结果

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 主键 |  |
| comp | varchar(100) | ✅ |  |  |  |
| home | varchar(100) | ✅ |  |  |  |
| away | varchar(100) | ✅ |  |  |  |
| short_comp | varchar(100) | ✅ |  |  |  |
| short_home | varchar(100) | ✅ |  |  |  |
| short_away | varchar(100) | ✅ |  |  |  |
| issue_num | varchar(100) | ✅ |  |  |  |
| match_time | bigint(20) | ✅ |  |  |  |
| home_score | int(3) | ✅ |  |  |  |
| away_score | int(3) | ✅ |  |  |  |
| half_home_score | int(3) | ✅ |  |  |  |
| half_away_score | int(3) | ✅ |  |  |  |
| spf | varchar(255) | ✅ |  |  |  |
| rq | varchar(255) | ✅ |  |  |  |
| bf | varchar(255) | ✅ |  |  |  |
| jq | varchar(255) | ✅ |  |  |  |
| bqc | varchar(255) | ✅ |  |  |  |

---

### 表: football_live_odds

**表说明**: 实时指数

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| company_id | bigint(20) | ❌ |  |  |  |
| match_id | bigint(20) | ❌ |  |  |  |
| match_status | int(10) | ❌ |  | 比赛状态 |  |
| type | varchar(255) | ❌ |  |  |  |
| odds_update_times | bigint(20) | ✅ |  |  |  |
| match_time | varchar(10) | ✅ |  |  |  |
| home_odds | decimal(10,2) | ❌ |  | 主胜/大球/大 赔率 |  |
| draw_odds | decimal(10,2) | ❌ |  | 和局/盘口 赔率 |  |
| away_odds | decimal(10,2) | ❌ |  | 客胜/小球/小 赔率 |  |
| status | int(10) | ❌ |  | 盘口状态 0 未封盘 1 封盘 |  |
| home_score | int(10) | ❌ |  | 主队比分 |  |
| away_score | int(10) | ❌ |  | 客队比分 |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| create_time | timestamp | ✅ |  |  |  |
| update_time | timestamp | ✅ |  |  |  |
| deleted | tinyint(4) | ✅ |  | 删除标志 |  |

---

### 表: football_match

**表说明**: 传统足彩比赛列表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 主键id |  |
| comp | varchar(100) | ✅ |  |  |  |
| home | varchar(100) | ✅ |  |  |  |
| away | varchar(100) | ✅ |  |  |  |
| issue | int(10) | ✅ |  |  |  |
| match_time | bigint(20) | ✅ |  |  |  |
| result | varchar(100) | ✅ |  |  |  |
| type | varchar(100) | ✅ |  |  |  |

---

### 表: football_match_result

**表说明**: 传统足彩开奖结果

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| issue | varchar(100) | ✅ |  |  |  |
| result | varchar(255) | ✅ |  |  |  |
| first_pot_count | int(10) | ✅ |  |  |  |
| first_prize | decimal(10,2) | ✅ |  |  |  |
| sales | decimal(10,2) | ✅ |  |  |  |
| second_pont_count | int(10) | ✅ |  |  |  |
| second_prize | decimal(10,2) | ✅ |  |  |  |
| jackpot | decimal(10,2) | ✅ |  |  |  |
| type | varchar(100) | ✅ |  |  |  |

---

### 表: football_tc_match

**表说明**: 体彩比赛关联列表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 主键 |  |
| sport_id | tinyint(4) | ✅ |  | 球类id 1-足球  2.篮球 |  |
| match_id | bigint(20) | ✅ |  |  |  |
| lottery_type | int(11) | ✅ |  |  |  |
| issue | varchar(100) | ✅ |  |  |  |
| issue_num | varchar(100) | ✅ |  |  |  |
| home_name | varchar(100) | ✅ |  |  |  |
| away_name | varchar(100) | ✅ |  |  |  |
| is_name | tinyint(4) | ✅ |  |  |  |

---

### 表: gold_order

**表说明**: 金币订单表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 主键 |  |
| order_no | varchar(100) | ✅ | 🔒 UK | 订单号 |  |
| user_id | bigint(20) | ✅ | 📇 FK | 用户id |  |
| amount | decimal(10,2) | ✅ |  |  |  |
| pay_amount | decimal(10,2) | ✅ |  |  |  |
| commission | decimal(10,2) | ✅ |  |  |  |
| gold_num | decimal(10,2) | ✅ |  |  |  |
| pay_type | tinyint(4) | ✅ |  |  |  |
| pay_time | datetime | ✅ |  |  |  |
| status | tinyint(4) | ✅ |  | 支付状态 0.待支付 1.支付成功  2.支付失败 |  |
| create_time | datetime | ✅ |  |  |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |
| third_party_no | varchar(100) | ✅ |  |  |  |
| channel_order_sn | varchar(100) | ✅ |  |  |  |
| ins_order_sn | varchar(100) | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| client_ip | varchar(100) | ✅ |  |  |  |
| present_gold | decimal(10,2) | ✅ |  | 赠送金币 |  |
| pay_app_id | varchar(255) | ✅ |  |  |  |
| pay_app_type | int(10) | ✅ |  | 支付渠道类型 默认 0 付呗 1 收钱吧 |  |

**索引信息**:
- order_no
- user_id

---

### 表: guess_records

**表说明**: 用户竞猜记录表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK |  |  |
| match_id | bigint(20) unsigned | ❌ | 📇 FK | 关联赛事ID |  |
| wx_account | varchar(50) | ❌ | 📇 FK | 微信号 |  |
| phone | varchar(20) | ❌ | 📇 FK | 手机号 |  |
| guess_type | tinyint(4) | ❌ |  |  |  |
| guess_item | tinyint(4) | ❌ |  |  |  |
| guess_value | varchar(20) | ❌ |  |  |  |
| status | tinyint(4) | ✅ | 📇 FK | 0 | 状态：0-待开奖 1-已中奖 2-未中奖 |
| win_amount | decimal(10,2) | ✅ |  | 中奖金额 |  |
| is_auto_selected | tinyint(4) | ✅ |  | 是否自动选中：0-否 1-是 |  |
| error_msg | varchar(255) | ✅ |  |  |  |
| created_at | timestamp | ❌ |  |  |  |
| updated_at | timestamp | ❌ |  |  |  |
| deleted_at | timestamp | ✅ |  |  |  |

**索引信息**:
- idx_match_id
- idx_match_user
- idx_phone
- idx_status
- idx_wx_account

---

### 表: home_banner

**表说明**: 首页banner

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | id |  |
| title | varchar(255) | ❌ |  |  |  |
| pic | varchar(255) | ❌ |  |  |  |
| to_url | varchar(255) | ✅ |  |  |  |
| status | int(10) | ❌ |  | 状态：0：启用 1：禁用 |  |
| sort | int(10) | ❌ |  | 排序，越大越靠前 |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |
| creator | varchar(255) | ✅ |  |  |  |
| updater | varchar(255) | ✅ |  |  |  |
| deleted | int(10) | ❌ |  | 删除标识 |  |

---

### 表: infra_api_access_log

**表说明**: API 访问日志表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 日志主键 |  |
| trace_id | varchar(64) | ❌ |  |  |  |
| user_id | bigint(20) | ❌ |  | 用户编号 |  |
| user_type | tinyint(4) | ❌ |  | 用户类型 |  |
| application_name | varchar(50) | ❌ |  |  |  |
| request_method | varchar(16) | ❌ |  |  |  |
| request_url | varchar(255) | ❌ |  |  |  |
| request_params | text | ✅ |  |  |  |
| response_body | text | ✅ |  |  |  |
| user_ip | varchar(50) | ❌ |  |  |  |
| user_agent | varchar(512) | ❌ |  |  |  |
| operate_module | varchar(50) | ✅ |  |  |  |
| operate_name | varchar(50) | ✅ |  |  |  |
| operate_type | tinyint(4) | ✅ |  | 操作分类 |  |
| begin_time | datetime | ❌ |  |  |  |
| end_time | datetime | ❌ |  |  |  |
| duration | int(11) | ❌ |  |  |  |
| result_code | int(11) | ❌ |  | 结果码 |  |
| result_msg | varchar(512) | ✅ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ | 📇 FK | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |
| tenant_id | bigint(20) | ❌ |  | 租户编号 |  |

**索引信息**:
- idx_create_time

---

### 表: infra_api_error_log

**表说明**: 系统异常日志

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 编号 |  |
| trace_id | varchar(64) | ❌ |  |  |  |
| user_id | int(11) | ❌ |  | 用户编号 |  |
| user_type | tinyint(4) | ❌ |  | 用户类型 |  |
| application_name | varchar(50) | ❌ |  |  |  |
| request_method | varchar(16) | ❌ |  |  |  |
| request_url | varchar(255) | ❌ |  |  |  |
| request_params | varchar(8000) | ❌ |  |  |  |
| user_ip | varchar(50) | ❌ |  |  |  |
| user_agent | varchar(512) | ❌ |  |  |  |
| exception_time | datetime | ❌ |  |  |  |
| exception_name | varchar(128) | ❌ |  |  |  |
| exception_message | text | ❌ |  |  |  |
| exception_root_cause_message | text | ❌ |  |  |  |
| exception_stack_trace | text | ❌ |  |  |  |
| exception_class_name | varchar(512) | ❌ |  |  |  |
| exception_file_name | varchar(512) | ❌ |  |  |  |
| exception_method_name | varchar(512) | ❌ |  |  |  |
| exception_line_number | int(11) | ❌ |  |  |  |
| process_status | tinyint(4) | ❌ |  |  |  |
| process_time | datetime | ✅ |  |  |  |
| process_user_id | int(11) | ✅ |  | 处理用户编号 |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |
| tenant_id | bigint(20) | ❌ |  | 租户编号 |  |

---

### 表: infra_codegen_column

**表说明**: 代码生成表字段定义

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 编号 |  |
| table_id | bigint(20) | ❌ |  |  |  |
| column_name | varchar(200) | ❌ |  |  |  |
| data_type | varchar(100) | ❌ |  |  |  |
| column_comment | varchar(500) | ❌ |  |  |  |
| nullable | bit(1) | ❌ |  |  |  |
| primary_key | bit(1) | ❌ |  |  |  |
| ordinal_position | int(11) | ❌ |  |  |  |
| java_type | varchar(32) | ❌ |  |  |  |
| java_field | varchar(64) | ❌ |  |  |  |
| dict_type | varchar(200) | ✅ |  |  |  |
| example | varchar(64) | ✅ |  |  |  |
| create_operation | bit(1) | ❌ |  |  |  |
| update_operation | bit(1) | ❌ |  |  |  |
| list_operation | bit(1) | ❌ |  |  |  |
| list_operation_condition | varchar(32) | ❌ |  | List 查询操作的条件类型 |  |
| list_operation_result | bit(1) | ❌ |  |  |  |
| html_type | varchar(32) | ❌ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: infra_codegen_table

**表说明**: 代码生成表定义

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 编号 |  |
| data_source_config_id | bigint(20) | ❌ |  |  |  |
| scene | tinyint(4) | ❌ |  | 生成场景 |  |
| table_name | varchar(200) | ❌ |  |  |  |
| table_comment | varchar(500) | ❌ |  |  |  |
| remark | varchar(500) | ✅ |  |  |  |
| module_name | varchar(30) | ❌ |  |  |  |
| business_name | varchar(30) | ❌ |  |  |  |
| class_name | varchar(100) | ❌ |  |  |  |
| class_comment | varchar(50) | ❌ |  |  |  |
| author | varchar(50) | ❌ |  |  |  |
| template_type | tinyint(4) | ❌ |  | 模板类型 |  |
| front_type | tinyint(4) | ❌ |  |  |  |
| parent_menu_id | bigint(20) | ✅ |  |  |  |
| master_table_id | bigint(20) | ✅ |  |  |  |
| sub_join_column_id | bigint(20) | ✅ |  |  |  |
| sub_join_many | bit(1) | ✅ |  |  |  |
| tree_parent_column_id | bigint(20) | ✅ |  |  |  |
| tree_name_column_id | bigint(20) | ✅ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: infra_config

**表说明**: 参数配置表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 参数主键 |  |
| category | varchar(50) | ❌ |  |  |  |
| type | tinyint(4) | ❌ |  |  |  |
| name | varchar(100) | ❌ |  |  |  |
| config_key | varchar(100) | ❌ |  |  |  |
| value | varchar(500) | ❌ |  |  |  |
| visible | bit(1) | ❌ |  |  |  |
| remark | varchar(500) | ✅ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: infra_data_source_config

**表说明**: 数据源配置表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 主键编号 |  |
| name | varchar(100) | ❌ |  |  |  |
| url | varchar(1024) | ❌ |  |  |  |
| username | varchar(255) | ❌ |  |  |  |
| password | varchar(255) | ❌ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: infra_file

**表说明**: 文件表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 文件编号 |  |
| tenant_id | bigint(20) | ❌ |  | 租户编号 |  |
| config_id | bigint(20) | ✅ |  |  |  |
| name | varchar(256) | ✅ |  |  |  |
| path | varchar(512) | ❌ |  |  |  |
| url | varchar(1024) | ❌ |  |  |  |
| type | varchar(128) | ✅ |  |  |  |
| size | int(11) | ❌ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: infra_file_config

**表说明**: 文件配置表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 编号 |  |
| tenant_id | bigint(20) | ❌ |  | 租户编号 |  |
| name | varchar(63) | ❌ |  |  |  |
| storage | tinyint(4) | ❌ |  |  |  |
| remark | varchar(255) | ✅ |  |  |  |
| master | bit(1) | ❌ |  |  |  |
| config | varchar(4096) | ❌ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: infra_file_content

**表说明**: 文件表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 编号 |  |
| config_id | bigint(20) | ❌ |  |  |  |
| path | varchar(512) | ❌ |  |  |  |
| content | mediumblob | ❌ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: infra_job

**表说明**: 定时任务表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 任务编号 |  |
| name | varchar(32) | ❌ |  |  |  |
| status | tinyint(4) | ❌ |  |  |  |
| handler_name | varchar(64) | ❌ |  |  |  |
| handler_param | varchar(255) | ✅ |  |  |  |
| cron_expression | varchar(32) | ❌ |  |  |  |
| retry_count | int(11) | ❌ |  | 重试次数 |  |
| retry_interval | int(11) | ❌ |  | 重试间隔 |  |
| monitor_timeout | int(11) | ❌ |  | 监控超时时间 |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: infra_job_log

**表说明**: 定时任务日志表

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | 日志编号 |  |
| job_id | bigint(20) | ❌ |  |  |  |
| handler_name | varchar(64) | ❌ |  |  |  |
| handler_param | varchar(255) | ✅ |  |  |  |
| execute_index | tinyint(4) | ❌ |  | 第几次执行 |  |
| begin_time | datetime | ❌ |  |  |  |
| end_time | datetime | ✅ |  |  |  |
| duration | int(11) | ✅ |  |  |  |
| status | tinyint(4) | ❌ |  |  |  |
| result | varchar(4000) | ✅ |  |  |  |
| creator | varchar(64) | ✅ |  |  |  |
| create_time | datetime | ❌ |  | 创建时间 |  |
| updater | varchar(64) | ✅ |  |  |  |
| update_time | datetime | ❌ |  | 更新时间 |  |
| deleted | bit(1) | ❌ |  | 是否删除 |  |

---

### 表: match_category

**表说明**: 赛事分类

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | ID |  |
| name | varchar(50) | ✅ |  |  |  |
| create_time | datetime | ✅ |  |  |  |
| update_time | datetime | ✅ |  |  |  |

---

### 表: match_coach

**表说明**: 教练

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | ID |  |
| name | varchar(50) | ❌ |  |  |  |
| logo | varchar(255) | ✅ |  |  |  |
| birthday | bigint(20) | ✅ |  |  |  |
| age | int(3) | ✅ |  |  |  |
| preferred_formation | varchar(50) | ✅ |  |  |  |
| joined | bigint(20) | ✅ |  |  |  |
| team_id | bigint(20) | ✅ |  |  |  |
| contract_until | bigint(20) | ✅ |  |  |  |
| type | tinyint(4) | ✅ |  |  |  |

---

### 表: match_competition

**表说明**: 赛事信息

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK | ID |  |
| category_id | bigint(20) | ✅ |  |  |  |
| name | varchar(50) | ✅ |  |  |  |
| short_name | varchar(50) | ✅ |  |  |  |
| logo | varchar(255) | ✅ |  |  |  |
| type | int(11) | ✅ |  |  |  |
| update_time | datetime | ✅ |  | 更新时间 |  |
| country_id | int(10) | ✅ |  |  |  |

---

### 表: match_country

**表说明**: 国家

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | int(10) | ❌ | 🔑 PK | 国家id |  |
| category_id | bigint(20) | ✅ | 📇 FK | 赛事id |  |
| logo | varchar(255) | ✅ |  |  |  |
| name | varchar(100) | ✅ |  |  |  |

