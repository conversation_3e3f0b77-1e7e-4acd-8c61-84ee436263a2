-- =============================================
-- 足球彩票系统多租户改造 - 添加租户字段
-- 执行时间：建议在业务低峰期执行
-- 注意事项：执行前请备份数据库
-- =============================================

-- 设置SQL模式，确保安全执行
SET SQL_SAFE_UPDATES = 0;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 会员模块表结构改造
-- =============================================

-- 会员用户表
ALTER TABLE member_user 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 会员等级表
ALTER TABLE member_level 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 会员等级记录表
ALTER TABLE member_level_record 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 会员积分记录表
ALTER TABLE member_point_record 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 会员签到记录表
ALTER TABLE member_sign_in_record 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 会员分组表（如果存在）
ALTER TABLE member_group 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- =============================================
-- 2. 支付模块表结构改造
-- =============================================

-- 支付应用表
ALTER TABLE pay_app 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 支付订单表
ALTER TABLE pay_order 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 支付退款表
ALTER TABLE pay_refund 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 用户钱包表
ALTER TABLE pay_wallet 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 钱包交易记录表
ALTER TABLE pay_wallet_transaction 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 支付转账表
ALTER TABLE pay_transfer 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- =============================================
-- 3. 业务模块表结构改造
-- =============================================

-- 文章表
ALTER TABLE article 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 文章追加表
ALTER TABLE author_article_append 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 球队信息表
ALTER TABLE match_team 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
ADD COLUMN creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
ADD COLUMN create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
ADD COLUMN update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
ADD COLUMN deleted BIT NOT NULL DEFAULT FALSE COMMENT '是否删除';

-- 轮播图表（如果存在）
ALTER TABLE banner 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 鱼币充值订单表（如果存在）
ALTER TABLE gold_order 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- =============================================
-- 4. 基础设施模块表结构改造
-- =============================================

-- 文件表
ALTER TABLE infra_file 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 文件配置表
ALTER TABLE infra_file_config 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 代码生成表
ALTER TABLE infra_codegen_table 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 代码生成字段表
ALTER TABLE infra_codegen_column 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- =============================================
-- 5. 微信公众号模块表结构改造
-- =============================================

-- 微信账号表
ALTER TABLE mp_account 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 微信消息表
ALTER TABLE mp_message 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 微信用户表
ALTER TABLE mp_user 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 微信标签表
ALTER TABLE mp_tag 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 微信菜单表
ALTER TABLE mp_menu 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 微信自动回复表
ALTER TABLE mp_auto_reply 
ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- =============================================
-- 6. 其他业务表结构改造
-- =============================================

-- 如果有其他业务表，请在此处添加
-- 例如：优惠券、活动、统计等相关表

-- 优惠券表（示例）
-- ALTER TABLE coupon 
-- ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- 活动表（示例）
-- ALTER TABLE activity 
-- ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号' AFTER id;

-- =============================================
-- 7. 验证表结构改造结果
-- =============================================

-- 检查所有表是否都添加了tenant_id字段
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY 
    TABLE_NAME;

-- 恢复SQL模式
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- =============================================
-- 执行完成提示
-- =============================================
SELECT 'tenant_id字段添加完成，请执行下一步：创建索引' AS message;
