-- =============================================
-- 足球彩票系统多租户改造 - 添加租户字段
-- 执行时间：建议在业务低峰期执行
-- 注意事项：执行前请备份数据库
-- 版本：2.0 - 支持重复执行，自动检查字段是否存在
-- =============================================

-- 设置SQL模式，确保安全执行
SET SQL_SAFE_UPDATES = 0;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建存储过程用于安全添加字段
DELIMITER $$

DROP PROCEDURE IF EXISTS AddTenantIdColumn$$

CREATE PROCEDURE AddTenantIdColumn(
    IN table_name VARCHAR(64),
    IN after_column VARCHAR(64)
)
BEGIN
    DECLARE column_exists INT DEFAULT 0;

    -- 检查字段是否已存在
    SELECT COUNT(*) INTO column_exists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = table_name
      AND COLUMN_NAME = 'tenant_id';

    -- 如果字段不存在，则添加
    IF column_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', table_name,
                         ' ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT ''租户编号'' AFTER ', after_column);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        SELECT CONCAT('✅ 表 ', table_name, ' 已添加 tenant_id 字段') AS result;
    ELSE
        SELECT CONCAT('⚠️  表 ', table_name, ' 的 tenant_id 字段已存在，跳过') AS result;
    END IF;
END$$

DELIMITER ;

-- =============================================
-- 1. 会员模块表结构改造
-- =============================================

SELECT '开始处理会员模块表...' AS message;

-- 会员用户表
CALL AddTenantIdColumn('member_user', 'id');

-- 会员等级表
CALL AddTenantIdColumn('member_level', 'id');

-- 会员等级记录表
CALL AddTenantIdColumn('member_level_record', 'id');

-- 会员积分记录表
CALL AddTenantIdColumn('member_point_record', 'id');

-- 会员签到记录表
CALL AddTenantIdColumn('member_sign_in_record', 'id');

-- 会员分组表（如果存在）
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_group';

IF @table_exists > 0 THEN
    CALL AddTenantIdColumn('member_group', 'id');
ELSE
    SELECT '⚠️  表 member_group 不存在，跳过' AS result;
END IF;

-- =============================================
-- 2. 支付模块表结构改造
-- =============================================

SELECT '开始处理支付模块表...' AS message;

-- 支付应用表
CALL AddTenantIdColumn('pay_app', 'id');

-- 支付订单表
CALL AddTenantIdColumn('pay_order', 'id');

-- 支付退款表
CALL AddTenantIdColumn('pay_refund', 'id');

-- 用户钱包表
CALL AddTenantIdColumn('pay_wallet', 'id');

-- 钱包交易记录表
CALL AddTenantIdColumn('pay_wallet_transaction', 'id');

-- 支付转账表（如果存在）
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'pay_transfer';

IF @table_exists > 0 THEN
    CALL AddTenantIdColumn('pay_transfer', 'id');
ELSE
    SELECT '⚠️  表 pay_transfer 不存在，跳过' AS result;
END IF;

-- =============================================
-- 3. 业务模块表结构改造
-- =============================================

SELECT '开始处理业务模块表...' AS message;

-- 文章表
CALL AddTenantIdColumn('author_article', 'id');

-- 文章追加表
CALL AddTenantIdColumn('author_article_append', 'id');

-- 球队信息表 - 需要特殊处理，可能需要添加基础字段
DELIMITER $$

DROP PROCEDURE IF EXISTS HandleMatchTeamTable$$

CREATE PROCEDURE HandleMatchTeamTable()
BEGIN
    DECLARE table_exists INT DEFAULT 0;
    DECLARE tenant_id_exists INT DEFAULT 0;
    DECLARE creator_exists INT DEFAULT 0;

    -- 检查表是否存在
    SELECT COUNT(*) INTO table_exists
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'match_team';

    IF table_exists > 0 THEN
        -- 检查tenant_id字段
        SELECT COUNT(*) INTO tenant_id_exists
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = 'match_team'
          AND COLUMN_NAME = 'tenant_id';

        -- 检查creator字段（判断是否已有基础字段）
        SELECT COUNT(*) INTO creator_exists
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = 'match_team'
          AND COLUMN_NAME = 'creator';

        -- 添加tenant_id字段
        IF tenant_id_exists = 0 THEN
            ALTER TABLE match_team ADD COLUMN tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号';
            SELECT '✅ 表 match_team 已添加 tenant_id 字段' AS result;
        ELSE
            SELECT '⚠️  表 match_team 的 tenant_id 字段已存在，跳过' AS result;
        END IF;

        -- 添加基础字段（如果不存在）
        IF creator_exists = 0 THEN
            ALTER TABLE match_team
            ADD COLUMN creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
            ADD COLUMN create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            ADD COLUMN updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
            ADD COLUMN update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            ADD COLUMN deleted BIT NOT NULL DEFAULT FALSE COMMENT '是否删除';
            SELECT '✅ 表 match_team 已添加基础字段' AS result;
        ELSE
            SELECT '⚠️  表 match_team 的基础字段已存在，跳过' AS result;
        END IF;
    ELSE
        SELECT '⚠️  表 match_team 不存在，跳过' AS result;
    END IF;
END$$

DELIMITER ;

CALL HandleMatchTeamTable();

-- 轮播图表（如果存在）
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'banner';

IF @table_exists > 0 THEN
    CALL AddTenantIdColumn('banner', 'id');
ELSE
    SELECT '⚠️  表 banner 不存在，跳过' AS result;
END IF;

-- 鱼币充值订单表（如果存在）
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'gold_order';

IF @table_exists > 0 THEN
    CALL AddTenantIdColumn('gold_order', 'id');
ELSE
    SELECT '⚠️  表 gold_order 不存在，跳过' AS result;
END IF;

-- =============================================
-- 4. 基础设施模块表结构改造
-- =============================================

SELECT '开始处理基础设施模块表...' AS message;

-- 文件表
CALL AddTenantIdColumn('infra_file', 'id');

-- 文件配置表
CALL AddTenantIdColumn('infra_file_config', 'id');

-- 代码生成表
CALL AddTenantIdColumn('infra_codegen_table', 'id');

-- 代码生成字段表
CALL AddTenantIdColumn('infra_codegen_column', 'id');

-- =============================================
-- 5. 微信公众号模块表结构改造
-- =============================================

SELECT '开始处理微信公众号模块表...' AS message;

-- 微信账号表
CALL AddTenantIdColumn('mp_account', 'id');

-- 微信消息表
CALL AddTenantIdColumn('mp_message', 'id');

-- 微信用户表
CALL AddTenantIdColumn('mp_user', 'id');

-- 微信标签表
CALL AddTenantIdColumn('mp_tag', 'id');

-- 微信菜单表
CALL AddTenantIdColumn('mp_menu', 'id');

-- 微信自动回复表
CALL AddTenantIdColumn('mp_auto_reply', 'id');

-- =============================================
-- 6. 其他业务表结构改造
-- =============================================

SELECT '开始处理其他业务表...' AS message;

-- 检查并处理可能存在的其他业务表
-- 优惠券表（如果存在）
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'coupon';

IF @table_exists > 0 THEN
    CALL AddTenantIdColumn('coupon', 'id');
ELSE
    SELECT '⚠️  表 coupon 不存在，跳过' AS result;
END IF;

-- 活动表（如果存在）
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'activity';

IF @table_exists > 0 THEN
    CALL AddTenantIdColumn('activity', 'id');
ELSE
    SELECT '⚠️  表 activity 不存在，跳过' AS result;
END IF;

-- =============================================
-- 7. 验证表结构改造结果
-- =============================================

SELECT '验证表结构改造结果...' AS message;

-- 检查所有表是否都添加了tenant_id字段
SELECT
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '是否可空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM
    INFORMATION_SCHEMA.COLUMNS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY
    TABLE_NAME;

-- 统计添加了tenant_id字段的表数量
SELECT
    COUNT(*) as '已添加tenant_id字段的表数量'
FROM
    INFORMATION_SCHEMA.COLUMNS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id';

-- 清理存储过程
DROP PROCEDURE IF EXISTS AddTenantIdColumn;
DROP PROCEDURE IF EXISTS HandleMatchTeamTable;

-- 恢复SQL模式
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- =============================================
-- 执行完成提示
-- =============================================
SELECT '✅ tenant_id字段添加完成，请执行下一步：创建索引' AS message;
