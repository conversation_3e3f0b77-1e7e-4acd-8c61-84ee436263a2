#!/bin/bash

# =============================================
# 完整表分类脚本
# 将所有191个表按功能完整分类，不做裁剪
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "========================================"
echo "完整表分类工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database/complete-classification

log_info "分析所有表的完整分类..."

# 获取所有表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

TOTAL_TABLES=$(wc -l < /tmp/all_tables.txt)

# 生成完整分类报告
cat > docs/database/complete-classification/complete-table-classification.md << 'EOF'
# 数据库表完整分类报告

## 📊 概览

本报告对数据库中的所有表进行完整分类，不做任何裁剪，确保每个表都有明确的归属。

EOF

echo "**总表数**: $TOTAL_TABLES" >> docs/database/complete-classification/complete-table-classification.md
echo "**分析时间**: $(date)" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

# 1. 平台框架表（系统核心）
echo "## 🏗️ 1. 平台框架表（Framework Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 系统核心框架功能，包含用户权限、租户管理、字典配置等" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

FRAMEWORK_TABLES=$(grep -E "^system_" /tmp/all_tables.txt | grep -vE "demo|test|log" 2>/dev/null || echo "")
if [ -n "$FRAMEWORK_TABLES" ]; then
    echo "$FRAMEWORK_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 2. 足彩业务表（football_*）
echo "## ⚽ 2. 足彩业务表（Football Business Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 足彩相关的核心业务功能" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

FOOTBALL_TABLES=$(grep -E "^football_" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$FOOTBALL_TABLES" ]; then
    echo "$FOOTBALL_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
else
    echo "⚠️ 当前数据库中未发现football_*表，可能使用了其他命名规范" >> docs/database/complete-classification/complete-table-classification.md
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 3. 赛事信息表（match_*）
echo "## 🏆 3. 赛事信息表（Match Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 赛事、球队、比赛相关信息管理" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

MATCH_TABLES=$(grep -E "^match_" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$MATCH_TABLES" ]; then
    echo "$MATCH_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
else
    echo "⚠️ 当前数据库中未发现match_*表" >> docs/database/complete-classification/complete-table-classification.md
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 4. 会员功能表（member_*）
echo "## 👥 4. 会员功能表（Member Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 会员管理、积分、等级、签到等功能" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

MEMBER_TABLES=$(grep -E "^member_" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$MEMBER_TABLES" ]; then
    echo "$MEMBER_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 5. 作业调度表（qrtz_*）
echo "## ⏰ 5. 作业调度表（Quartz Scheduler Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: Quartz框架的作业调度基础设施" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

QRTZ_TABLES=$(grep -E "^qrtz_" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$QRTZ_TABLES" ]; then
    echo "$QRTZ_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 6. 支付功能表（pay_*）
echo "## 💰 6. 支付功能表（Payment Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 支付、钱包、订单相关功能" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

PAY_TABLES=$(grep -E "^pay_" /tmp/all_tables.txt | grep -v demo 2>/dev/null || echo "")
if [ -n "$PAY_TABLES" ]; then
    echo "$PAY_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 7. 微信功能表（mp_*）
echo "## 💬 7. 微信功能表（WeChat Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 微信公众号、小程序相关功能" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

MP_TABLES=$(grep -E "^mp_" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$MP_TABLES" ]; then
    echo "$MP_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 8. 基础设施表（infra_*）
echo "## 🔧 8. 基础设施表（Infrastructure Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 文件管理、代码生成、配置管理等基础设施" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

INFRA_TABLES=$(grep -E "^infra_" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$INFRA_TABLES" ]; then
    echo "$INFRA_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 9. 内容管理表（author_*等）
echo "## 📝 9. 内容管理表（Content Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 文章、作者、内容相关管理" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

CONTENT_TABLES=$(grep -E "^author_|^banner|^gold_" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$CONTENT_TABLES" ]; then
    echo "$CONTENT_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 10. 企业微信表（wx_*）
echo "## 🏢 10. 企业微信表（Work WeChat Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 企业微信相关功能" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

WX_TABLES=$(grep -E "^wx_" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$WX_TABLES" ]; then
    echo "$WX_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
else
    echo "⚠️ 当前数据库中未发现wx_*表" >> docs/database/complete-classification/complete-table-classification.md
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 11. Demo和测试表
echo "## 🗑️ 11. Demo和测试表（Demo/Test Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 框架示例和测试用表，可以安全删除" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

DEMO_TABLES=$(grep -iE "demo|test|yudao" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$DEMO_TABLES" ]; then
    echo "$DEMO_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO ❌ 可删除" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
else
    echo "✅ 未发现Demo/测试表" >> docs/database/complete-classification/complete-table-classification.md
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 12. 日志表
echo "## 📋 12. 日志表（Log Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 系统日志、操作记录等" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

LOG_TABLES=$(grep -E "log|logs" /tmp/all_tables.txt 2>/dev/null || echo "")
if [ -n "$LOG_TABLES" ]; then
    echo "$LOG_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 13. 未明确分类的表
echo "## ❓ 13. 未明确分类的表（Unclassified Tables）" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "**功能**: 需要进一步确认功能的表" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md

# 获取所有已分类的表
CLASSIFIED_PATTERNS="^system_|^football_|^match_|^member_|^qrtz_|^pay_|^mp_|^infra_|^author_|^banner|^gold_|^wx_|demo|test|yudao|log|logs"

# 找出未分类的表
> /tmp/unclassified_tables.txt
while read table_name; do
    if [ -n "$table_name" ]; then
        if ! echo "$table_name" | grep -qE "$CLASSIFIED_PATTERNS"; then
            echo "$table_name" >> /tmp/unclassified_tables.txt
        fi
    fi
done < /tmp/all_tables.txt

if [ -s /tmp/unclassified_tables.txt ]; then
    while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            echo "- **$table_name**: $TABLE_INFO ⚠️ 需确认" >> docs/database/complete-classification/complete-table-classification.md
        fi
    done < /tmp/unclassified_tables.txt
else
    echo "✅ 所有表都已分类" >> docs/database/complete-classification/complete-table-classification.md
fi

echo "" >> docs/database/complete-classification/complete-table-classification.md

# 统计信息
FRAMEWORK_COUNT=$(grep -cE "^system_" /tmp/all_tables.txt | grep -vcE "demo|test|log" 2>/dev/null || echo "0")
FOOTBALL_COUNT=$(grep -cE "^football_" /tmp/all_tables.txt 2>/dev/null || echo "0")
MATCH_COUNT=$(grep -cE "^match_" /tmp/all_tables.txt 2>/dev/null || echo "0")
MEMBER_COUNT=$(grep -cE "^member_" /tmp/all_tables.txt 2>/dev/null || echo "0")
QRTZ_COUNT=$(grep -cE "^qrtz_" /tmp/all_tables.txt 2>/dev/null || echo "0")
PAY_COUNT=$(grep -cE "^pay_" /tmp/all_tables.txt | grep -vc demo 2>/dev/null || echo "0")
MP_COUNT=$(grep -cE "^mp_" /tmp/all_tables.txt 2>/dev/null || echo "0")
INFRA_COUNT=$(grep -cE "^infra_" /tmp/all_tables.txt 2>/dev/null || echo "0")
CONTENT_COUNT=$(grep -cE "^author_|^banner|^gold_" /tmp/all_tables.txt 2>/dev/null || echo "0")
WX_COUNT=$(grep -cE "^wx_" /tmp/all_tables.txt 2>/dev/null || echo "0")
DEMO_COUNT=$(grep -icE "demo|test|yudao" /tmp/all_tables.txt 2>/dev/null || echo "0")
LOG_COUNT=$(grep -cE "log|logs" /tmp/all_tables.txt 2>/dev/null || echo "0")
UNCLASSIFIED_COUNT=$(wc -l < /tmp/unclassified_tables.txt 2>/dev/null || echo "0")

echo "## 📊 分类统计" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "| 分类 | 表数量 | 说明 |" >> docs/database/complete-classification/complete-table-classification.md
echo "|------|--------|------|" >> docs/database/complete-classification/complete-table-classification.md
echo "| 平台框架表 | $FRAMEWORK_COUNT | 系统核心功能 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 足彩业务表 | $FOOTBALL_COUNT | 足彩核心业务 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 赛事信息表 | $MATCH_COUNT | 赛事比赛数据 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 会员功能表 | $MEMBER_COUNT | 会员管理功能 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 作业调度表 | $QRTZ_COUNT | Quartz调度器 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 支付功能表 | $PAY_COUNT | 支付钱包功能 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 微信功能表 | $MP_COUNT | 微信公众号功能 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 基础设施表 | $INFRA_COUNT | 基础设施功能 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 内容管理表 | $CONTENT_COUNT | 内容管理功能 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 企业微信表 | $WX_COUNT | 企业微信功能 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| Demo测试表 | $DEMO_COUNT | 可删除 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 日志表 | $LOG_COUNT | 日志记录 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| 未分类表 | $UNCLASSIFIED_COUNT | 需要确认 |" >> docs/database/complete-classification/complete-table-classification.md
echo "| **总计** | **$TOTAL_TABLES** | **所有表** |" >> docs/database/complete-classification/complete-table-classification.md

echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "## 🎯 重构建议" >> docs/database/complete-classification/complete-table-classification.md
echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "### 按功能模块组织SQL脚本" >> docs/database/complete-classification/complete-table-classification.md
echo "1. **01-framework-tables.sql** - 平台框架表" >> docs/database/complete-classification/complete-table-classification.md
echo "2. **02-football-business-tables.sql** - 足彩业务表" >> docs/database/complete-classification/complete-table-classification.md
echo "3. **03-match-tables.sql** - 赛事信息表" >> docs/database/complete-classification/complete-table-classification.md
echo "4. **04-member-tables.sql** - 会员功能表" >> docs/database/complete-classification/complete-table-classification.md
echo "5. **05-quartz-tables.sql** - 作业调度表" >> docs/database/complete-classification/complete-table-classification.md
echo "6. **06-payment-tables.sql** - 支付功能表" >> docs/database/complete-classification/complete-table-classification.md
echo "7. **07-wechat-tables.sql** - 微信功能表" >> docs/database/complete-classification/complete-table-classification.md
echo "8. **08-infrastructure-tables.sql** - 基础设施表" >> docs/database/complete-classification/complete-table-classification.md
echo "9. **09-content-tables.sql** - 内容管理表" >> docs/database/complete-classification/complete-table-classification.md
echo "10. **10-unclassified-tables.sql** - 未分类表" >> docs/database/complete-classification/complete-table-classification.md
echo "11. **99-basic-data.sql** - 基础数据" >> docs/database/complete-classification/complete-table-classification.md

echo "" >> docs/database/complete-classification/complete-table-classification.md
echo "### 注意事项" >> docs/database/complete-classification/complete-table-classification.md
echo "- ✅ **不做裁剪**: 保留所有业务表，确保功能完整" >> docs/database/complete-classification/complete-table-classification.md
echo "- ⚠️ **Demo表**: 可以安全删除，不影响业务功能" >> docs/database/complete-classification/complete-table-classification.md
echo "- 🔍 **未分类表**: 需要进一步分析确认功能" >> docs/database/complete-classification/complete-table-classification.md
echo "- 📋 **日志表**: 根据监控需求决定是否保留" >> docs/database/complete-classification/complete-table-classification.md

log_success "完整表分类完成: docs/database/complete-classification/complete-table-classification.md"

# 清理临时文件
rm -f /tmp/unclassified_tables.txt

# 清理临时文件
rm -f /tmp/all_tables.txt

echo
echo "========================================"
echo "完整表分类完成！"
echo "========================================"
echo "📋 docs/database/complete-classification/complete-table-classification.md - 完整分类报告"
echo "📊 总表数: $TOTAL_TABLES"
echo "========================================"
