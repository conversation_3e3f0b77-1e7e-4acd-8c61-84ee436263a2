erDiagram
    %% 足球彩票系统数据库ER图
    %% 基于实际数据库表生成
    
    %% 系统租户表
    system_tenant {
        bigint id PK "租户ID"
        varchar name "租户名称"
        varchar contact_name "联系人"
        varchar contact_mobile "联系电话"
        tinyint status "状态"
        datetime expire_time "过期时间"
        datetime create_time "创建时间"
    }
    
    %% 会员模块
    member_user {
        bigint id PK "用户ID"
        bigint tenant_id "租户ID"
        varchar username "用户名"
        varchar mobile "手机号"
        varchar nickname "昵称"
        decimal gold "鱼币"
        decimal balance "余额"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
    member_level {
        bigint id PK "等级ID"
        bigint tenant_id "租户ID"
        varchar name "等级名称"
        int level "等级值"
        decimal discount_percent "折扣比例"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
    %% 支付模块
    pay_order {
        bigint id PK "订单ID"
        bigint tenant_id "租户ID"
        bigint app_id "应用ID"
        varchar merchant_order_id "商户订单号"
        varchar subject "订单标题"
        int price "支付金额(分)"
        tinyint status "订单状态"
        datetime success_time "支付成功时间"
        datetime create_time "创建时间"
    }
    
    pay_wallet {
        bigint id PK "钱包ID"
        bigint tenant_id "租户ID"
        bigint user_id "用户ID"
        varchar user_type "用户类型"
        int balance "余额(分)"
        int total_expense "总支出"
        int total_recharge "总充值"
        datetime create_time "创建时间"
    }
    
    pay_app {
        bigint id PK "应用ID"
        bigint tenant_id "租户ID"
        varchar name "应用名称"
        tinyint status "状态"
        varchar pay_notify_url "支付回调地址"
        varchar refund_notify_url "退款回调地址"
        datetime create_time "创建时间"
    }
    
    %% 业务模块
    author_article {
        bigint id PK "文章ID"
        bigint tenant_id "租户ID"
        bigint author_id "作者ID"
        varchar title "标题"
        text intro "简介"
        text free_contents "免费内容"
        text contents "付费内容"
        decimal price "价格"
        tinyint status "状态"
        datetime start_time "开始时间"
        datetime create_time "创建时间"
    }
    
    author_article_append {
        bigint id PK "追加ID"
        bigint tenant_id "租户ID"
        bigint article_id "文章ID"
        text content "追加内容"
        datetime create_time "创建时间"
    }
    
    %% 系统管理
    system_users {
        bigint id PK "用户ID"
        bigint tenant_id "租户ID"
        varchar username "用户名"
        varchar nickname "昵称"
        varchar mobile "手机号"
        tinyint status "状态"
        datetime create_time "创建时间"
    }
    
    %% 表关系定义
    system_tenant ||--o{ member_user : "tenant_id"
    system_tenant ||--o{ pay_order : "tenant_id"
    system_tenant ||--o{ pay_wallet : "tenant_id"
    system_tenant ||--o{ author_article : "tenant_id"
    member_user ||--|| pay_wallet : "user_id"
    member_user ||--o{ pay_order : "user_id"
    member_user ||--o{ author_article : "author_id"
    pay_app ||--o{ pay_order : "app_id"
    author_article ||--o{ author_article_append : "article_id"
