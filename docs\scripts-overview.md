# 数据库分析脚本完整清单

## 📁 脚本目录结构

```
scripts/
├── universal-database-analyzer.sh      # 🌟 主要脚本：通用数据库分析器
├── fixed-table-analyzer.sh            # ✅ 修复版：简化表分析器
├── analyze-football-lottery-db.sh     # 🎯 专用脚本：足球彩票项目
├── complete-table-classification.sh   # 📊 原始脚本：表分类分析
├── debug-mysql-queries.sh            # 🔧 调试工具：MySQL查询测试
├── test-single-table.sh              # 🧪 测试工具：单表分析
├── debug-while-loop.sh               # 🔍 调试工具：循环问题诊断
├── debug-failed-tables.sh            # 🔍 调试工具：失败表诊断
└── generate-complete-database-scripts.sh # 📝 生成工具：完整数据库脚本

docs/
├── universal-database-analyzer-complete.md # 📖 完整文档
├── quick-start-guide.md                   # 🚀 快速开始指南
├── database-analyzer-fix-summary.md       # 🔧 修复总结
└── scripts-overview.md                    # 📋 本文档
```

## 🌟 核心脚本功能对比

| 脚本名称 | 主要功能 | 适用场景 | 输出格式 | 状态 |
|---------|---------|---------|---------|------|
| **universal-database-analyzer.sh** | 全功能数据库分析 | 任何MySQL数据库 | MD + Mermaid | ✅ 推荐 |
| **fixed-table-analyzer.sh** | 简化表结构分析 | 小型数据库测试 | MD | ✅ 可用 |
| **analyze-football-lottery-db.sh** | 足球彩票项目专用 | 特定项目 | MD | ✅ 专用 |
| **complete-table-classification.sh** | 表分类和ER图 | 表分类分析 | MD + Mermaid | ✅ 原始版 |

## 📊 功能特性对比

### 核心功能
| 功能 | universal | fixed | football | complete |
|------|-----------|-------|----------|----------|
| 表结构分析 | ✅ | ✅ | ✅ | ✅ |
| 字段详情 | ✅ | ✅ | ✅ | ✅ |
| 表注释获取 | ✅ | ✅ | ✅ | ✅ |
| 数据字典 | ✅ | ✅ | ❌ | ❌ |
| ER图生成 | ✅ | ❌ | ✅ | ✅ |
| 智能分类 | ✅ | ❌ | ✅ | ✅ |

### 技术特性
| 特性 | universal | fixed | football | complete |
|------|-----------|-------|----------|----------|
| 跨平台兼容 | ✅ | ✅ | ✅ | ❌ |
| 回车符修复 | ✅ | ✅ | ❌ | ❌ |
| 配置文件支持 | ✅ | ❌ | ✅ | ❌ |
| 命令行参数 | ✅ | ❌ | ❌ | ❌ |
| 错误处理 | ✅ | ✅ | ✅ | ✅ |
| 进度显示 | ✅ | ✅ | ✅ | ✅ |

### 输出质量
| 输出 | universal | fixed | football | complete |
|------|-----------|-------|----------|----------|
| 格式规范 | ✅ | ✅ | ✅ | ✅ |
| 中文支持 | ✅ | ✅ | ✅ | ✅ |
| 表格美化 | ✅ | ✅ | ✅ | ✅ |
| 关系推断 | ✅ | ❌ | ✅ | ✅ |
| 统计信息 | ✅ | ❌ | ✅ | ✅ |

## 🎯 使用建议

### 推荐使用场景

#### 1. 通用数据库分析 → `universal-database-analyzer.sh`
```bash
# 完整功能，适合生产环境
bash scripts/universal-database-analyzer.sh \
    --database=mydb \
    --user=root \
    --password=pass \
    --with-structure \
    --with-er-diagram \
    --with-data-dict
```

#### 2. 快速表结构查看 → `fixed-table-analyzer.sh`
```bash
# 简单快速，适合开发调试
bash scripts/fixed-table-analyzer.sh
```

#### 3. 足球彩票项目 → `analyze-football-lottery-db.sh`
```bash
# 项目专用，自动配置
bash scripts/analyze-football-lottery-db.sh
```

#### 4. 问题调试 → 调试工具
```bash
# MySQL连接问题
bash scripts/debug-mysql-queries.sh

# 单表测试
bash scripts/test-single-table.sh
```

## 🔧 调试和测试工具

### 调试工具详情
| 工具 | 用途 | 输出 | 使用时机 |
|------|------|------|---------|
| `debug-mysql-queries.sh` | MySQL查询测试 | 控制台 | 连接问题 |
| `test-single-table.sh` | 单表字段测试 | 控制台 | 字段问题 |
| `debug-while-loop.sh` | 循环逻辑测试 | 控制台 | 循环问题 |
| `debug-failed-tables.sh` | 失败表诊断 | 控制台 | 表查询失败 |
| `debug-exact-logic.sh` | 精确逻辑复制 | 控制台 | 逻辑问题 |

### 常用调试命令
```bash
# 1. 检查MySQL连接
bash scripts/debug-mysql-queries.sh

# 2. 测试特定表
# 编辑脚本中的TEST_TABLE变量
bash scripts/test-single-table.sh

# 3. 检查表名问题
xxd -p temp_tables.txt  # 查看十六进制内容

# 4. 检查权限
mysql -u root -p -e "SHOW GRANTS FOR CURRENT_USER();"
```

## 📈 性能和限制

### 性能指标
| 数据库规模 | 推荐脚本 | 预计时间 | 内存使用 |
|-----------|---------|---------|---------|
| 小型 (<50表) | universal | 1-2分钟 | <100MB |
| 中型 (50-200表) | universal | 3-5分钟 | <200MB |
| 大型 (>200表) | 分批处理 | 10+分钟 | <500MB |

### 使用限制
- **MySQL版本**: 5.7+ (需要INFORMATION_SCHEMA支持)
- **权限要求**: SELECT权限 + INFORMATION_SCHEMA访问权限
- **字符编码**: 建议使用UTF-8
- **平台支持**: Linux/macOS/Windows(Git Bash)

## 🔄 版本历史

### v2.1 (当前版本) - 2025-01-16
- ✅ 修复Windows回车符问题
- ✅ 完善跨平台兼容性
- ✅ 增强错误处理
- ✅ 优化输出格式

### v2.0 - 2025-01-15
- ✅ 重构为通用分析器
- ✅ 添加配置文件支持
- ✅ 增加命令行参数
- ✅ 模块化设计

### v1.0 - 2025-01-10
- ✅ 基础表分类功能
- ✅ ER图生成
- ✅ 足球彩票项目专用

## 🚀 未来规划

### 计划功能
- [ ] 支持PostgreSQL数据库
- [ ] 添加数据库性能分析
- [ ] 支持多数据库对比
- [ ] Web界面版本
- [ ] Docker容器化

### 优化方向
- [ ] 并行处理提升性能
- [ ] 更智能的关系推断
- [ ] 自定义模板支持
- [ ] 增量分析功能

## 📞 技术支持

### 问题反馈
1. 检查是否使用最新版本
2. 运行相应的调试工具
3. 提供错误日志和环境信息
4. 描述具体的使用场景

### 贡献指南
1. Fork项目仓库
2. 创建功能分支
3. 提交Pull Request
4. 添加测试用例

---

**脚本清单完成！** 📋

现在您对所有数据库分析脚本有了全面的了解，可以根据具体需求选择合适的工具。
