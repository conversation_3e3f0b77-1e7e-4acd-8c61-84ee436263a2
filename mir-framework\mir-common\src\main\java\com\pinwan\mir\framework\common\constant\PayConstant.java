package com.pinwan.mir.framework.common.constant;

/**
 * 支付相关常量类
 */
public class PayConstant {

    /** 支付二维码返回类型 - 二维码BASE64 */
    public static final String PAY_QRCODE_TYPE_BASE64 = "BASE64";
    /** 支付二维码返回类型 - 二维码访问路径 */
    public static final String PAY_QRCODE_TYPE_URL = "URL";

    /**
     * 请求第三方支付平台成功
     */
    public static final String SUCCESS = "SUCCESS";

    public static final Integer SUCCESS_200 = 200;

    /**
     * 4100 退款金额超过今日实收金额，暂无法退款
     */
    public static final Integer FAIL_4100 = 4100;
    /**
     * 请求第三方支付平台失败
     */
    public static final String FAIL = "FAIL";

    /** 订单状态 - 待支付 */
    public static final Integer ORDER_TYPE_WAIT = 0;
    /** 订单状态 - 支付成功 */
    public static final Integer ORDER_TYPE_SUCCESS = 1;
    /** 订单状态 - 支付失败 */
    public static final Integer ORDER_TYPE_FAIL = 2;

    /** 订单状态 - 已关闭 */
    public static final Integer ORDER_TYPE_CLOSE = -1;

    /** 充值类型 - 充值到钱包 */
    public static final Integer RECHARGE_TYPE_WALLET = 1;
    /** 充值类型 - 充值到游戏 */
    public static final Integer RECHARGE_TYPE_GAME = 2;

}
