#!/bin/bash

# =============================================
# 紧急修复脚本 - 解决 Duplicate column name 错误
# 专门用于修复部分执行失败的多租户改造
# =============================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================"
echo "紧急修复脚本 - 解决重复字段错误"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
log_info "验证数据库连接..."
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    log_error "数据库连接失败"
    exit 1
fi
log_success "数据库连接成功"

# 创建临时修复脚本
TEMP_SQL="/tmp/emergency_fix_$(date +%s).sql"

log_info "生成修复脚本..."

cat > "$TEMP_SQL" << 'EOF'
-- 紧急修复脚本 - 安全添加tenant_id字段
SET SQL_SAFE_UPDATES = 0;
SET FOREIGN_KEY_CHECKS = 0;

-- 检查并添加字段的函数
DELIMITER $$

DROP PROCEDURE IF EXISTS SafeAddColumn$$

CREATE PROCEDURE SafeAddColumn(
    IN p_table_name VARCHAR(64),
    IN p_column_name VARCHAR(64),
    IN p_column_definition TEXT
)
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    DECLARE table_exists INT DEFAULT 0;
    
    -- 检查表是否存在
    SELECT COUNT(*) INTO table_exists
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = p_table_name;
    
    IF table_exists > 0 THEN
        -- 检查字段是否已存在
        SELECT COUNT(*) INTO column_exists
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = p_table_name
          AND COLUMN_NAME = p_column_name;
        
        -- 如果字段不存在，则添加
        IF column_exists = 0 THEN
            SET @sql = CONCAT('ALTER TABLE ', p_table_name, ' ADD COLUMN ', p_column_definition);
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            SELECT CONCAT('✅ 表 ', p_table_name, ' 已添加字段 ', p_column_name) AS result;
        ELSE
            SELECT CONCAT('⚠️  表 ', p_table_name, ' 的字段 ', p_column_name, ' 已存在，跳过') AS result;
        END IF;
    ELSE
        SELECT CONCAT('❌ 表 ', p_table_name, ' 不存在，跳过') AS result;
    END IF;
END$$

DELIMITER ;

-- 开始修复
SELECT '开始修复tenant_id字段...' AS message;

-- 会员模块
CALL SafeAddColumn('member_user', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('member_level', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('member_level_record', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('member_point_record', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('member_sign_in_record', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');

-- 支付模块
CALL SafeAddColumn('pay_app', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('pay_order', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('pay_refund', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('pay_wallet', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('pay_wallet_transaction', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');

-- 业务模块
CALL SafeAddColumn('author_article', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('author_article_append', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('match_team', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\'');

-- 基础设施模块
CALL SafeAddColumn('infra_file', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('infra_file_config', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');

-- 微信模块
CALL SafeAddColumn('mp_account', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('mp_message', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');
CALL SafeAddColumn('mp_user', 'tenant_id', 'tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT \'租户编号\' AFTER id');

-- 检查结果
SELECT '修复完成，检查结果:' AS message;

SELECT 
    TABLE_NAME as '表名',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY 
    TABLE_NAME;

SELECT 
    CONCAT('总共添加了 ', COUNT(*), ' 个表的tenant_id字段') as '统计结果'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id';

-- 清理
DROP PROCEDURE SafeAddColumn;

SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

SELECT '✅ 紧急修复完成' AS message;
EOF

log_info "执行修复脚本..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$TEMP_SQL"

if [ $? -eq 0 ]; then
    log_success "修复完成！"
    rm -f "$TEMP_SQL"

    # 执行简单检查
    log_info "执行修复后检查..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < sql/tenant-migration/00-ultra-simple-check.sql 2>/dev/null || {
        log_warning "详细检查失败，但修复已完成"
    }

    echo
    echo "========================================"
    log_success "紧急修复成功完成！"
    echo "========================================"
    echo
    log_info "现在您可以："
    echo "1. 继续执行索引创建: mysql -u root -p mir < sql/tenant-migration/02-create-indexes.sql"
    echo "2. 执行数据迁移: mysql -u root -p mir < sql/tenant-migration/03-migrate-data.sql"
    echo "3. 或者使用完整的改造脚本: ./scripts/saas-migration.sh"
    echo
else
    log_error "修复失败，临时脚本保存在: $TEMP_SQL"
    echo "请检查错误信息并手动处理"
    exit 1
fi
