erDiagram
    %% 核心业务ER图 - 重构后的核心表关系
    
    %% 租户中心
    system_tenant {
        bigint id PK
        varchar name
        tinyint status
        datetime expire_time
    }
    
    %% 用户中心
    member_user {
        bigint id PK
        bigint tenant_id FK
        varchar username
        varchar mobile
        decimal gold
        decimal balance
        bigint level_id FK
    }
    
    member_level {
        bigint id PK
        bigint tenant_id FK
        varchar name
        int level
        decimal discount_percent
    }
    
    %% 支付中心
    pay_order {
        bigint id PK
        bigint tenant_id FK
        bigint app_id FK
        varchar merchant_order_id
        int price
        tinyint status
    }
    
    pay_wallet {
        bigint id PK
        bigint tenant_id FK
        bigint user_id FK
        int balance
        int total_expense
    }
    
    %% 内容中心
    author_article {
        bigint id PK
        bigint tenant_id FK
        bigint author_id FK
        varchar title
        decimal price
        tinyint status
    }
    
    %% 核心关系
    system_tenant ||--o{ member_user : "tenant_id"
    system_tenant ||--o{ pay_order : "tenant_id"
    system_tenant ||--o{ author_article : "tenant_id"
    
    member_user ||--|| pay_wallet : "user_id"
    member_user ||--o{ pay_order : "user_id"
    member_user ||--o{ author_article : "author_id"
    member_level ||--o{ member_user : "level_id"
