package com.pinwan.mir.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付渠道枚举
 */
@Getter
@AllArgsConstructor
public enum ThirdPayEnum {

    SDCF(1, "山东畅风","success"),
    JIUKA(2, "久卡支付","OK"),
    FB(3, "FB支付","success"),
    SQB(4, "收钱吧支付","success"),
            ;
    private final Integer type;
    private final String name;
    private final String notifySuccess;


}
