package com.pinwan.mir.framework.common.enums.match;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MatchPositionEnum {

    FORWARD("F", "前锋"),
    MIDFIELDER("M", "中场"),
    DEFENDER("D", "后卫"),
    GOALKEEPER("G", "守门员"),
    UNKNOWN("", "未知");

    private String value;

    private String desc;

    // 通过value获取desc 匹配不到的返回未知
    public static String getDescByValue(String value) {
        for (MatchPositionEnum positionEnum : MatchPositionEnum.values()) {
            if (positionEnum.getValue().equals(value)) {
                return positionEnum.getDesc();
            }
        }
        return MatchPositionEnum.UNKNOWN.getDesc();
    }

}
