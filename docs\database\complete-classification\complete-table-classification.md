# 数据库表完整分类报告

## 📊 概览

本报告对数据库中的所有表进行完整分类，不做任何裁剪，确保每个表都有明确的归属。

**总表数**: 190
**分析时间**: Thu Jul 10 18:09:17     2025

## 🏗️ 1. 平台框架表（Framework Tables）

**功能**: 系统核心框架功能，包含用户权限、租户管理、字典配置等

- **system_command**: 关键词与接口关联表 (0行)
- **system_dept**: 部门表 (17行)
- **system_dict_data**: 字典数据表 (451行)
- **system_dict_type**: 字典类型表 (101行)
- **system_mail_account**: 邮箱账号表 (4行)
- **system_mail_template**: 邮件模版表 (3行)
- **system_menu**: 菜单权限表 (887行)
- **system_notice**: 通知公告表 (3行)
- **system_notify_message**: 站内信消息表 (9行)
- **system_notify_template**: 站内信模板表 (0行)
- **system_oauth2_access_token**: OAuth2 访问令牌 (3250行)
- **system_oauth2_approve**: OAuth2 批准表 (0行)
- **system_oauth2_client**: OAuth2 客户端表 (4行)
- **system_oauth2_code**: OAuth2 授权码表 (0行)
- **system_oauth2_refresh_token**: OAuth2 刷新令牌 (1748行)
- **system_post**: 岗位信息表 (5行)
- **system_role**: 角色信息表 (10行)
- **system_role_menu**: 角色和菜单关联表 (1510行)
- **system_sensitive_word**: 敏感词 (0行)
- **system_sms_channel**: 短信渠道 (0行)
- **system_sms_code**: 手机验证码 (11行)
- **system_sms_template**: 短信模板 (13行)
- **system_social_client**: 社交客户端表 (5行)
- **system_social_user**: 社交用户表 (0行)
- **system_social_user_bind**: 社交绑定表 (0行)
- **system_tenant**: 租户表 (3行)
- **system_tenant_package**: 租户套餐表 (3行)
- **system_user_post**: 用户岗位表 (14行)
- **system_user_role**: 用户和角色关联表 (24行)
- **system_users**: 用户信息表 (22行)

## ⚽ 2. 足彩业务表（Football Business Tables）

**功能**: 足彩相关的核心业务功能

- **football_bd_odds**: 北单指数 (3283行)
- **football_bd_result**: 北单开奖结果 (4605行)
- **football_bd_sf_odds**: 北单胜负过关指数 (3774行)
- **football_bd_sf_result**: 北单胜负过关结果 (5398行)
- **football_bo_live_odds**: 实时指数 (198行)
- **football_company**: 公司列表 (494040行)
- **football_half_live_odds**: 半场实时指数 (304行)
- **football_issue**: 足球期数表 (0行)
- **football_jc_odds**: 竞彩比赛指数 (947行)
- **football_jc_result**: 竞彩比赛结果 (1174行)
- **football_live_odds**: 实时指数 (334647行)
- **football_match**: 传统足彩比赛列表 (1092行)
- **football_match_result**: 传统足彩开奖结果 (0行)
- **football_tc_match**: 体彩比赛关联列表 (5485行)

## 🏆 3. 赛事信息表（Match Tables）

**功能**: 赛事、球队、比赛相关信息管理

- **match_category**: 赛事分类 (8行)
- **match_coach**: 教练 (127695行)
- **match_competition**: 赛事信息 (2325行)
- **match_country**: 国家 (212行)
- **match_future_record**: 未来赛程 (6032行)
- **match_history**: 比赛记录及盘口信息 (9138行)
- **match_lineup_detail**: 赛事阵容详情 (2517行)
- **match_list**: 比赛场次信息 (61651行)
- **match_live_info**: 比赛直播信息 (100行)
- **match_odds**: 比赛开盘信息 (42行)
- **match_player**: 球员 (0行)
- **match_player_info**: 比赛球队球员信息 (159行)
- **match_player_transfer**: 球队转会信息 (2928行)
- **match_point_rank**: 联赛积分排名 (4032行)
- **match_referee**: 裁判表 (4144行)
- **match_season**: 赛季信息 (0行)
- **match_stage**: 赛事阶段表 (38753行)
- **match_stats**: 比赛数据统计表 (7084行)
- **match_team**: 球队信息 (70924行)

## 👥 4. 会员功能表（Member Tables）

**功能**: 会员管理、积分、等级、签到等功能

- **member_address**:  (0行)
- **member_attention**: 用户关注表 (63行)
- **member_author_privilege**: 用户作者特权 (49行)
- **member_bind_record**: 会员绑定记录表 (0行)
- **member_config**:  (0行)
- **member_experience_record**:  (0行)
- **member_group**:  (0行)
- **member_level**:  (0行)
- **member_level_record**:  (0行)
- **member_match_attention**: 用户关注比赛信息 (14行)
- **member_match_chat**: 比赛场次聊天记录 (3行)
- **member_match_vote**: 比赛场次投票 (0行)
- **member_point_record**:  (0行)
- **member_privilege_log**: 用户特权使用记录 (11行)
- **member_settlement_info**: 用户提现账号信息 (10行)
- **member_sign_in_config**:  (0行)
- **member_sign_in_record**:  (0行)
- **member_tag**:  (0行)
- **member_user**: 用户端-用户表 (45行)
- **member_user_bak**:  (0行)
- **member_user_balance_logs**: 用户余额变更记录 (303行)
- **member_user_ex_balance_logs**: 用户推广余额变更记录 (27行)
- **member_user_gold_logs**: 用户金币变更记录 (345行)

## ⏰ 5. 作业调度表（Quartz Scheduler Tables）

**功能**: Quartz框架的作业调度基础设施

- **qrtz_blob_triggers**:  (0行)
- **qrtz_calendars**:  (0行)
- **qrtz_cron_triggers**:  (13行)
- **qrtz_fired_triggers**:  (0行)
- **qrtz_job_details**:  (13行)
- **qrtz_locks**:  (2行)
- **qrtz_paused_trigger_grps**:  (0行)
- **qrtz_scheduler_state**:  (2行)
- **qrtz_simple_triggers**:  (0行)
- **qrtz_simprop_triggers**:  (0行)
- **qrtz_triggers**:  (13行)

## 💰 6. 支付功能表（Payment Tables）

**功能**: 支付、钱包、订单相关功能

- **pay_app**:  (0行)
- **pay_bank_info**: 银行信息 (4行)
- **pay_channel**:  (0行)
- **pay_notify_log**:  (0行)
- **pay_notify_task**:  (0行)
- **pay_order**:  (0行)
- **pay_order_extension**:  (0行)
- **pay_refund**:  (0行)
- **pay_transfer**:  (0行)
- **pay_wallet**:  (0行)
- **pay_wallet_recharge**:  (0行)
- **pay_wallet_recharge_package**:  (2行)
- **pay_wallet_transaction**:  (0行)

## 💬 7. 微信功能表（WeChat Tables）

**功能**: 微信公众号、小程序相关功能

- **mp_account**: 公众号账号表 (7行)
- **mp_auto_reply**: 公众号消息自动回复表 (0行)
- **mp_click_logs**: 公众号关注点击记录了表 (6行)
- **mp_material**: 公众号素材表 (0行)
- **mp_menu**: 公众号菜单表 (4行)
- **mp_message**: 公众号消息表  (23行)
- **mp_mini_user**: 小程序用户表 (7行)
- **mp_other_even_logs**: 1w1公众扫码记录 (0行)
- **mp_pay_config_log**: 微信配置记录日志 (4行)
- **mp_tag**: 公众号标签表 (0行)
- **mp_template_config**: 微信模板配置表 (9行)
- **mp_user**: 公众号粉丝表 (25行)

## 🔧 8. 基础设施表（Infrastructure Tables）

**功能**: 文件管理、代码生成、配置管理等基础设施

- **infra_api_access_log**: API 访问日志表 (299703行)
- **infra_api_error_log**: 系统异常日志 (1254行)
- **infra_codegen_column**: 代码生成表字段定义 (396行)
- **infra_codegen_table**: 代码生成表定义 (38行)
- **infra_config**: 参数配置表 (7行)
- **infra_data_source_config**: 数据源配置表 (0行)
- **infra_file**: 文件表 (16行)
- **infra_file_config**: 文件配置表 (3行)
- **infra_file_content**: 文件表 (0行)
- **infra_job**: 定时任务表 (24行)
- **infra_job_log**: 定时任务日志表 (243412行)

## 📝 9. 内容管理表（Content Tables）

**功能**: 文章、作者、内容相关管理

- **author_accomplishment**: 作者战绩 (29行)
- **author_article**: 文章 (600行)
- **author_article_append**: 文章 (15行)
- **author_article_pv_logs**: 文章PV记录 (11268行)
- **author_audit**: 作者审核列表 (18行)
- **author_audit_copy1**: 作者审核列表 (9行)
- **author_commission_rate**: 作者分成提现设置表 (3行)
- **author_day_report**: 作者日报 (2654行)
- **author_hit_rate**: 作者命中率记录表 (33行)
- **author_info**:  (5行)
- **author_privilege_set**: 作者特权设置 (63行)
- **author_withdraw_logs**: 作者提现表 (33行)
- **gold_order**: 金币订单表 (183行)

## 🏢 10. 企业微信表（Work WeChat Tables）

**功能**: 企业微信相关功能

- **wx_external_contact**: 企微客服客户信息对照表 (9287行)
- **wx_external_contact_way_config**:  (0行)
- **wx_work_setting**: 企微号配置 (2行)

## 🗑️ 11. Demo和测试表（Demo/Test Tables）

**功能**: 框架示例和测试用表，可以安全删除

- **pay_demo_order**:  (0行) ❌ 可删除
- **pay_demo_transfer**:  (0行) ❌ 可删除
- **yudao_demo01_contact**: 示例联系人表 (0行) ❌ 可删除
- **yudao_demo02_category**: 示例分类表 (6行) ❌ 可删除
- **yudao_demo03_course**: 学生课程表 (10行) ❌ 可删除
- **yudao_demo03_grade**: 学生班级表 (3行) ❌ 可删除
- **yudao_demo03_student**: 学生表 (3行) ❌ 可删除

## 📋 12. 日志表（Log Tables）

**功能**: 系统日志、操作记录等

- **author_article_pv_logs**: 文章PV记录 (11268行)
- **author_withdraw_logs**: 作者提现表 (33行)
- **infra_api_access_log**: API 访问日志表 (299703行)
- **infra_api_error_log**: 系统异常日志 (1254行)
- **infra_job_log**: 定时任务日志表 (243412行)
- **member_privilege_log**: 用户特权使用记录 (11行)
- **member_user_balance_logs**: 用户余额变更记录 (303行)
- **member_user_ex_balance_logs**: 用户推广余额变更记录 (27行)
- **member_user_gold_logs**: 用户金币变更记录 (345行)
- **mp_click_logs**: 公众号关注点击记录了表 (6行)
- **mp_other_even_logs**: 1w1公众扫码记录 (0行)
- **mp_pay_config_log**: 微信配置记录日志 (4行)
- **partner_invite_logs**: 合作伙伴邀请记录表 (3行)
- **partner_withdraw_logs**: 作者提现表 (9行)
- **pay_notify_log**:  (0行)
- **recommend_user_register_logs**: 文章推荐用户注册记录 (0行)
- **system_login_log**: 系统访问记录 (828行)
- **system_mail_log**: 邮件日志表 (0行)
- **system_operate_log**: 操作日志记录 V2 版本 (0行)
- **system_sms_log**: 短信日志 (5行)

## ❓ 13. 未明确分类的表（Unclassified Tables）

**功能**: 需要进一步确认功能的表

- **account_statistic
**:  ⚠️ 需确认
- **account_users
**:  ⚠️ 需确认
- **article_push_config
**:  ⚠️ 需确认
- **authors
**:  ⚠️ 需确认
- **broomer_match_scheme
**:  ⚠️ 需确认
- **broomers
**:  ⚠️ 需确认
- **guess_records
**:  ⚠️ 需确认
- **home_banner
**:  ⚠️ 需确认
- **matches
**:  ⚠️ 需确认
- **partner_audit
**:  ⚠️ 需确认
- **partner_commission_rate
**:  ⚠️ 需确认
- **partner_divide_config
**:  ⚠️ 需确认
- **partner_info
**:  ⚠️ 需确认
- **partner_settlement_info
**:  ⚠️ 需确认
- **play_type
**:  ⚠️ 需确认
- **playing_method
**:  ⚠️ 需确认
- **privilege_order
**:  ⚠️ 需确认
- **push_amount_config
**:  ⚠️ 需确认
- **recommend_author
**:  ⚠️ 需确认
- **refund_order
**:  ⚠️ 需确认
- **report_operation_day
**:  ⚠️ 需确认
- **scheme_order
**:  ⚠️ 需确认
- **scheme_play
**:  ⚠️ 需确认
- **sys_configs
**:  ⚠️ 需确认
- **third_pay_channel
**:  ⚠️ 需确认
- **third_pay_sqb_config
**:  ⚠️ 需确认
- **wecom_setting
**:  ⚠️ 需确认

## 📊 分类统计

| 分类 | 表数量 | 说明 |
|------|--------|------|
| 平台框架表 | 1 | 系统核心功能 |
| 足彩业务表 | 14 | 足彩核心业务 |
| 赛事信息表 | 19 | 赛事比赛数据 |
| 会员功能表 | 23 | 会员管理功能 |
| 作业调度表 | 11 | Quartz调度器 |
| 支付功能表 | 1 | 支付钱包功能 |
| 微信功能表 | 12 | 微信公众号功能 |
| 基础设施表 | 11 | 基础设施功能 |
| 内容管理表 | 13 | 内容管理功能 |
| 企业微信表 | 3 | 企业微信功能 |
| Demo测试表 | 7 | 可删除 |
| 日志表 | 20 | 日志记录 |
| 未分类表 | 27 | 需要确认 |
| **总计** | **190** | **所有表** |

## 🎯 重构建议

### 按功能模块组织SQL脚本
1. **01-framework-tables.sql** - 平台框架表
2. **02-football-business-tables.sql** - 足彩业务表
3. **03-match-tables.sql** - 赛事信息表
4. **04-member-tables.sql** - 会员功能表
5. **05-quartz-tables.sql** - 作业调度表
6. **06-payment-tables.sql** - 支付功能表
7. **07-wechat-tables.sql** - 微信功能表
8. **08-infrastructure-tables.sql** - 基础设施表
9. **09-content-tables.sql** - 内容管理表
10. **10-unclassified-tables.sql** - 未分类表
11. **99-basic-data.sql** - 基础数据

### 注意事项
- ✅ **不做裁剪**: 保留所有业务表，确保功能完整
- ⚠️ **Demo表**: 可以安全删除，不影响业务功能
- 🔍 **未分类表**: 需要进一步分析确认功能
- 📋 **日志表**: 根据监控需求决定是否保留
