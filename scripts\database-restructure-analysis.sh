#!/bin/bash

# =============================================
# 数据库重构分析脚本
# 分析现有数据库，按功能模块分类，生成新建库脚本
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "数据库重构分析工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database/restructure
mkdir -p sql/restructure

log_info "分析现有数据库结构..."

# 获取所有表的详细信息
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF' > /tmp/all_tables_detail.txt
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS,
    CREATE_TIME,
    UPDATE_TIME,
    DATA_LENGTH,
    INDEX_LENGTH
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_TYPE = 'BASE TABLE'
ORDER BY 
    TABLE_NAME;
EOF

# 获取所有表的字段信息
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF' > /tmp/all_columns_detail.txt
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT,
    COLUMN_KEY,
    EXTRA
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
ORDER BY 
    TABLE_NAME, ORDINAL_POSITION;
EOF

# 获取外键关系
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF' > /tmp/foreign_keys_detail.txt
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME,
    CONSTRAINT_NAME
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND REFERENCED_TABLE_NAME IS NOT NULL;
EOF

# 获取索引信息
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF' > /tmp/indexes_detail.txt
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE,
    SEQ_IN_INDEX
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME != 'PRIMARY'
ORDER BY 
    TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
EOF

log_info "生成数据库重构分析报告..."

# 生成重构分析报告
cat > docs/database/restructure/database-restructure-analysis.md << 'EOF'
# 数据库重构分析报告

## 📊 概览

本报告分析现有数据库结构，按功能模块分类表，识别未使用的表，并提供数据库重构方案。

## 🎯 重构目标

1. **模块化分离**: 按平台框架/后台前端/应用功能分类
2. **清理冗余**: 移除未使用的Demo表和测试表
3. **优化结构**: 重新组织表结构和关系
4. **标准化**: 统一命名规范和字段标准

## 📋 现有数据库分析

### 数据库基本信息

EOF

# 添加数据库统计信息
TOTAL_TABLES=$(wc -l < /tmp/all_tables_detail.txt)
TOTAL_SIZE=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT ROUND(SUM(DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE();" \
    --batch --skip-column-names 2>/dev/null)

echo "- **数据库名称**: $DB_NAME" >> docs/database/restructure/database-restructure-analysis.md
echo "- **总表数**: $TOTAL_TABLES" >> docs/database/restructure/database-restructure-analysis.md
echo "- **数据库大小**: ${TOTAL_SIZE}MB" >> docs/database/restructure/database-restructure-analysis.md
echo "- **分析时间**: $(date)" >> docs/database/restructure/database-restructure-analysis.md
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 按模块分类表
echo "## 🏗️ 表分类分析" >> docs/database/restructure/database-restructure-analysis.md
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 1. 平台框架表
echo "### 1. 平台框架表（Framework Tables）" >> docs/database/restructure/database-restructure-analysis.md
echo "" >> docs/database/restructure/database-restructure-analysis.md
echo "系统核心框架功能，所有应用共享：" >> docs/database/restructure/database-restructure-analysis.md
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 获取框架表
FRAMEWORK_TABLES=$(awk '{print $1}' /tmp/all_tables_detail.txt | grep -E "^system_|^infra_" | grep -vE "demo|test")

if [ -n "$FRAMEWORK_TABLES" ]; then
    echo "$FRAMEWORK_TABLES" | while read table_name; do
        TABLE_INFO=$(grep "^$table_name" /tmp/all_tables_detail.txt | awk '{print $2 " (" $3 "行)"}')
        echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/database-restructure-analysis.md
    done
fi

echo "" >> docs/database/restructure/database-restructure-analysis.md

# 2. 后台前端表
echo "### 2. 后台前端表（Admin Frontend Tables）" >> docs/database/restructure/database-restructure-analysis.md
echo "" >> docs/database/restructure/database-restructure-analysis.md
echo "后台管理系统专用表：" >> docs/database/restructure/database-restructure-analysis.md
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 定义后台前端核心表
ADMIN_CORE_TABLES=("system_users" "system_role" "system_menu" "system_user_role" "system_role_menu" "system_dept" "system_post" "system_dict_type" "system_dict_data" "system_notice")

for table in "${ADMIN_CORE_TABLES[@]}"; do
    if awk '{print $1}' /tmp/all_tables_detail.txt | grep -q "^$table$"; then
        TABLE_INFO=$(grep "^$table" /tmp/all_tables_detail.txt | awk '{print $2 " (" $3 "行)"}')
        echo "- **$table**: $TABLE_INFO" >> docs/database/restructure/database-restructure-analysis.md
    fi
done

echo "" >> docs/database/restructure/database-restructure-analysis.md

# 3. 应用功能表
echo "### 3. 应用功能表（Application Tables）" >> docs/database/restructure/database-restructure-analysis.md
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 3.1 会员模块
echo "#### 3.1 会员模块" >> docs/database/restructure/database-restructure-analysis.md
MEMBER_TABLES=$(awk '{print $1}' /tmp/all_tables_detail.txt | grep "^member_")
if [ -n "$MEMBER_TABLES" ]; then
    echo "$MEMBER_TABLES" | while read table_name; do
        TABLE_INFO=$(grep "^$table_name" /tmp/all_tables_detail.txt | awk '{print $2 " (" $3 "行)"}')
        echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/database-restructure-analysis.md
    done
fi
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 3.2 支付模块
echo "#### 3.2 支付模块" >> docs/database/restructure/database-restructure-analysis.md
PAY_TABLES=$(awk '{print $1}' /tmp/all_tables_detail.txt | grep "^pay_" | grep -v demo)
if [ -n "$PAY_TABLES" ]; then
    echo "$PAY_TABLES" | while read table_name; do
        TABLE_INFO=$(grep "^$table_name" /tmp/all_tables_detail.txt | awk '{print $2 " (" $3 "行)"}')
        echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/database-restructure-analysis.md
    done
fi
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 3.3 业务模块
echo "#### 3.3 业务模块" >> docs/database/restructure/database-restructure-analysis.md
BUSINESS_TABLES=$(awk '{print $1}' /tmp/all_tables_detail.txt | grep -E "^author_|^match_|^banner|^gold_")
if [ -n "$BUSINESS_TABLES" ]; then
    echo "$BUSINESS_TABLES" | while read table_name; do
        TABLE_INFO=$(grep "^$table_name" /tmp/all_tables_detail.txt | awk '{print $2 " (" $3 "行)"}')
        echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/database-restructure-analysis.md
    done
fi
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 3.4 微信模块
echo "#### 3.4 微信模块" >> docs/database/restructure/database-restructure-analysis.md
MP_TABLES=$(awk '{print $1}' /tmp/all_tables_detail.txt | grep "^mp_")
if [ -n "$MP_TABLES" ]; then
    echo "$MP_TABLES" | while read table_name; do
        TABLE_INFO=$(grep "^$table_name" /tmp/all_tables_detail.txt | awk '{print $2 " (" $3 "行)"}')
        echo "- **$table_name**: $TABLE_INFO" >> docs/database/restructure/database-restructure-analysis.md
    done
fi
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 4. 未使用的表
echo "### 4. 未使用的表（Unused Tables）" >> docs/database/restructure/database-restructure-analysis.md
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 4.1 Demo表
echo "#### 4.1 Demo/测试表" >> docs/database/restructure/database-restructure-analysis.md
DEMO_TABLES=$(awk '{print $1}' /tmp/all_tables_detail.txt | grep -iE "demo|test|sample|temp")
if [ -n "$DEMO_TABLES" ]; then
    echo "$DEMO_TABLES" | while read table_name; do
        TABLE_INFO=$(grep "^$table_name" /tmp/all_tables_detail.txt | awk '{print $2 " (" $3 "行)"}')
        echo "- **$table_name**: $TABLE_INFO ❌ 可删除" >> docs/database/restructure/database-restructure-analysis.md
    done
else
    echo "✅ 未发现Demo/测试表" >> docs/database/restructure/database-restructure-analysis.md
fi
echo "" >> docs/database/restructure/database-restructure-analysis.md

# 4.2 日志表
echo "#### 4.2 日志表" >> docs/database/restructure/database-restructure-analysis.md
LOG_TABLES=$(awk '{print $1}' /tmp/all_tables_detail.txt | grep -E "log|_api_|_job_")
if [ -n "$LOG_TABLES" ]; then
    echo "$LOG_TABLES" | while read table_name; do
        TABLE_INFO=$(grep "^$table_name" /tmp/all_tables_detail.txt | awk '{print $2 " (" $3 "行)"}')
        echo "- **$table_name**: $TABLE_INFO ⚠️ 可考虑删除" >> docs/database/restructure/database-restructure-analysis.md
    done
else
    echo "✅ 未发现独立的日志表" >> docs/database/restructure/database-restructure-analysis.md
fi

log_success "数据库重构分析完成: docs/database/restructure/database-restructure-analysis.md"

# 清理临时文件
rm -f /tmp/all_tables_detail.txt /tmp/all_columns_detail.txt /tmp/foreign_keys_detail.txt /tmp/indexes_detail.txt

echo
echo "========================================"
echo "数据库重构分析完成！"
echo "========================================"
echo "📋 docs/database/restructure/database-restructure-analysis.md - 重构分析报告"
echo "========================================"
