-- =============================================
-- 足球彩票系统 - 平台框架表
-- 系统核心框架功能，所有应用共享
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 租户管理
-- =============================================

-- 租户表
CREATE TABLE IF NOT EXISTS `system_tenant` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '租户名',
    `contact_user_id` bigint DEFAULT NULL COMMENT '联系人的用户编号',
    `contact_name` varchar(30) NOT NULL COMMENT '联系人',
    `contact_mobile` varchar(500) DEFAULT NULL COMMENT '联系手机',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
    `website` varchar(256) DEFAULT '' COMMENT '绑定域名',
    `package_id` bigint NOT NULL COMMENT '租户套餐编号',
    `expire_time` datetime NOT NULL COMMENT '过期时间',
    `account_count` int NOT NULL COMMENT '账号数量',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';

-- =============================================
-- 用户权限管理
-- =============================================

-- 用户信息表
CREATE TABLE IF NOT EXISTS `system_users` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `username` varchar(30) NOT NULL COMMENT '用户账号',
    `password` varchar(100) DEFAULT '' COMMENT '密码',
    `nickname` varchar(30) NOT NULL COMMENT '用户昵称',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
    `post_ids` varchar(255) DEFAULT NULL COMMENT '岗位编号数组',
    `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
    `mobile` varchar(11) DEFAULT '' COMMENT '手机号码',
    `sex` tinyint DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
    `avatar` varchar(512) DEFAULT '' COMMENT '头像地址',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
    `login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
    `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`,`update_time`,`tenant_id`),
    KEY `idx_mobile` (`mobile`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 角色信息表
CREATE TABLE IF NOT EXISTS `system_role` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL COMMENT '角色名称',
    `code` varchar(100) NOT NULL COMMENT '角色权限字符串',
    `sort` int NOT NULL COMMENT '显示顺序',
    `data_scope` tinyint NOT NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
    `data_scope_dept_ids` varchar(500) DEFAULT '' COMMENT '数据范围(指定部门数组)',
    `status` tinyint NOT NULL COMMENT '角色状态（0正常 1停用）',
    `type` tinyint NOT NULL COMMENT '角色类型',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色信息表';

-- 菜单权限表
CREATE TABLE IF NOT EXISTS `system_menu` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `name` varchar(50) NOT NULL COMMENT '菜单名称',
    `permission` varchar(100) DEFAULT '' COMMENT '权限标识',
    `type` tinyint NOT NULL COMMENT '菜单类型（1目录 2菜单 3按钮）',
    `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
    `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父菜单ID',
    `path` varchar(200) DEFAULT '' COMMENT '路由地址',
    `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
    `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
    `component_name` varchar(255) DEFAULT NULL COMMENT '组件名',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
    `visible` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否可见',
    `keep_alive` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否缓存',
    `always_show` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否总是显示',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单权限表';

-- 用户和角色关联表
CREATE TABLE IF NOT EXISTS `system_user_role` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户和角色关联表';

-- 角色和菜单关联表
CREATE TABLE IF NOT EXISTS `system_role_menu` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `menu_id` bigint NOT NULL COMMENT '菜单ID',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_menu_id` (`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和菜单关联表';

-- =============================================
-- 字典管理
-- =============================================

-- 字典类型表
CREATE TABLE IF NOT EXISTS `system_dict_type` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
    `name` varchar(100) DEFAULT '' COMMENT '字典名称',
    `type` varchar(100) DEFAULT '' COMMENT '字典类型',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典类型表';

-- 字典数据表
CREATE TABLE IF NOT EXISTS `system_dict_data` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
    `sort` int NOT NULL DEFAULT '0' COMMENT '字典排序',
    `label` varchar(100) DEFAULT '' COMMENT '字典标签',
    `value` varchar(100) DEFAULT '' COMMENT '字典键值',
    `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `color_type` varchar(100) DEFAULT '' COMMENT '颜色类型',
    `css_class` varchar(100) DEFAULT '' COMMENT 'css 样式',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_dict_type` (`dict_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典数据表';

SET FOREIGN_KEY_CHECKS = 1;
