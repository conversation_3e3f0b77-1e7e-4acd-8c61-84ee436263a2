-- =============================================
-- 足球彩票系统 - 足彩业务表
-- 包含所有football_*和match_*相关的足彩核心业务表
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 足彩业务表（football_*）
-- =============================================

-- 注意：根据数据库分析报告，当前数据库中没有发现football_*表
-- 这些表可能在其他环境中存在，或者使用了不同的命名规范
-- 如果需要足彩相关功能，可以在这里添加相应的表结构

-- =============================================
-- 赛事信息表（match_*）
-- =============================================

-- 赛事分类表
CREATE TABLE IF NOT EXISTS `match_category` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `name` varchar(100) NOT NULL COMMENT '分类名称',
    `code` varchar(50) NOT NULL COMMENT '分类编码',
    `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='赛事分类';

-- 国家表
CREATE TABLE IF NOT EXISTS `match_country` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '国家ID',
    `name` varchar(100) NOT NULL COMMENT '国家名称',
    `code` varchar(10) NOT NULL COMMENT '国家编码',
    `flag_url` varchar(255) DEFAULT NULL COMMENT '国旗图片URL',
    `continent` varchar(50) DEFAULT NULL COMMENT '所属大洲',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国家';

-- 赛事信息表
CREATE TABLE IF NOT EXISTS `match_competition` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '赛事ID',
    `name` varchar(200) NOT NULL COMMENT '赛事名称',
    `short_name` varchar(100) DEFAULT NULL COMMENT '赛事简称',
    `category_id` bigint NOT NULL COMMENT '分类ID',
    `country_id` bigint DEFAULT NULL COMMENT '国家ID',
    `level` tinyint NOT NULL DEFAULT '0' COMMENT '赛事级别',
    `logo_url` varchar(255) DEFAULT NULL COMMENT '赛事logo',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_country_id` (`country_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='赛事信息';

-- 赛季信息表
CREATE TABLE IF NOT EXISTS `match_season` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '赛季ID',
    `competition_id` bigint NOT NULL COMMENT '赛事ID',
    `name` varchar(100) NOT NULL COMMENT '赛季名称',
    `start_date` date NOT NULL COMMENT '开始日期',
    `end_date` date NOT NULL COMMENT '结束日期',
    `current_season` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否当前赛季',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_competition_id` (`competition_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='赛季信息';

-- 赛事阶段表
CREATE TABLE IF NOT EXISTS `match_stage` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '阶段ID',
    `season_id` bigint NOT NULL COMMENT '赛季ID',
    `name` varchar(100) NOT NULL COMMENT '阶段名称',
    `stage_type` tinyint NOT NULL COMMENT '阶段类型',
    `start_date` date DEFAULT NULL COMMENT '开始日期',
    `end_date` date DEFAULT NULL COMMENT '结束日期',
    `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_season_id` (`season_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='赛事阶段表';

-- 球队信息表
CREATE TABLE IF NOT EXISTS `match_team` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '球队ID',
    `name` varchar(200) NOT NULL COMMENT '球队名称',
    `short_name` varchar(100) DEFAULT NULL COMMENT '球队简称',
    `country_id` bigint DEFAULT NULL COMMENT '国家ID',
    `city` varchar(100) DEFAULT NULL COMMENT '所在城市',
    `founded_year` int DEFAULT NULL COMMENT '成立年份',
    `logo_url` varchar(255) DEFAULT NULL COMMENT '球队logo',
    `home_stadium` varchar(200) DEFAULT NULL COMMENT '主场球场',
    `official_website` varchar(255) DEFAULT NULL COMMENT '官方网站',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_country_id` (`country_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='球队信息';

-- 球员表
CREATE TABLE IF NOT EXISTS `match_player` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '球员ID',
    `name` varchar(200) NOT NULL COMMENT '球员姓名',
    `english_name` varchar(200) DEFAULT NULL COMMENT '英文姓名',
    `country_id` bigint DEFAULT NULL COMMENT '国籍ID',
    `birth_date` date DEFAULT NULL COMMENT '出生日期',
    `height` int DEFAULT NULL COMMENT '身高(cm)',
    `weight` int DEFAULT NULL COMMENT '体重(kg)',
    `position` varchar(50) DEFAULT NULL COMMENT '位置',
    `jersey_number` int DEFAULT NULL COMMENT '球衣号码',
    `market_value` decimal(15,2) DEFAULT NULL COMMENT '身价',
    `photo_url` varchar(255) DEFAULT NULL COMMENT '照片URL',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_country_id` (`country_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='球员';

-- 比赛球队球员信息表
CREATE TABLE IF NOT EXISTS `match_player_info` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `match_id` bigint NOT NULL COMMENT '比赛ID',
    `team_id` bigint NOT NULL COMMENT '球队ID',
    `player_id` bigint NOT NULL COMMENT '球员ID',
    `jersey_number` int DEFAULT NULL COMMENT '球衣号码',
    `position` varchar(50) DEFAULT NULL COMMENT '位置',
    `is_starter` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否首发',
    `substitute_time` int DEFAULT NULL COMMENT '替补上场时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_match_id` (`match_id`),
    KEY `idx_team_id` (`team_id`),
    KEY `idx_player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛球队球员信息';

-- 球队转会信息表
CREATE TABLE IF NOT EXISTS `match_player_transfer` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '转会ID',
    `player_id` bigint NOT NULL COMMENT '球员ID',
    `from_team_id` bigint DEFAULT NULL COMMENT '转出球队ID',
    `to_team_id` bigint NOT NULL COMMENT '转入球队ID',
    `transfer_date` date NOT NULL COMMENT '转会日期',
    `transfer_fee` decimal(15,2) DEFAULT NULL COMMENT '转会费',
    `transfer_type` tinyint NOT NULL COMMENT '转会类型',
    `contract_end_date` date DEFAULT NULL COMMENT '合同结束日期',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_player_id` (`player_id`),
    KEY `idx_from_team_id` (`from_team_id`),
    KEY `idx_to_team_id` (`to_team_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='球队转会信息';

-- 教练表
CREATE TABLE IF NOT EXISTS `match_coach` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '教练ID',
    `name` varchar(200) NOT NULL COMMENT '教练姓名',
    `english_name` varchar(200) DEFAULT NULL COMMENT '英文姓名',
    `country_id` bigint DEFAULT NULL COMMENT '国籍ID',
    `birth_date` date DEFAULT NULL COMMENT '出生日期',
    `coaching_style` varchar(100) DEFAULT NULL COMMENT '执教风格',
    `photo_url` varchar(255) DEFAULT NULL COMMENT '照片URL',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_country_id` (`country_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='教练';

-- 裁判表
CREATE TABLE IF NOT EXISTS `match_referee` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '裁判ID',
    `name` varchar(200) NOT NULL COMMENT '裁判姓名',
    `english_name` varchar(200) DEFAULT NULL COMMENT '英文姓名',
    `country_id` bigint DEFAULT NULL COMMENT '国籍ID',
    `birth_date` date DEFAULT NULL COMMENT '出生日期',
    `referee_level` varchar(50) DEFAULT NULL COMMENT '裁判级别',
    `photo_url` varchar(255) DEFAULT NULL COMMENT '照片URL',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_country_id` (`country_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='裁判表';

-- 比赛场次信息表
CREATE TABLE IF NOT EXISTS `match_list` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '比赛ID',
    `competition_id` bigint NOT NULL COMMENT '赛事ID',
    `season_id` bigint NOT NULL COMMENT '赛季ID',
    `stage_id` bigint DEFAULT NULL COMMENT '阶段ID',
    `home_team_id` bigint NOT NULL COMMENT '主队ID',
    `away_team_id` bigint NOT NULL COMMENT '客队ID',
    `match_date` datetime NOT NULL COMMENT '比赛时间',
    `match_status` tinyint NOT NULL DEFAULT '0' COMMENT '比赛状态',
    `home_score` int DEFAULT NULL COMMENT '主队得分',
    `away_score` int DEFAULT NULL COMMENT '客队得分',
    `half_home_score` int DEFAULT NULL COMMENT '半场主队得分',
    `half_away_score` int DEFAULT NULL COMMENT '半场客队得分',
    `referee_id` bigint DEFAULT NULL COMMENT '主裁判ID',
    `stadium` varchar(200) DEFAULT NULL COMMENT '比赛场地',
    `weather` varchar(100) DEFAULT NULL COMMENT '天气',
    `temperature` int DEFAULT NULL COMMENT '温度',
    `attendance` int DEFAULT NULL COMMENT '观众人数',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_competition_id` (`competition_id`),
    KEY `idx_season_id` (`season_id`),
    KEY `idx_home_team_id` (`home_team_id`),
    KEY `idx_away_team_id` (`away_team_id`),
    KEY `idx_match_date` (`match_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛场次信息';

-- 比赛记录及盘口信息表
CREATE TABLE IF NOT EXISTS `match_history` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `match_id` bigint NOT NULL COMMENT '比赛ID',
    `odds_type` varchar(50) NOT NULL COMMENT '盘口类型',
    `home_odds` decimal(10,3) DEFAULT NULL COMMENT '主队赔率',
    `draw_odds` decimal(10,3) DEFAULT NULL COMMENT '平局赔率',
    `away_odds` decimal(10,3) DEFAULT NULL COMMENT '客队赔率',
    `handicap` decimal(5,2) DEFAULT NULL COMMENT '让球数',
    `over_under` decimal(5,2) DEFAULT NULL COMMENT '大小球',
    `record_time` datetime NOT NULL COMMENT '记录时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_match_id` (`match_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛记录及盘口信息';

-- 比赛开盘信息表
CREATE TABLE IF NOT EXISTS `match_odds` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `match_id` bigint NOT NULL COMMENT '比赛ID',
    `bookmaker` varchar(100) NOT NULL COMMENT '博彩公司',
    `odds_type` varchar(50) NOT NULL COMMENT '盘口类型',
    `home_odds` decimal(10,3) DEFAULT NULL COMMENT '主队赔率',
    `draw_odds` decimal(10,3) DEFAULT NULL COMMENT '平局赔率',
    `away_odds` decimal(10,3) DEFAULT NULL COMMENT '客队赔率',
    `handicap` decimal(5,2) DEFAULT NULL COMMENT '让球数',
    `over_under` decimal(5,2) DEFAULT NULL COMMENT '大小球',
    `update_time_odds` datetime DEFAULT NULL COMMENT '赔率更新时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_match_id` (`match_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛开盘信息';

-- 比赛数据统计表
CREATE TABLE IF NOT EXISTS `match_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `match_id` bigint NOT NULL COMMENT '比赛ID',
    `team_id` bigint NOT NULL COMMENT '球队ID',
    `possession` int DEFAULT NULL COMMENT '控球率',
    `shots` int DEFAULT NULL COMMENT '射门次数',
    `shots_on_target` int DEFAULT NULL COMMENT '射正次数',
    `corners` int DEFAULT NULL COMMENT '角球次数',
    `fouls` int DEFAULT NULL COMMENT '犯规次数',
    `yellow_cards` int DEFAULT NULL COMMENT '黄牌数',
    `red_cards` int DEFAULT NULL COMMENT '红牌数',
    `offsides` int DEFAULT NULL COMMENT '越位次数',
    `passes` int DEFAULT NULL COMMENT '传球次数',
    `pass_accuracy` decimal(5,2) DEFAULT NULL COMMENT '传球成功率',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_match_id` (`match_id`),
    KEY `idx_team_id` (`team_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛数据统计表';

-- 赛事阵容详情表
CREATE TABLE IF NOT EXISTS `match_lineup_detail` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `match_id` bigint NOT NULL COMMENT '比赛ID',
    `team_id` bigint NOT NULL COMMENT '球队ID',
    `player_id` bigint NOT NULL COMMENT '球员ID',
    `position` varchar(50) DEFAULT NULL COMMENT '位置',
    `jersey_number` int DEFAULT NULL COMMENT '球衣号码',
    `is_starter` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否首发',
    `substitute_in_time` int DEFAULT NULL COMMENT '替补上场时间',
    `substitute_out_time` int DEFAULT NULL COMMENT '替补下场时间',
    `goals` int DEFAULT '0' COMMENT '进球数',
    `assists` int DEFAULT '0' COMMENT '助攻数',
    `yellow_cards` int DEFAULT '0' COMMENT '黄牌数',
    `red_cards` int DEFAULT '0' COMMENT '红牌数',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_match_id` (`match_id`),
    KEY `idx_team_id` (`team_id`),
    KEY `idx_player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='赛事阵容详情';

-- 未来赛程表
CREATE TABLE IF NOT EXISTS `match_future_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `competition_id` bigint NOT NULL COMMENT '赛事ID',
    `season_id` bigint NOT NULL COMMENT '赛季ID',
    `home_team_id` bigint NOT NULL COMMENT '主队ID',
    `away_team_id` bigint NOT NULL COMMENT '客队ID',
    `match_date` datetime NOT NULL COMMENT '比赛时间',
    `round_number` int DEFAULT NULL COMMENT '轮次',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_competition_id` (`competition_id`),
    KEY `idx_season_id` (`season_id`),
    KEY `idx_match_date` (`match_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='未来赛程';

-- 联赛积分排名表
CREATE TABLE IF NOT EXISTS `match_point_rank` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `competition_id` bigint NOT NULL COMMENT '赛事ID',
    `season_id` bigint NOT NULL COMMENT '赛季ID',
    `team_id` bigint NOT NULL COMMENT '球队ID',
    `rank_position` int NOT NULL COMMENT '排名',
    `matches_played` int NOT NULL DEFAULT '0' COMMENT '已赛场次',
    `wins` int NOT NULL DEFAULT '0' COMMENT '胜场',
    `draws` int NOT NULL DEFAULT '0' COMMENT '平场',
    `losses` int NOT NULL DEFAULT '0' COMMENT '负场',
    `goals_for` int NOT NULL DEFAULT '0' COMMENT '进球数',
    `goals_against` int NOT NULL DEFAULT '0' COMMENT '失球数',
    `goal_difference` int NOT NULL DEFAULT '0' COMMENT '净胜球',
    `points` int NOT NULL DEFAULT '0' COMMENT '积分',
    `update_date` date NOT NULL COMMENT '更新日期',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_competition_season` (`competition_id`,`season_id`),
    KEY `idx_team_id` (`team_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联赛积分排名';

-- 比赛直播信息表
CREATE TABLE IF NOT EXISTS `match_live_info` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `match_id` bigint NOT NULL COMMENT '比赛ID',
    `live_url` varchar(500) DEFAULT NULL COMMENT '直播链接',
    `live_platform` varchar(100) DEFAULT NULL COMMENT '直播平台',
    `live_status` tinyint NOT NULL DEFAULT '0' COMMENT '直播状态',
    `viewer_count` int DEFAULT '0' COMMENT '观看人数',
    `start_time` datetime DEFAULT NULL COMMENT '直播开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '直播结束时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_match_id` (`match_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛直播信息';

SET FOREIGN_KEY_CHECKS = 1;
