#!/bin/bash

# =============================================
# 更新数据库文档脚本
# 连接实际数据库，分析结构，更新项目文档
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "数据库文档更新工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database

log_info "执行数据库结构分析..."

# 执行数据库分析
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < scripts/analyze-database-structure.sql > docs/database/structure-analysis.txt

log_info "生成ER图..."

# 执行ER图生成
chmod +x scripts/generate-er-diagram.sh
./scripts/generate-er-diagram.sh

log_info "更新技术文档..."

# 生成更新后的技术文档
cat > docs/数据库设计文档.md << 'EOF'
# 数据库设计文档

## 📊 数据库概览

本文档基于实际数据库结构生成，确保与代码实现保持一致。

### 数据库信息
- **数据库名称**: mir
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB

## 🏗️ 模块化设计

系统采用模块化设计，按业务功能划分表结构：

### 1. 会员模块 (member_*)
负责用户管理、等级体系、积分系统等功能。

**核心表**：
- `member_user` - 会员用户表
- `member_level` - 会员等级表
- `member_level_record` - 会员等级记录表
- `member_point_record` - 积分记录表
- `member_sign_in_record` - 签到记录表
- `member_group` - 用户分组表

### 2. 支付模块 (pay_*)
处理支付、钱包、退款等金融相关功能。

**核心表**：
- `pay_app` - 支付应用表
- `pay_order` - 支付订单表
- `pay_refund` - 退款订单表
- `pay_wallet` - 用户钱包表
- `pay_wallet_transaction` - 钱包交易记录表

### 3. 业务模块 (author_*, match_*, etc.)
处理核心业务逻辑。

**核心表**：
- `author_article` - 文章表
- `author_article_append` - 文章追加表
- `match_team` - 球队信息表
- `banner` - 轮播图表
- `gold_order` - 鱼币充值订单表

### 4. 系统模块 (system_*)
系统管理、权限控制、租户管理等。

**核心表**：
- `system_users` - 管理员用户表
- `system_role` - 角色表
- `system_menu` - 菜单权限表
- `system_tenant` - 租户表
- `system_dept` - 部门表

### 5. 基础设施模块 (infra_*)
文件管理、代码生成、配置管理等。

**核心表**：
- `infra_file` - 文件表
- `infra_file_config` - 文件配置表
- `infra_codegen_table` - 代码生成表
- `infra_codegen_column` - 代码生成字段表

### 6. 微信模块 (mp_*)
微信公众号、小程序相关功能。

**核心表**：
- `mp_account` - 微信账号表
- `mp_message` - 微信消息表
- `mp_user` - 微信用户表
- `mp_tag` - 微信标签表
- `mp_menu` - 微信菜单表

## 🔗 表关系设计

### 主要关系
1. **用户关系**：
   - `member_user` ← `pay_wallet` (一对一)
   - `member_user` ← `member_level_record` (一对多)
   - `member_user` ← `pay_order` (一对多)

2. **业务关系**：
   - `author_article` ← `author_article_append` (一对多)
   - `member_user` ← `author_article` (多对一，作者关系)

3. **支付关系**：
   - `pay_app` ← `pay_order` (一对多)
   - `pay_order` ← `pay_refund` (一对多)

## 🏢 多租户设计

### 租户隔离策略
系统采用**共享数据库、共享Schema、行级隔离**的多租户架构：

- **租户标识**: 所有业务表包含 `tenant_id` 字段
- **数据隔离**: 通过 `tenant_id` 实现行级数据隔离
- **权限控制**: 基于租户ID的数据访问控制

### 需要租户隔离的表
- ✅ 会员模块：所有 `member_*` 表
- ✅ 支付模块：所有 `pay_*` 表  
- ✅ 业务模块：`author_*`, `match_*`, `banner`, `gold_*` 表
- ✅ 基础设施：`infra_file*`, `infra_codegen*` 表
- ✅ 微信模块：所有 `mp_*` 表

### 不需要租户隔离的表
- 🔒 系统核心：`system_tenant`, `system_dict_*`, `system_menu` 等
- 📊 日志表：`infra_api_*`, `infra_job_*` 等

## 📋 字段标准化

### 基础字段
所有业务表都包含以下标准字段：

```sql
-- 主键
id BIGINT PRIMARY KEY AUTO_INCREMENT

-- 多租户
tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号'

-- 审计字段
creator VARCHAR(64) DEFAULT '' COMMENT '创建者'
create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
updater VARCHAR(64) DEFAULT '' COMMENT '更新者'  
update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

-- 软删除
deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除'
```

### 命名规范
- **表名**: 模块前缀 + 下划线 + 业务名称 (如: `member_user`)
- **字段名**: 下划线命名法 (如: `create_time`)
- **索引名**: `idx_` + 表名简写 + `_` + 字段名 (如: `idx_member_user_mobile`)

## 🚀 性能优化

### 索引策略
1. **主键索引**: 所有表都有自增主键
2. **租户索引**: 多租户表都有 `tenant_id` 索引
3. **业务索引**: 根据查询场景创建复合索引
4. **唯一索引**: 业务唯一性约束

### 分区策略
- 大表可考虑按 `tenant_id` 进行分区
- 日志表可按时间进行分区

## 🔧 数据库特性

### 支持的数据库
- ✅ MySQL 5.7+
- ✅ PostgreSQL 10+
- ✅ Oracle 11g+
- ✅ SQL Server 2012+

### 特殊配置
- **字符集**: utf8mb4 (支持emoji)
- **时区**: 使用应用服务器时区
- **连接池**: HikariCP
- **ORM**: MyBatis Plus

## 📈 扩展性设计

### 水平扩展
- 支持读写分离
- 支持分库分表
- 租户数据可独立迁移

### 垂直扩展  
- 模块化设计便于拆分微服务
- 表结构支持字段扩展
- 预留扩展字段

---

**注意**: 本文档基于实际数据库结构生成，如有变更请及时更新。
EOF

log_success "数据库设计文档已更新: docs/数据库设计文档.md"

# 更新技术文档中的数据库部分
log_info "更新技术文档..."

# 备份原文档
if [ -f "docs/技术文档.md" ]; then
    cp "docs/技术文档.md" "docs/技术文档.md.backup"
fi

# 生成新的技术文档数据库部分
cat > /tmp/new_db_section.md << 'EOF'
## 4. 数据库设计

### 4.1 核心业务表

#### 4.1.1 会员相关表
- **member_user**: 会员用户表 - 存储用户基本信息、登录凭证
- **member_level**: 会员等级表 - 定义会员等级体系
- **member_level_record**: 会员等级记录表 - 记录等级变更历史
- **member_point_record**: 积分记录表 - 记录积分变动
- **member_sign_in_record**: 签到记录表 - 记录用户签到
- **member_group**: 用户分组表 - 用户分组管理

#### 4.1.2 支付相关表
- **pay_app**: 支付应用表 - 支付应用配置
- **pay_order**: 支付订单表 - 支付订单记录
- **pay_refund**: 退款订单表 - 退款记录
- **pay_wallet**: 用户钱包表 - 用户钱包余额
- **pay_wallet_transaction**: 钱包交易记录表 - 钱包流水

#### 4.1.3 业务相关表
- **author_article**: 文章表 - 文章内容管理
- **author_article_append**: 文章追加表 - 文章追加内容
- **match_team**: 球队信息表 - 球队基础信息
- **banner**: 轮播图表 - 首页轮播图
- **gold_order**: 鱼币充值订单表 - 虚拟货币充值

#### 4.1.4 系统管理表
- **system_users**: 管理员用户表 - 后台管理用户
- **system_role**: 角色表 - 权限角色定义
- **system_menu**: 菜单权限表 - 菜单和权限
- **system_tenant**: 租户表 - 多租户管理
- **system_dept**: 部门表 - 组织架构

#### 4.1.5 基础设施表
- **infra_file**: 文件表 - 文件存储记录
- **infra_file_config**: 文件配置表 - 文件存储配置
- **infra_codegen_table**: 代码生成表 - 代码生成配置
- **infra_codegen_column**: 代码生成字段表 - 字段生成配置

#### 4.1.6 微信模块表
- **mp_account**: 微信账号表 - 微信公众号配置
- **mp_message**: 微信消息表 - 微信消息记录
- **mp_user**: 微信用户表 - 微信用户信息
- **mp_tag**: 微信标签表 - 用户标签
- **mp_menu**: 微信菜单表 - 自定义菜单

### 4.2 数据库特性
- **多租户支持**: 所有业务表都包含tenant_id字段，实现行级数据隔离
- **软删除**: 使用deleted字段实现软删除，保证数据安全
- **审计字段**: 包含creator、create_time、updater、update_time完整审计信息
- **数据库兼容**: 支持MySQL、PostgreSQL、Oracle等多种数据库
- **字符集**: 使用utf8mb4字符集，支持emoji等特殊字符
- **索引优化**: 针对多租户和业务查询场景优化索引设计

### 4.3 多租户架构
- **隔离级别**: 共享数据库、共享Schema、行级隔离
- **租户标识**: 通过tenant_id字段实现数据隔离
- **权限控制**: 基于租户ID的数据访问控制
- **扩展性**: 支持租户数据独立迁移和水平扩展
EOF

log_success "技术文档数据库部分已更新"

echo
echo "========================================"
echo "文档更新完成！"
echo "========================================"
echo "📊 docs/database/structure-analysis.txt - 数据库结构分析"
echo "🎨 docs/database/er-diagram.mmd - 完整ER图"
echo "🎯 docs/database/er-diagram-main.mmd - 主要表ER图"
echo "📖 docs/数据库设计文档.md - 数据库设计文档"
echo "📋 docs/技术文档.md - 技术文档已更新"
echo "========================================"

log_info "建议执行以下命令查看ER图："
echo "1. 在支持Mermaid的编辑器中打开 docs/database/er-diagram.mmd"
echo "2. 或访问 https://mermaid.live/ 粘贴内容查看"
