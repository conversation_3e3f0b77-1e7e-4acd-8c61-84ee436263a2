# 简单的MySQL连接测试

$DB_HOST = "localhost"
$DB_PORT = "3306"
$DB_NAME = "sports_gaming"
$DB_USER = "root"
$DB_PASSWORD = "123456"

Write-Host "Testing MySQL connection..."
Write-Host "Host: $DB_HOST"
Write-Host "Database: $DB_NAME"
Write-Host "User: $DB_USER"
Write-Host ""

# Test basic connection
Write-Host "=== Test 1: Basic Connection ==="
$result = & mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -e "SELECT 1;" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Connection successful"
} else {
    Write-Host "✗ Connection failed"
    exit 1
}

# Test show tables
Write-Host ""
Write-Host "=== Test 2: Show Tables ==="
$tables = & mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "SHOW TABLES;" --batch --skip-column-names 2>$null
if ($tables) {
    $tableArray = $tables -split "`n" | Where-Object { $_.Trim() -ne "" }
    Write-Host "✓ Found $($tableArray.Count) tables"
    Write-Host "First 5 tables:"
    $tableArray[0..4] | ForEach-Object { Write-Host "  - $_" }
    $firstTable = $tableArray[0]
} else {
    Write-Host "✗ No tables found"
    exit 1
}

# Test describe table
Write-Host ""
Write-Host "=== Test 3: Describe Table ==="
Write-Host "Testing table: $firstTable"
$describe = & mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "DESCRIBE $firstTable;" 2>$null
if ($describe) {
    Write-Host "✓ DESCRIBE query successful"
    Write-Host $describe
} else {
    Write-Host "✗ DESCRIBE query failed"
}

# Test INFORMATION_SCHEMA
Write-Host ""
Write-Host "=== Test 4: INFORMATION_SCHEMA Query ==="
$columns = & mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "SELECT COLUMN_NAME, COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$firstTable' LIMIT 3;" --batch --skip-column-names 2>$null
if ($columns) {
    Write-Host "✓ INFORMATION_SCHEMA query successful"
    Write-Host "Columns:"
    $columns -split "`n" | Where-Object { $_.Trim() -ne "" } | ForEach-Object {
        $parts = $_ -split "`t"
        Write-Host "  - $($parts[0]): $($parts[1])"
    }
} else {
    Write-Host "✗ INFORMATION_SCHEMA query failed"
}

Write-Host ""
Write-Host "=== Test Complete ==="
