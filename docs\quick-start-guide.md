# 通用数据库分析器快速开始指南

## 🚀 5分钟快速上手

### 1. 准备工作
确保您的系统已安装：
- MySQL客户端
- Bash环境（Linux/macOS/Windows Git Bash）

### 2. 基础使用
```bash
# 进入项目目录
cd /path/to/football-lottery

# 最简单的使用方式
bash scripts/universal-database-analyzer.sh \
    --database=your_database \
    --user=root \
    --password=your_password
```

### 3. 完整功能使用
```bash
# 生成所有类型的分析报告
bash scripts/universal-database-analyzer.sh \
    --database=your_database \
    --user=root \
    --password=your_password \
    --with-structure \
    --with-er-diagram \
    --with-data-dict \
    --output=./analysis-results
```

### 4. 使用配置文件
```bash
# 创建配置文件
cat > config/database.conf << EOF
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=your_database
WITH_STRUCTURE=true
WITH_ER_DIAGRAM=true
WITH_DATA_DICT=true
EOF

# 使用配置文件运行
bash scripts/universal-database-analyzer.sh --config=config/database.conf
```

## 📊 输出文件说明

运行完成后，您将得到以下文件：

### 1. 基础分析报告
**文件**: `database-analysis-YYYYMMDD-HHMMSS.md`
**内容**: 
- 数据库概览
- 智能表分类
- 统计信息
- 建议和总结

### 2. 表结构详情（可选）
**文件**: `table-structure-YYYYMMDD-HHMMSS.md`
**内容**:
- 每个表的详细字段信息
- 字段类型、约束、默认值
- 字段注释和说明

### 3. 数据字典（可选）
**文件**: `data-dictionary-YYYYMMDD-HHMMSS.md`
**内容**:
- 表概览统计
- 完整的字段清单
- 标准化的文档格式

### 4. ER图（可选）
**文件**: `er-diagram-YYYYMMDD-HHMMSS.mmd`
**内容**:
- Mermaid格式的实体关系图
- 表结构和字段定义
- 自动推断的表关系

## 🎨 查看ER图

### 在线查看
1. 访问 [Mermaid Live Editor](https://mermaid.live/)
2. 复制生成的`.mmd`文件内容
3. 粘贴到编辑器中查看

### VS Code查看
1. 安装"Mermaid Preview"扩展
2. 在VS Code中打开`.mmd`文件
3. 使用预览功能查看

### 生成图片
```bash
# 安装mermaid-cli
npm install -g @mermaid-js/mermaid-cli

# 生成PNG图片
mmdc -i er-diagram.mmd -o er-diagram.png
```

## 📋 命令行参数

| 参数 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--database` | `-d` | 数据库名称（必需） | `-d mydb` |
| `--user` | `-u` | 数据库用户 | `-u root` |
| `--password` | `-p` | 数据库密码 | `-p mypass` |
| `--host` | `-H` | 数据库主机 | `-H localhost` |
| `--port` | `-P` | 数据库端口 | `-P 3306` |
| `--output` | `-o` | 输出目录 | `-o ./results` |
| `--config` | `-c` | 配置文件 | `-c db.conf` |
| `--with-structure` | | 生成表结构详情 | |
| `--with-er-diagram` | | 生成ER图 | |
| `--with-data-dict` | | 生成数据字典 | |
| `--help` | `-h` | 显示帮助信息 | |

## 🔧 项目专用脚本

### 足球彩票项目
```bash
# 使用项目专用脚本（自动读取配置）
bash scripts/analyze-football-lottery-db.sh
```

### 简化版分析器
```bash
# 使用修复版分析器（处理少量表）
bash scripts/fixed-table-analyzer.sh
```

## ⚠️ 注意事项

### 权限要求
- 需要数据库的`SELECT`权限
- 需要访问`INFORMATION_SCHEMA`的权限

### 性能考虑
- 大型数据库（>100个表）可能需要较长时间
- 建议在非高峰时段运行
- 可以使用`--output`指定输出目录避免文件冲突

### 字符编码
- 确保数据库使用UTF-8编码
- 确保终端支持UTF-8显示

## 🆘 常见问题

### Q: 提示"无法获取字段信息"？
A: 检查数据库连接和权限，确保用户有访问INFORMATION_SCHEMA的权限。

### Q: 生成的ER图关系不准确？
A: ER图基于字段命名约定推断关系（如`user_id`关联到`user`表），如果命名不规范可能不准确。

### Q: 中文注释显示乱码？
A: 确保数据库和表使用UTF-8字符集，并且终端支持UTF-8显示。

### Q: 脚本运行很慢？
A: 对于大型数据库，可以先运行基础分析（不加可选参数），然后根据需要生成详细报告。

## 📞 获取帮助

```bash
# 查看帮助信息
bash scripts/universal-database-analyzer.sh --help

# 调试MySQL连接
bash scripts/debug-mysql-queries.sh

# 测试单个表
bash scripts/test-single-table.sh
```

## 🎯 最佳实践

1. **首次使用** - 先运行基础分析了解数据库结构
2. **定期生成** - 建议每周或每月生成一次文档
3. **版本控制** - 将生成的文档加入版本控制跟踪变化
4. **团队协作** - 将数据字典分享给团队成员
5. **持续优化** - 根据ER图优化数据库设计

---

**快速开始完成！** 🎉

现在您已经掌握了通用数据库分析器的基本使用方法。更多高级功能请参考完整文档。
