# 足球彩票系统API接口文档

## 1. 接口概述

### 1.1 基本信息
- **接口地址**: http://localhost:48080
- **接口协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 统一响应格式
```json
{
  "code": 0,
  "data": {},
  "msg": "操作成功"
}
```

### 1.3 状态码说明
| 状态码 | 说明 |
|--------|------|
| 0 | 成功 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 500 | 系统错误 |

## 2. 认证授权

### 2.1 登录接口
**接口地址**: `POST /admin-api/system/auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "admin123",
  "captchaVerification": "1234"
}
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "userId": 1,
    "accessToken": "eyJhbGciOiJIUzI1NiJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiJ9...",
    "expiresTime": 1640995200000
  },
  "msg": "登录成功"
}
```

### 2.2 获取用户信息
**接口地址**: `GET /admin-api/system/auth/get-permission-info`

**请求头**:
```
Authorization: Bearer {accessToken}
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "avatar": "http://example.com/avatar.jpg"
    },
    "roles": ["admin"],
    "permissions": ["system:user:query", "system:user:create"]
  },
  "msg": "获取成功"
}
```

## 3. 用户管理

### 3.1 用户列表
**接口地址**: `GET /admin-api/system/user/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNo | int | 是 | 页码 |
| pageSize | int | 是 | 每页数量 |
| username | string | 否 | 用户名 |
| mobile | string | 否 | 手机号 |
| status | int | 否 | 状态 |

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "username": "admin",
        "nickname": "管理员",
        "email": "<EMAIL>",
        "mobile": "13800138000",
        "status": 0,
        "createTime": "2023-01-01 00:00:00"
      }
    ]
  },
  "msg": "查询成功"
}
```

### 3.2 创建用户
**接口地址**: `POST /admin-api/system/user/create`

**请求参数**:
```json
{
  "username": "testuser",
  "nickname": "测试用户",
  "password": "123456",
  "email": "<EMAIL>",
  "mobile": "13800138001",
  "sex": 1,
  "deptId": 1,
  "postIds": [1, 2],
  "roleIds": [2]
}
```

### 3.3 更新用户
**接口地址**: `PUT /admin-api/system/user/update`

### 3.4 删除用户
**接口地址**: `DELETE /admin-api/system/user/delete`

## 4. 会员管理

### 4.1 会员列表
**接口地址**: `GET /admin-api/member/user/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNo | int | 是 | 页码 |
| pageSize | int | 是 | 每页数量 |
| nickname | string | 否 | 昵称 |
| mobile | string | 否 | 手机号 |

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "total": 1000,
    "list": [
      {
        "id": 1,
        "nickname": "用户001",
        "mobile": "13800138000",
        "gold": 100.00,
        "balance": 50.00,
        "status": 0,
        "registerTime": "2023-01-01 00:00:00"
      }
    ]
  },
  "msg": "查询成功"
}
```

### 4.2 会员详情
**接口地址**: `GET /admin-api/member/user/get`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 会员ID |

### 4.3 会员等级管理
**接口地址**: `GET /admin-api/member/level/list`

**响应示例**:
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "青铜会员",
      "level": 1,
      "experience": 0,
      "discountPercent": 100,
      "icon": "bronze.png",
      "status": 0
    },
    {
      "id": 2,
      "name": "白银会员",
      "level": 2,
      "experience": 1000,
      "discountPercent": 95,
      "icon": "silver.png",
      "status": 0
    }
  ],
  "msg": "查询成功"
}
```

## 5. 支付管理

### 5.1 支付订单列表
**接口地址**: `GET /admin-api/pay/order/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNo | int | 是 | 页码 |
| pageSize | int | 是 | 每页数量 |
| merchantOrderId | string | 否 | 商户订单号 |
| status | int | 否 | 订单状态 |

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "total": 500,
    "list": [
      {
        "id": 1,
        "merchantOrderId": "ORDER_20230101_001",
        "subject": "鱼币充值",
        "price": 10000,
        "status": 20,
        "payTime": "2023-01-01 12:00:00",
        "createTime": "2023-01-01 11:55:00"
      }
    ]
  },
  "msg": "查询成功"
}
```

### 5.2 创建支付订单
**接口地址**: `POST /admin-api/pay/order/submit`

**请求参数**:
```json
{
  "appId": 1,
  "merchantOrderId": "ORDER_20230101_002",
  "subject": "鱼币充值",
  "body": "充值100鱼币",
  "price": 10000,
  "userIp": "***********",
  "channelCode": "alipay_pc"
}
```

### 5.3 钱包余额查询
**接口地址**: `GET /admin-api/pay/wallet/get`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | long | 是 | 用户ID |
| userType | int | 是 | 用户类型 |

## 6. 系统管理

### 6.1 菜单管理
**接口地址**: `GET /admin-api/system/menu/list`

**响应示例**:
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "系统管理",
      "permission": "",
      "type": 1,
      "sort": 1,
      "parentId": 0,
      "path": "/system",
      "icon": "system",
      "status": 0,
      "children": [
        {
          "id": 2,
          "name": "用户管理",
          "permission": "system:user:query",
          "type": 2,
          "sort": 1,
          "parentId": 1,
          "path": "user",
          "component": "system/user/index",
          "status": 0
        }
      ]
    }
  ],
  "msg": "查询成功"
}
```

### 6.2 角色管理
**接口地址**: `GET /admin-api/system/role/page`

### 6.3 字典管理
**接口地址**: `GET /admin-api/system/dict-type/page`

## 7. 文件管理

### 7.1 文件上传
**接口地址**: `POST /admin-api/infra/file/upload`

**请求参数**: multipart/form-data
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | file | 是 | 上传文件 |
| path | string | 否 | 存储路径 |

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "configId": 1,
    "name": "avatar.jpg",
    "path": "2023/01/01/avatar.jpg",
    "url": "http://example.com/2023/01/01/avatar.jpg",
    "size": 102400,
    "type": "image/jpeg"
  },
  "msg": "上传成功"
}
```

## 8. 错误码说明

### 8.1 系统错误码
| 错误码 | 说明 |
|--------|------|
| 1001001000 | 用户不存在 |
| 1001001001 | 密码错误 |
| 1001001002 | 账号被禁用 |
| 1001002000 | 角色不存在 |
| 1001003000 | 菜单不存在 |

### 8.2 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1002001000 | 会员不存在 |
| 1002001001 | 余额不足 |
| 1003001000 | 支付订单不存在 |
| 1003001001 | 订单已支付 |

## 9. 接口调用示例

### 9.1 JavaScript示例
```javascript
// 登录
const loginResponse = await fetch('/admin-api/system/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'admin123'
  })
});

const loginData = await loginResponse.json();
const token = loginData.data.accessToken;

// 获取用户列表
const userResponse = await fetch('/admin-api/system/user/page?pageNo=1&pageSize=10', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const userData = await userResponse.json();
```

### 9.2 Java示例
```java
// 使用RestTemplate调用接口
RestTemplate restTemplate = new RestTemplate();

// 设置请求头
HttpHeaders headers = new HttpHeaders();
headers.setContentType(MediaType.APPLICATION_JSON);
headers.setBearerAuth(token);

// 创建请求实体
HttpEntity<String> entity = new HttpEntity<>(headers);

// 发送请求
ResponseEntity<String> response = restTemplate.exchange(
    "http://localhost:48080/admin-api/system/user/page?pageNo=1&pageSize=10",
    HttpMethod.GET,
    entity,
    String.class
);
```

---

**注意事项**:
1. 所有接口都需要在请求头中携带有效的访问令牌
2. 分页查询的页码从1开始
3. 时间格式统一使用 yyyy-MM-dd HH:mm:ss
4. 金额单位统一使用分（整数）
5. 文件上传大小限制为10MB
