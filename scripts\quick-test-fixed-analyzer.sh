#!/bin/bash

# =============================================
# 快速测试修复后的数据库分析器
# =============================================

echo "========================================"
echo "快速测试修复后的数据库分析器"
echo "========================================"

# 设置数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

echo "数据库: $DB_NAME"
echo

# 清理之前的测试结果
rm -rf test-analysis-fixed
mkdir -p test-analysis-fixed

echo "=== 运行修复后的分析器 ==="
./scripts/universal-database-analyzer.sh \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --user="$DB_USER" \
    --password="$DB_PASSWORD" \
    --database="$DB_NAME" \
    --output="./test-analysis-fixed" \
    --with-structure \
    --with-er-diagram \
    --with-data-dict

if [ $? -eq 0 ]; then
    echo "✅ 分析器运行成功"
    echo
    
    # 检查生成的文件
    echo "=== 检查生成的文件 ==="
    ls -la test-analysis-fixed/
    echo
    
    # 检查表结构文件
    STRUCTURE_FILE=$(ls test-analysis-fixed/table-structure-*.md 2>/dev/null | head -1)
    if [ -f "$STRUCTURE_FILE" ]; then
        echo "=== 检查表结构文件 ==="
        echo "文件: $STRUCTURE_FILE"
        
        # 检查第一个表的字段数据
        echo "第一个表的字段信息:"
        sed -n '/### 表:/,/---/p' "$STRUCTURE_FILE" | head -20
        
        # 检查是否有格式问题
        if grep -q "| [^-].*| [^-].*| [^-].*| [^-].*| [^-].*| [^-].*|" "$STRUCTURE_FILE"; then
            echo "✅ 表结构格式正确"
        else
            echo "❌ 表结构格式有问题"
        fi
        echo
    fi
    
    # 检查数据字典文件
    DICT_FILE=$(ls test-analysis-fixed/data-dictionary-*.md 2>/dev/null | head -1)
    if [ -f "$DICT_FILE" ]; then
        echo "=== 检查数据字典文件 ==="
        echo "文件: $DICT_FILE"
        
        # 检查表概览部分
        echo "表概览信息:"
        sed -n '/| 序号 | 表名 | 注释 | 字段数 | 记录数 |/,/^$/p' "$DICT_FILE" | head -10
        
        # 检查是否有正确的数据
        if grep -q "| [0-9]* | [a-zA-Z_]* | .* | [0-9]* | [0-9]* |" "$DICT_FILE"; then
            echo "✅ 数据字典格式正确"
        else
            echo "❌ 数据字典格式有问题"
        fi
        echo
    fi
    
    # 检查ER图文件
    ER_FILE=$(ls test-analysis-fixed/er-diagram-*.mmd 2>/dev/null | head -1)
    if [ -f "$ER_FILE" ]; then
        echo "=== 检查ER图文件 ==="
        echo "文件: $ER_FILE"
        
        # 检查第一个表的定义
        echo "第一个表的ER定义:"
        sed -n '/^    [a-zA-Z_]* {$/,/^    }$/p' "$ER_FILE" | head -15
        
        # 检查是否有字段定义
        if grep -q "string\|int\|varchar\|bigint\|decimal" "$ER_FILE"; then
            echo "✅ ER图包含字段定义"
        else
            echo "❌ ER图缺少字段定义"
        fi
        echo
    fi
    
    echo "=== 测试总结 ==="
    echo "✅ 分析器运行成功"
    echo "📁 输出目录: test-analysis-fixed/"
    echo "📊 生成文件数: $(ls test-analysis-fixed/ | wc -l)"
    
else
    echo "❌ 分析器运行失败"
    exit 1
fi

echo "========================================"
echo "测试完成！"
echo "========================================"
