# PowerShell版本的MySQL测试脚本

# 设置数据库配置
$DB_HOST = "localhost"
$DB_PORT = "3306"
$DB_NAME = "sports_gaming"
$DB_USER = "root"
$DB_PASSWORD = "123456"

Write-Host "测试数据库连接..."
Write-Host "主机: ${DB_HOST}:${DB_PORT}"
Write-Host "数据库: $DB_NAME"
Write-Host "用户: $DB_USER"
Write-Host ""

# 测试基本连接
Write-Host "=== 测试1: 基本连接 ==="
$testConnection = mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 连接成功"
} else {
    Write-Host "❌ 连接失败"
    exit 1
}

# 测试表列表
Write-Host ""
Write-Host "=== 测试2: 获取表列表 ==="
$tables = mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SHOW TABLES;" --batch --skip-column-names 2>$null
if ($tables) {
    $tableCount = ($tables -split "`n").Count
    Write-Host "✅ 发现 $tableCount 个表"
    Write-Host "前5个表:"
    ($tables -split "`n")[0..4] | ForEach-Object { Write-Host "  - $_" }
} else {
    Write-Host "❌ 未发现表"
    exit 1
}

# 测试第一个表的字段信息
$firstTable = ($tables -split "`n")[0]
Write-Host ""
Write-Host "=== 测试3: 获取表字段信息 ==="
Write-Host "测试表: $firstTable"

# 方法1: INFORMATION_SCHEMA
Write-Host ""
Write-Host "方法1: INFORMATION_SCHEMA查询"
$columns1 = mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, COLUMN_KEY, COLUMN_DEFAULT, COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$firstTable' ORDER BY ORDINAL_POSITION;" --batch --skip-column-names 2>$null

if ($columns1) {
    Write-Host "✅ 成功获取字段信息"
    Write-Host "字段数量: $(($columns1 -split "`n").Count)"
    Write-Host "前3个字段:"
    ($columns1 -split "`n")[0..2] | ForEach-Object {
        $fields = $_ -split "`t"
        Write-Host "  - 字段: $($fields[0]), 类型: $($fields[1]), 注释: $($fields[5])"
    }
} else {
    Write-Host "❌ 无法获取字段信息"
}

# 方法2: DESCRIBE
Write-Host ""
Write-Host "方法2: DESCRIBE查询"
$columns2 = mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "DESCRIBE $firstTable;" 2>$null

if ($columns2) {
    Write-Host "✅ DESCRIBE查询成功"
    Write-Host $columns2
} else {
    Write-Host "❌ DESCRIBE查询失败"
}

# 测试表注释
Write-Host ""
Write-Host "=== 测试4: 获取表注释 ==="
$tableComment = mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$firstTable';" --batch --skip-column-names 2>$null

if ($tableComment) {
    Write-Host "✅ 表注释: $tableComment"
} else {
    Write-Host "❌ 无法获取表注释"
}

Write-Host ""
Write-Host "=== 测试完成 ==="
