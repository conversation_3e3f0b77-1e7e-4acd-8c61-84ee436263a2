#!/bin/bash

# =============================================
# 测试MySQL查询语句
# =============================================

echo "测试MySQL查询语句"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="sports_gaming"
DB_USER="root"
DB_PASSWORD="123456"

table_name="account_statistic"

echo "测试表: $table_name"

# 测试1: 调试脚本中成功的查询
echo
echo "=== 测试1: 调试脚本中成功的查询 ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COLUMN_NAME, COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' LIMIT 3;" \
    --batch --skip-column-names 2>/dev/null > ./test1.txt

echo "结果文件大小: $(wc -c < ./test1.txt) 字节"
echo "结果文件行数: $(wc -l < ./test1.txt) 行"
if [ -s "./test1.txt" ]; then
    echo "✅ 查询成功"
    cat ./test1.txt
else
    echo "❌ 查询失败"
fi

# 测试2: working脚本中的查询
echo
echo "=== 测试2: working脚本中的查询 ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, IFNULL(COLUMN_KEY,''), IFNULL(COLUMN_DEFAULT,''), IFNULL(COLUMN_COMMENT,'') FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' ORDER BY ORDINAL_POSITION;" \
    --batch --skip-column-names 2>/dev/null > ./test2.txt

echo "结果文件大小: $(wc -c < ./test2.txt) 字节"
echo "结果文件行数: $(wc -l < ./test2.txt) 行"
if [ -s "./test2.txt" ]; then
    echo "✅ 查询成功"
    echo "前3行:"
    head -3 ./test2.txt
else
    echo "❌ 查询失败"
fi

# 测试3: 分步查询
echo
echo "=== 测试3: 分步查询 ==="
echo "步骤1: 基本查询"
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' LIMIT 3;" \
    --batch --skip-column-names 2>/dev/null

echo "步骤2: 带IFNULL的查询"
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COLUMN_NAME, IFNULL(COLUMN_KEY,'') FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name' LIMIT 3;" \
    --batch --skip-column-names 2>/dev/null

# 清理
rm -f ./test1.txt ./test2.txt

echo
echo "测试完成"
