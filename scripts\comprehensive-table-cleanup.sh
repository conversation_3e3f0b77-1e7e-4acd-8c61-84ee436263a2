#!/bin/bash

# =============================================
# 综合表清理分析脚本
# 整合所有分析，提供完整的表清理建议
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "数据库表清理综合分析工具"
echo "========================================"

# 检查是否在项目根目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 请在项目根目录执行此脚本"
    exit 1
fi

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p docs/database

log_info "执行综合表分析..."

# 获取所有表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

TOTAL_TABLES=$(wc -l < /tmp/all_tables.txt)
log_info "数据库中共有 $TOTAL_TABLES 个表"

# 生成综合分析报告
cat > docs/database/comprehensive-cleanup-report.md << 'EOF'
# 数据库表清理综合报告

## 📊 概览

本报告提供数据库表的综合清理建议，帮助识别和删除不必要的表。

EOF

echo "**数据库**: $DB_NAME" >> docs/database/comprehensive-cleanup-report.md
echo "**总表数**: $TOTAL_TABLES" >> docs/database/comprehensive-cleanup-report.md
echo "**分析时间**: $(date)" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md

# 1. 分析Demo表
log_info "分析Demo表..."

echo "## 🎯 1. Demo和测试表" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md

# 基于已知的demo表模式
DEMO_PATTERNS=("mir_demo" "yudao_demo" "demo" "test" "sample" "temp" "example")
> /tmp/demo_tables.txt

for pattern in "${DEMO_PATTERNS[@]}"; do
    grep -i "$pattern" /tmp/all_tables.txt >> /tmp/demo_tables.txt 2>/dev/null || true
done

# 去重
sort /tmp/demo_tables.txt | uniq > /tmp/demo_tables_unique.txt
DEMO_COUNT=$(wc -l < /tmp/demo_tables_unique.txt 2>/dev/null || echo "0")

if [ $DEMO_COUNT -gt 0 ]; then
    echo "发现 $DEMO_COUNT 个Demo/测试表:" >> docs/database/comprehensive-cleanup-report.md
    echo "" >> docs/database/comprehensive-cleanup-report.md
    
    while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/comprehensive-cleanup-report.md
        fi
    done < /tmp/demo_tables_unique.txt
else
    echo "✅ 未发现Demo/测试表" >> docs/database/comprehensive-cleanup-report.md
fi

echo "" >> docs/database/comprehensive-cleanup-report.md

# 2. 分析空表
log_info "分析空表..."

echo "## 📭 2. 空表（无数据）" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md

EMPTY_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_TYPE = 'BASE TABLE' AND (TABLE_ROWS = 0 OR TABLE_ROWS IS NULL);" \
    --batch --skip-column-names 2>/dev/null)

if [ -n "$EMPTY_TABLES" ]; then
    EMPTY_COUNT=$(echo "$EMPTY_TABLES" | wc -l)
    echo "发现 $EMPTY_COUNT 个空表:" >> docs/database/comprehensive-cleanup-report.md
    echo "" >> docs/database/comprehensive-cleanup-report.md
    
    echo "$EMPTY_TABLES" | while read table_name; do
        if [ -n "$table_name" ]; then
            TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_COMMENT" >> docs/database/comprehensive-cleanup-report.md
        fi
    done
else
    echo "✅ 所有表都有数据" >> docs/database/comprehensive-cleanup-report.md
fi

echo "" >> docs/database/comprehensive-cleanup-report.md

# 3. 分析业务核心表
log_info "分析业务核心表..."

echo "## ✅ 3. 核心业务表（不能删除）" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md

# 定义核心业务表模式
CORE_PATTERNS=("member_" "pay_" "author_" "system_tenant" "system_users" "system_role" "system_menu")

echo "### 核心业务模块表" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md

for pattern in "${CORE_PATTERNS[@]}"; do
    CORE_TABLES=$(grep "^$pattern" /tmp/all_tables.txt 2>/dev/null || echo "")
    if [ -n "$CORE_TABLES" ]; then
        MODULE_NAME=""
        case "$pattern" in
            "member_") MODULE_NAME="会员模块" ;;
            "pay_") MODULE_NAME="支付模块" ;;
            "author_") MODULE_NAME="业务模块" ;;
            "system_") MODULE_NAME="系统模块" ;;
        esac
        
        if [ -n "$MODULE_NAME" ]; then
            echo "**$MODULE_NAME**:" >> docs/database/comprehensive-cleanup-report.md
            echo "$CORE_TABLES" | while read table; do
                echo "- $table" >> docs/database/comprehensive-cleanup-report.md
            done
            echo "" >> docs/database/comprehensive-cleanup-report.md
        fi
    fi
done

# 4. 生成清理建议
echo "## 🗑️ 4. 清理建议" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md

# 合并所有可删除的表
> /tmp/deletable_tables.txt

# 添加demo表
if [ -f "/tmp/demo_tables_unique.txt" ]; then
    cat /tmp/demo_tables_unique.txt >> /tmp/deletable_tables.txt
fi

# 添加空表（但排除核心表）
if [ -n "$EMPTY_TABLES" ]; then
    echo "$EMPTY_TABLES" | while read table; do
        # 检查是否是核心表
        IS_CORE=0
        for pattern in "${CORE_PATTERNS[@]}"; do
            if [[ "$table" == $pattern* ]]; then
                IS_CORE=1
                break
            fi
        done
        
        if [ $IS_CORE -eq 0 ]; then
            echo "$table" >> /tmp/deletable_tables.txt
        fi
    done
fi

# 去重并统计
sort /tmp/deletable_tables.txt | uniq > /tmp/deletable_tables_final.txt
DELETABLE_COUNT=$(wc -l < /tmp/deletable_tables_final.txt 2>/dev/null || echo "0")

if [ $DELETABLE_COUNT -gt 0 ]; then
    echo "### 建议删除的表" >> docs/database/comprehensive-cleanup-report.md
    echo "" >> docs/database/comprehensive-cleanup-report.md
    echo "发现 $DELETABLE_COUNT 个表建议删除:" >> docs/database/comprehensive-cleanup-report.md
    echo "" >> docs/database/comprehensive-cleanup-report.md
    
    # 按优先级分类
    echo "#### 高优先级（Demo/测试表）" >> docs/database/comprehensive-cleanup-report.md
    grep -iE "demo|test|sample|temp" /tmp/deletable_tables_final.txt | while read table; do
        echo "- **$table** - Demo/测试表，可安全删除" >> docs/database/comprehensive-cleanup-report.md
    done
    echo "" >> docs/database/comprehensive-cleanup-report.md
    
    echo "#### 中优先级（空表）" >> docs/database/comprehensive-cleanup-report.md
    grep -viE "demo|test|sample|temp" /tmp/deletable_tables_final.txt | while read table; do
        if [ -n "$table" ]; then
            echo "- **$table** - 空表，需确认后删除" >> docs/database/comprehensive-cleanup-report.md
        fi
    done
    echo "" >> docs/database/comprehensive-cleanup-report.md
    
    # 生成删除SQL
    echo "### 删除SQL脚本" >> docs/database/comprehensive-cleanup-report.md
    echo "" >> docs/database/comprehensive-cleanup-report.md
    echo '```sql' >> docs/database/comprehensive-cleanup-report.md
    echo "-- =============================================" >> docs/database/comprehensive-cleanup-report.md
    echo "-- 数据库表清理脚本" >> docs/database/comprehensive-cleanup-report.md
    echo "-- 执行前请务必备份数据库" >> docs/database/comprehensive-cleanup-report.md
    echo "-- =============================================" >> docs/database/comprehensive-cleanup-report.md
    echo "" >> docs/database/comprehensive-cleanup-report.md
    echo "SET SQL_SAFE_UPDATES = 0;" >> docs/database/comprehensive-cleanup-report.md
    echo "" >> docs/database/comprehensive-cleanup-report.md
    echo "-- Demo和测试表（优先删除）" >> docs/database/comprehensive-cleanup-report.md
    
    grep -iE "demo|test|sample|temp" /tmp/deletable_tables_final.txt | while read table; do
        if [ -n "$table" ]; then
            echo "DROP TABLE IF EXISTS $table;" >> docs/database/comprehensive-cleanup-report.md
        fi
    done
    
    echo "" >> docs/database/comprehensive-cleanup-report.md
    echo "-- 空表（需确认后删除）" >> docs/database/comprehensive-cleanup-report.md
    
    grep -viE "demo|test|sample|temp" /tmp/deletable_tables_final.txt | while read table; do
        if [ -n "$table" ]; then
            echo "-- DROP TABLE IF EXISTS $table; -- 请确认后取消注释" >> docs/database/comprehensive-cleanup-report.md
        fi
    done
    
    echo "" >> docs/database/comprehensive-cleanup-report.md
    echo "SET SQL_SAFE_UPDATES = 1;" >> docs/database/comprehensive-cleanup-report.md
    echo "" >> docs/database/comprehensive-cleanup-report.md
    echo "SELECT '表清理完成' AS message;" >> docs/database/comprehensive-cleanup-report.md
    echo '```' >> docs/database/comprehensive-cleanup-report.md
    
    # 生成独立的清理脚本
    cat > docs/database/comprehensive-cleanup.sql << 'EOF'
-- =============================================
-- 数据库表综合清理脚本
-- 注意：执行前请备份数据库
-- =============================================

SET SQL_SAFE_UPDATES = 0;

-- Demo和测试表（可安全删除）
EOF
    
    grep -iE "demo|test|sample|temp" /tmp/deletable_tables_final.txt | while read table; do
        if [ -n "$table" ]; then
            echo "DROP TABLE IF EXISTS $table;" >> docs/database/comprehensive-cleanup.sql
        fi
    done
    
    echo "" >> docs/database/comprehensive-cleanup.sql
    echo "-- 空表（需确认后删除）" >> docs/database/comprehensive-cleanup.sql
    
    grep -viE "demo|test|sample|temp" /tmp/deletable_tables_final.txt | while read table; do
        if [ -n "$table" ]; then
            echo "-- DROP TABLE IF EXISTS $table; -- 请确认后取消注释" >> docs/database/comprehensive-cleanup.sql
        fi
    done
    
    echo "" >> docs/database/comprehensive-cleanup.sql
    echo "SET SQL_SAFE_UPDATES = 1;" >> docs/database/comprehensive-cleanup.sql
    echo "" >> docs/database/comprehensive-cleanup.sql
    echo "SELECT '表清理完成' AS message;" >> docs/database/comprehensive-cleanup.sql
    
    log_success "清理脚本已生成: docs/database/comprehensive-cleanup.sql"
else
    echo "✅ 未发现需要清理的表" >> docs/database/comprehensive-cleanup-report.md
fi

echo "" >> docs/database/comprehensive-cleanup-report.md

# 5. 添加注意事项
echo "## ⚠️ 5. 重要注意事项" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md
echo "### 删除前必须执行的步骤" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md
echo "1. **备份数据库**" >> docs/database/comprehensive-cleanup-report.md
echo "   \`\`\`bash" >> docs/database/comprehensive-cleanup-report.md
echo "   mysqldump -u root -p $DB_NAME > backup_\$(date +%Y%m%d).sql" >> docs/database/comprehensive-cleanup-report.md
echo "   \`\`\`" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md
echo "2. **在测试环境验证**" >> docs/database/comprehensive-cleanup-report.md
echo "3. **检查代码依赖**" >> docs/database/comprehensive-cleanup-report.md
echo "4. **确认业务影响**" >> docs/database/comprehensive-cleanup-report.md
echo "5. **分批执行删除**" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md

echo "### 执行顺序建议" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md
echo "1. 先删除明确的Demo/测试表" >> docs/database/comprehensive-cleanup-report.md
echo "2. 再删除确认不需要的空表" >> docs/database/comprehensive-cleanup-report.md
echo "3. 最后清理相关的Java代码文件" >> docs/database/comprehensive-cleanup-report.md

# 6. 统计信息
echo "" >> docs/database/comprehensive-cleanup-report.md
echo "## 📊 6. 统计信息" >> docs/database/comprehensive-cleanup-report.md
echo "" >> docs/database/comprehensive-cleanup-report.md
echo "- **总表数**: $TOTAL_TABLES" >> docs/database/comprehensive-cleanup-report.md
echo "- **Demo/测试表**: $DEMO_COUNT" >> docs/database/comprehensive-cleanup-report.md
echo "- **空表数**: $(echo "$EMPTY_TABLES" | wc -l 2>/dev/null || echo "0")" >> docs/database/comprehensive-cleanup-report.md
echo "- **建议删除**: $DELETABLE_COUNT" >> docs/database/comprehensive-cleanup-report.md
echo "- **清理后剩余**: $(( TOTAL_TABLES - DELETABLE_COUNT ))" >> docs/database/comprehensive-cleanup-report.md

log_success "综合分析报告已生成: docs/database/comprehensive-cleanup-report.md"

# 清理临时文件
rm -f /tmp/all_tables.txt /tmp/demo_tables.txt /tmp/demo_tables_unique.txt /tmp/deletable_tables.txt /tmp/deletable_tables_final.txt

echo
echo "========================================"
echo "综合表清理分析完成！"
echo "========================================"
echo "📋 docs/database/comprehensive-cleanup-report.md - 综合分析报告"
if [ $DELETABLE_COUNT -gt 0 ]; then
    echo "🗑️ docs/database/comprehensive-cleanup.sql - 清理脚本"
    echo "📊 发现 $DELETABLE_COUNT 个表可以清理"
fi
echo "========================================"
