#!/bin/bash

# =============================================
# 简单状态检查脚本 - 避免复杂SQL
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "多租户改造状态检查"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

echo
echo "=== 检查已添加tenant_id字段的表 ==="

# 获取已添加tenant_id字段的表
TENANT_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'tenant_id' ORDER BY TABLE_NAME;" \
    --batch --skip-column-names 2>/dev/null)

if [ -n "$TENANT_TABLES" ]; then
    echo "已添加tenant_id字段的表："
    echo "$TENANT_TABLES" | while read table; do
        echo "  ✅ $table"
    done
    
    TENANT_COUNT=$(echo "$TENANT_TABLES" | wc -l)
    echo "总计: $TENANT_COUNT 个表"
else
    echo "❌ 没有表添加了tenant_id字段"
fi

echo
echo "=== 检查重要表状态 ==="

# 检查重要表
IMPORTANT_TABLES=("member_user" "pay_order" "article" "pay_wallet" "pay_app" "member_level")

for table in "${IMPORTANT_TABLES[@]}"; do
    # 检查表是否存在
    TABLE_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table';" \
        --batch --skip-column-names 2>/dev/null)
    
    if [ "$TABLE_EXISTS" = "1" ]; then
        # 检查是否有tenant_id字段
        TENANT_FIELD=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table' AND COLUMN_NAME = 'tenant_id';" \
            --batch --skip-column-names 2>/dev/null)
        
        if [ "$TENANT_FIELD" = "1" ]; then
            echo "  ✅ $table (表存在，tenant_id字段已添加)"
        else
            echo "  ⚠️  $table (表存在，但缺少tenant_id字段)"
        fi
    else
        echo "  ❌ $table (表不存在)"
    fi
done

echo
echo "=== 检查索引状态 ==="

# 检查tenant相关索引
TENANT_INDEXES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT CONCAT(TABLE_NAME, '.', INDEX_NAME) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND INDEX_NAME LIKE '%tenant%' GROUP BY TABLE_NAME, INDEX_NAME;" \
    --batch --skip-column-names 2>/dev/null)

if [ -n "$TENANT_INDEXES" ]; then
    echo "已创建的tenant相关索引："
    echo "$TENANT_INDEXES" | while read index; do
        echo "  ✅ $index"
    done
    
    INDEX_COUNT=$(echo "$TENANT_INDEXES" | wc -l)
    echo "总计: $INDEX_COUNT 个索引"
else
    echo "❌ 没有创建tenant相关索引"
fi

echo
echo "=== 检查系统租户表 ==="

# 检查system_tenant表
TENANT_TABLE_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'system_tenant';" \
    --batch --skip-column-names 2>/dev/null)

if [ "$TENANT_TABLE_EXISTS" = "1" ]; then
    echo "✅ system_tenant表存在"
    
    # 检查默认租户
    DEFAULT_TENANT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "SELECT COUNT(*) FROM system_tenant WHERE id = 1;" \
        --batch --skip-column-names 2>/dev/null)
    
    if [ "$DEFAULT_TENANT" = "1" ]; then
        echo "✅ 默认租户(ID=1)存在"
    else
        echo "⚠️  默认租户(ID=1)不存在"
    fi
else
    echo "❌ system_tenant表不存在"
fi

echo
echo "=== 改造建议 ==="

# 统计已添加字段的表数量
TOTAL_TENANT_TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'tenant_id';" \
    --batch --skip-column-names 2>/dev/null)

# 统计索引数量
TOTAL_INDEXES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COUNT(DISTINCT CONCAT(TABLE_NAME, '.', INDEX_NAME)) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND INDEX_NAME LIKE '%tenant%';" \
    --batch --skip-column-names 2>/dev/null)

if [ "$TOTAL_TENANT_TABLES" = "0" ]; then
    echo "建议: 执行 ./scripts/emergency-fix.sh 添加租户字段"
elif [ "$TOTAL_INDEXES" = "0" ]; then
    echo "建议: 执行 mysql < sql/tenant-migration/02-create-indexes.sql 创建索引"
elif [ "$TENANT_TABLE_EXISTS" = "0" ]; then
    echo "建议: 执行 mysql < sql/tenant-migration/03-migrate-data.sql 迁移数据"
else
    echo "建议: 字段、索引、租户表都已就绪，可以启用多租户配置"
fi

echo
echo "========================================"
echo "状态检查完成"
echo "========================================"
