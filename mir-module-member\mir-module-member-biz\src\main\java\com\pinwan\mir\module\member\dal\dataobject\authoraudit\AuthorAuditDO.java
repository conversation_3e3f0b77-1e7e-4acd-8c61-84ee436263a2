package com.pinwan.mir.module.member.dal.dataobject.authoraudit;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;
import org.apache.commons.lang3.StringUtils;

/**
 * 作者审核列 DO
 *
 * <AUTHOR>
 */
@TableName("author_audit")
@KeySequence("author_audit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthorAuditDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 作者id
     */
    private Long authorId;

    /**
     * 审核图片地址
     */
    private String imgUrl;

    /**
     * 身份证号 base64加密
     */
    private String idCard;

    /**
     * 姓名
     */
    private String name;
    /**
     * 审核状态 0 待审核 -1 审核不通过 1 审核通过
     */
    private Integer status;
    /**
     * 审核失败原因
     */
    private String failReason;

}
