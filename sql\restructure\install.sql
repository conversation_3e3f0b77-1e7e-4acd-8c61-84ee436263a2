-- =============================================
-- 足球彩票系统 - 数据库安装脚本
-- 执行顺序：框架表 -> 后台表 -> 应用表 -> 基础数据
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `football_lottery` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `football_lottery`;

-- 执行框架表
SOURCE 01-framework-tables.sql;

-- 执行后台前端表
SOURCE 02-admin-frontend-tables.sql;

-- 执行应用功能表
SOURCE 03-application-tables.sql;

-- 执行基础数据
SOURCE 04-basic-data.sql;

-- 执行微信扩展表
SOURCE 05-wechat-extension-tables.sql;

-- 完成提示
SELECT '数据库安装完成！' AS message;
SELECT '默认管理员账号: admin' AS admin_info;
SELECT '默认管理员密码: admin123' AS password_info;
