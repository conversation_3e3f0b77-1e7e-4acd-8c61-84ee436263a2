package com.pinwan.mir.module.member.dal.dataobject.usergoldlogs;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;

/**
 * 用户鱼币变更记录 DO
 *
 * <AUTHOR>
 */
@TableName("member_user_gold_logs")
@KeySequence("member_user_gold_logs_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberUserGoldLogsDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 修改前鱼币
     */
    private BigDecimal beforeAmount;
    /**
     * 修改的鱼币
     */
    private BigDecimal amount;
    /**
     * 修改后鱼币
     */
    private BigDecimal afterAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分类
     */
    private Integer classify;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单类型
     */
    private Integer orderType;

    @TableField(exist = false)
    private String classifyName;
}
