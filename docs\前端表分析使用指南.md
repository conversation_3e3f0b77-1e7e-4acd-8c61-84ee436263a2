# 前端表分析使用指南

## 🎯 目标

专门分析`football-lottery-frontend`（后台管理前端）项目使用的数据库表，确保在清理数据库时不会误删前端必需的表。

## 📋 背景说明

- **前端项目**: football-lottery-frontend
- **项目性质**: 后台管理系统前端
- **技术栈**: Vue3 + Element Plus + TypeScript
- **对应后端**: football-lottery (mir项目)
- **部署位置**: h:/football-lottery-frontend

## 🛠️ 分析工具

### 1. 前端表分析工具

```bash
# 分析前端使用的表
chmod +x scripts/analyze-frontend-tables.sh
./scripts/analyze-frontend-tables.sh
```

**功能**:
- 基于后端API模块分析前端可能使用的表
- 按功能模块分类表的重要性
- 识别前端核心表和非核心表
- 生成前端表保护清单

### 2. 综合清理分析（已集成前端保护）

```bash
# 综合分析（包含前端表保护）
chmod +x scripts/comprehensive-table-cleanup.sh
./scripts/comprehensive-table-cleanup.sh
```

**功能**:
- 在清理建议中自动排除前端核心表
- 提供安全的删除建议
- 避免误删前端必需的表

## 📊 前端功能模块与表的对应关系

### 🔴 核心功能表（绝对不能删除）

#### 系统管理核心
```
system_users          # 管理员登录认证
system_role           # 角色权限管理
system_menu           # 菜单权限控制
system_tenant         # 多租户管理
system_user_role      # 用户角色关联
system_role_menu      # 角色菜单关联
```

#### 业务管理核心
```
member_user           # 会员信息管理
member_level          # 会员等级管理
pay_app              # 支付配置管理
pay_order            # 支付订单管理
pay_wallet           # 用户钱包管理
author_article       # 文章内容管理
```

#### 基础配置核心
```
system_dict_type     # 数据字典类型
system_dict_data     # 数据字典数据
infra_file           # 文件上传管理
infra_file_config    # 文件存储配置
```

### 🟡 重要功能表（谨慎删除）

```
system_dept          # 部门管理
system_post          # 岗位管理
member_level_record  # 会员等级记录
pay_refund          # 退款管理
pay_wallet_transaction # 钱包交易记录
author_article_append # 文章追加内容
match_team          # 球队信息管理
banner              # 轮播图管理
mp_account          # 微信账号管理
mp_user             # 微信用户管理
```

### 🟢 一般功能表（可考虑删除）

```
system_notice        # 通知公告
member_point_record  # 积分记录
member_sign_in_record # 签到记录
mp_message          # 微信消息记录
mp_tag              # 微信标签
infra_codegen_table # 代码生成工具
infra_codegen_column # 代码生成字段
```

## 🗑️ 可安全删除的表

### Demo/测试表（优先删除）
```sql
-- YuDao框架Demo表
DROP TABLE IF EXISTS mir_demo01_contact;
DROP TABLE IF EXISTS mir_demo02_category;
DROP TABLE IF EXISTS mir_demo03_student;
DROP TABLE IF EXISTS mir_demo03_course;
DROP TABLE IF EXISTS mir_demo03_grade;
DROP TABLE IF EXISTS pay_demo_transfer;
```

### 日志表（可考虑删除）
```sql
-- 系统日志表
DROP TABLE IF EXISTS infra_api_access_log;
DROP TABLE IF EXISTS infra_api_error_log;
DROP TABLE IF EXISTS infra_job_log;
```

### 可选功能表（根据需要删除）
```sql
-- 短信功能（如果不使用）
-- DROP TABLE IF EXISTS system_sms_channel;
-- DROP TABLE IF EXISTS system_sms_template;
-- DROP TABLE IF EXISTS system_sms_log;

-- OAuth2功能（如果不使用第三方登录）
-- DROP TABLE IF EXISTS system_oauth2_client;
-- DROP TABLE IF EXISTS system_oauth2_access_token;
-- DROP TABLE IF EXISTS system_oauth2_refresh_token;
```

## 📋 执行步骤

### 步骤1: 分析前端使用的表

```bash
# 运行前端表分析
./scripts/analyze-frontend-tables.sh
```

查看生成的报告：
```bash
cat docs/database/frontend-tables-analysis.md
cat docs/database/frontend-core-tables.txt
```

### 步骤2: 确认前端核心表

检查以下文件确认前端核心表清单：
- `docs/database/frontend-tables-record.md` - 详细的前端表记录
- `docs/database/frontend-core-tables.txt` - 核心表清单

### 步骤3: 执行安全清理

```bash
# 运行综合清理分析（已集成前端保护）
./scripts/comprehensive-table-cleanup.sh
```

### 步骤4: 验证清理结果

清理后验证前端功能：

1. **登录功能**: 管理员登录是否正常
2. **菜单权限**: 菜单显示和权限控制是否正常
3. **会员管理**: 会员列表和详情是否正常
4. **支付管理**: 订单查询和支付配置是否正常
5. **文章管理**: 文章发布和编辑是否正常
6. **文件上传**: 图片和文件上传是否正常

## ⚠️ 前端特殊注意事项

### 1. 权限相关表
前端的菜单显示和权限控制完全依赖以下表：
- `system_menu` - 菜单结构
- `system_role` - 角色定义
- `system_role_menu` - 角色菜单关联

**这些表绝对不能删除或修改结构！**

### 2. 字典数据表
前端的下拉选项、状态显示等依赖字典表：
- `system_dict_type` - 字典类型
- `system_dict_data` - 字典数据

**删除会导致前端显示异常！**

### 3. 文件管理表
前端的图片上传、文件管理功能依赖：
- `infra_file` - 文件记录
- `infra_file_config` - 存储配置

**删除会导致文件上传失败！**

### 4. 租户相关表
多租户系统的数据隔离依赖：
- `system_tenant` - 租户信息
- 所有业务表的 `tenant_id` 字段

**删除会导致数据混乱！**

## 🔍 前端功能验证清单

删除表后，请按以下清单验证前端功能：

### 基础功能
- [ ] 管理员登录
- [ ] 菜单显示
- [ ] 权限控制
- [ ] 多租户切换

### 会员管理
- [ ] 会员列表查询
- [ ] 会员详情查看
- [ ] 会员等级管理
- [ ] 积分记录查询

### 支付管理
- [ ] 支付配置
- [ ] 订单查询
- [ ] 退款处理
- [ ] 钱包管理

### 内容管理
- [ ] 文章发布
- [ ] 文章编辑
- [ ] 图片上传
- [ ] 轮播图管理

### 系统配置
- [ ] 字典管理
- [ ] 文件管理
- [ ] 部门管理
- [ ] 角色管理

## 📈 清理效果预估

基于前端功能分析：

| 表类型 | 数量 | 清理建议 | 前端影响 |
|--------|------|----------|----------|
| 前端核心表 | 15-20个 | 🔴 不能删除 | 严重影响 |
| 前端重要表 | 20-25个 | 🟡 谨慎删除 | 部分影响 |
| 前端一般表 | 10-15个 | 🟢 可考虑删除 | 轻微影响 |
| Demo/测试表 | 5-10个 | ✅ 可安全删除 | 无影响 |
| 日志表 | 5-10个 | ✅ 可安全删除 | 无影响 |

**预估清理效果**：
- 可安全删除：10-20个表
- 对前端无影响的清理比例：约20-30%

## 🔄 维护建议

1. **定期同步**: 前端功能变更时及时更新表记录
2. **版本管理**: 前端版本升级时检查新的表依赖
3. **测试验证**: 每次清理后完整测试前端功能
4. **文档更新**: 及时更新前端表使用记录

---

**重要提醒**: 前端是用户直接接触的界面，任何表的误删都可能导致严重的用户体验问题。在清理数据库时，务必优先保护前端核心表！
