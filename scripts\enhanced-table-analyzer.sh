#!/bin/bash

# =============================================
# 增强表分析器
# 基于已经工作的complete-table-classification.sh
# 生成表结构、数据字典和ER图
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "========================================"
echo "增强表分析器"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-sports_gaming}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 验证连接
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "❌ 数据库连接失败"
    exit 1
fi
echo "✅ 数据库连接成功"

# 创建输出目录
mkdir -p enhanced-analysis

log_info "分析数据库: $DB_NAME"

# 获取所有表
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

TOTAL_TABLES=$(wc -l < /tmp/all_tables.txt)
log_info "发现 $TOTAL_TABLES 个表"

# 1. 生成表结构详情
log_info "生成表结构详情..."

cat > enhanced-analysis/table-structure-enhanced.md << EOF
# 数据库表结构详情

## 📊 数据库: $DB_NAME

**生成时间**: $(date)
**总表数**: $TOTAL_TABLES

EOF

while read table_name; do
    if [ -n "$table_name" ]; then
        echo "### 表: $table_name" >> enhanced-analysis/table-structure-enhanced.md
        echo "" >> enhanced-analysis/table-structure-enhanced.md
        
        # 获取表注释 - 使用与complete-table-classification.sh相同的方法
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)

        # 处理空注释
        [ -z "$TABLE_COMMENT" ] && TABLE_COMMENT="无注释"
        
        echo "**表说明**: $TABLE_COMMENT" >> enhanced-analysis/table-structure-enhanced.md
        echo "" >> enhanced-analysis/table-structure-enhanced.md
        
        # 生成字段信息表格
        echo "| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |" >> enhanced-analysis/table-structure-enhanced.md
        echo "|--------|------|----------|-----|--------|------|" >> enhanced-analysis/table-structure-enhanced.md
        
        # 获取字段详情 - 使用临时文件避免管道问题
        temp_columns_file="/tmp/columns_${table_name}_$$.txt"
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT
                COLUMN_NAME,
                COLUMN_TYPE,
                IS_NULLABLE,
                IFNULL(COLUMN_KEY, ''),
                IFNULL(COLUMN_DEFAULT, ''),
                IFNULL(COLUMN_COMMENT, '')
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name'
            ORDER BY ORDINAL_POSITION;" \
            --batch --skip-column-names 2>/dev/null > "$temp_columns_file"

        if [ -s "$temp_columns_file" ]; then
            while IFS=$'\t' read -r col_name col_type is_null col_key col_default col_comment; do
                # 跳过空行
                [ -z "$col_name" ] && continue

                # 处理特殊字符
                col_default=$(echo "$col_default" | sed 's/|/\\|/g' | sed 's/NULL//')
                col_comment=$(echo "$col_comment" | sed 's/|/\\|/g' | sed 's/NULL//')

                # 格式化键类型
                case "$col_key" in
                    "PRI") col_key="🔑 PK" ;;
                    "UNI") col_key="🔒 UK" ;;
                    "MUL") col_key="📇 FK" ;;
                    *) col_key="" ;;
                esac

                # 格式化是否为空
                case "$is_null" in
                    "NO") is_null="❌" ;;
                    "YES") is_null="✅" ;;
                esac

                echo "| $col_name | $col_type | $is_null | $col_key | $col_default | $col_comment |" >> enhanced-analysis/table-structure-enhanced.md
            done < "$temp_columns_file"
        else
            echo "| - | 无法获取字段信息 | - | - | - | 请检查数据库权限 |" >> enhanced-analysis/table-structure-enhanced.md
        fi

        rm -f "$temp_columns_file"
        
        echo "" >> enhanced-analysis/table-structure-enhanced.md
        echo "---" >> enhanced-analysis/table-structure-enhanced.md
        echo "" >> enhanced-analysis/table-structure-enhanced.md
    fi
done < /tmp/all_tables.txt

log_success "表结构详情已生成: enhanced-analysis/table-structure-enhanced.md"

# 2. 生成数据字典
log_info "生成数据字典..."

cat > enhanced-analysis/data-dictionary-enhanced.md << EOF
# 数据字典

## 📊 数据库: $DB_NAME

**生成时间**: $(date)

## 📋 表概览

| 序号 | 表名 | 注释 | 字段数 | 记录数 |
|------|------|------|--------|--------|
EOF

table_num=1
while read table_name; do
    if [ -n "$table_name" ]; then
        # 获取表信息
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        COLUMN_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        ROW_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_ROWS, 0) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        echo "| $table_num | $table_name | $TABLE_COMMENT | $COLUMN_COUNT | $ROW_COUNT |" >> enhanced-analysis/data-dictionary-enhanced.md
        table_num=$((table_num + 1))
    fi
done < /tmp/all_tables.txt

echo "" >> enhanced-analysis/data-dictionary-enhanced.md
echo "## 📝 字段详情" >> enhanced-analysis/data-dictionary-enhanced.md
echo "" >> enhanced-analysis/data-dictionary-enhanced.md

# 按表生成字段详情
while read table_name; do
    if [ -n "$table_name" ]; then
        echo "### $table_name" >> enhanced-analysis/data-dictionary-enhanced.md
        echo "" >> enhanced-analysis/data-dictionary-enhanced.md
        
        TABLE_COMMENT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT IFNULL(TABLE_COMMENT, '无注释') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
            --batch --skip-column-names 2>/dev/null)
        
        echo "**表说明**: $TABLE_COMMENT" >> enhanced-analysis/data-dictionary-enhanced.md
        echo "" >> enhanced-analysis/data-dictionary-enhanced.md
        
        echo "| 字段 | 类型 | 空值 | 键 | 默认值 | 说明 |" >> enhanced-analysis/data-dictionary-enhanced.md
        echo "|------|------|------|-----|--------|------|" >> enhanced-analysis/data-dictionary-enhanced.md
        
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT
                COLUMN_NAME,
                COLUMN_TYPE,
                IF(IS_NULLABLE='YES', 'Y', 'N'),
                IFNULL(COLUMN_KEY, ''),
                IFNULL(COLUMN_DEFAULT, ''),
                IFNULL(COLUMN_COMMENT, '')
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name'
            ORDER BY ORDINAL_POSITION;" \
            --batch --skip-column-names 2>/dev/null > /tmp/dict_columns_${table_name}.txt

        if [ -s "/tmp/dict_columns_${table_name}.txt" ]; then
            while IFS=$'\t' read -r col_name col_type is_null col_key col_default col_comment; do
                # 处理特殊字符
                col_default=$(echo "$col_default" | sed 's/NULL//')
                col_comment=$(echo "$col_comment" | sed 's/NULL//')

                echo "| $col_name | $col_type | $is_null | $col_key | $col_default | $col_comment |" >> enhanced-analysis/data-dictionary-enhanced.md
            done < /tmp/dict_columns_${table_name}.txt
        fi

        rm -f /tmp/dict_columns_${table_name}.txt
        
        echo "" >> enhanced-analysis/data-dictionary-enhanced.md
    fi
done < /tmp/all_tables.txt

log_success "数据字典已生成: enhanced-analysis/data-dictionary-enhanced.md"

# 3. 生成ER图
log_info "生成ER图..."

cat > enhanced-analysis/er-diagram-enhanced.mmd << EOF
erDiagram
    %% 数据库ER图: $DB_NAME
    %% 生成时间: $(date)
    
EOF

# 为每个表生成ER图定义
while read table_name; do
    if [ -n "$table_name" ]; then
        echo "    $table_name {" >> enhanced-analysis/er-diagram-enhanced.mmd
        
        # 获取字段信息 - 使用临时文件避免管道问题
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT
                COLUMN_NAME,
                COLUMN_TYPE,
                IFNULL(COLUMN_KEY, ''),
                IFNULL(COLUMN_COMMENT, '')
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name'
            ORDER BY ORDINAL_POSITION;" \
            --batch --skip-column-names 2>/dev/null > /tmp/er_columns_${table_name}.txt

        if [ -s "/tmp/er_columns_${table_name}.txt" ]; then
            while IFS=$'\t' read -r col_name col_type col_key col_comment; do
                [ -z "$col_name" ] && continue

                # 确定字段类型标记
                key_marker=""
                case "$col_key" in
                    "PRI") key_marker=" PK" ;;
                    "UNI") key_marker=" UK" ;;
                    "MUL") key_marker=" FK" ;;
                esac

                # 简化数据类型
                simple_type=$(echo "$col_type" | sed 's/([^)]*)//g' | awk '{print $1}')

                # 清理注释中的特殊字符
                col_comment=$(echo "$col_comment" | sed 's/"/\\"/g' | sed 's/NULL//')

                if [ -n "$col_comment" ]; then
                    echo "        $simple_type $col_name$key_marker \"$col_comment\"" >> enhanced-analysis/er-diagram-enhanced.mmd
                else
                    echo "        $simple_type $col_name$key_marker" >> enhanced-analysis/er-diagram-enhanced.mmd
                fi
            done < /tmp/er_columns_${table_name}.txt
        fi

        rm -f /tmp/er_columns_${table_name}.txt
        
        echo "    }" >> enhanced-analysis/er-diagram-enhanced.mmd
        echo "" >> enhanced-analysis/er-diagram-enhanced.mmd
    fi
done < /tmp/all_tables.txt

# 尝试识别表关系（基于外键命名约定）
echo "    %% 表关系（基于命名约定推断）" >> enhanced-analysis/er-diagram-enhanced.mmd

while read table_name; do
    if [ -n "$table_name" ]; then
        # 查找可能的外键字段（以_id结尾且不是主键）
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = '$table_name'
            AND COLUMN_NAME LIKE '%_id'
            AND COLUMN_KEY != 'PRI';" \
            --batch --skip-column-names 2>/dev/null > /tmp/fk_columns_${table_name}.txt

        if [ -s "/tmp/fk_columns_${table_name}.txt" ]; then
            while read fk_column; do
                # 推断关联表名（去掉_id后缀）
                ref_table=$(echo "$fk_column" | sed 's/_id$//')

                # 检查推断的表是否存在
                if grep -q "^${ref_table}$" /tmp/all_tables.txt; then
                    echo "    $ref_table ||--o{ $table_name : \"$fk_column\"" >> enhanced-analysis/er-diagram-enhanced.mmd
                fi
            done < /tmp/fk_columns_${table_name}.txt
        fi

        rm -f /tmp/fk_columns_${table_name}.txt
    fi
done < /tmp/all_tables.txt

log_success "ER图已生成: enhanced-analysis/er-diagram-enhanced.mmd"

# 清理临时文件
rm -f /tmp/all_tables.txt

echo
echo "========================================"
echo "增强表分析完成！"
echo "========================================"
echo "📊 表结构详情: enhanced-analysis/table-structure-enhanced.md"
echo "📖 数据字典: enhanced-analysis/data-dictionary-enhanced.md"
echo "🎨 ER图: enhanced-analysis/er-diagram-enhanced.mmd"
echo "📊 总表数: $TOTAL_TABLES"
echo "========================================"
echo ""
echo "💡 提示："
echo "- 可以在 https://mermaid.live/ 查看ER图"
echo "- 所有文件都保存在 enhanced-analysis/ 目录中"
