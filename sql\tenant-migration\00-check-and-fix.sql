-- =============================================
-- 足球彩票系统多租户改造 - 检查和修复脚本
-- 目的：检查当前改造状态，修复部分执行的问题
-- 使用场景：当改造脚本执行中断或出错时使用
-- =============================================

-- 设置SQL模式
SET SQL_SAFE_UPDATES = 0;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 检查当前改造状态
-- =============================================

SELECT '=== 多租户改造状态检查 ===' AS message;

-- 检查哪些表已经添加了tenant_id字段
SELECT 
    '已添加tenant_id字段的表:' AS category,
    TABLE_NAME as table_name,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id'
ORDER BY 
    TABLE_NAME;

-- 统计已添加tenant_id字段的表数量
SELECT 
    COUNT(*) as '已添加tenant_id字段的表数量'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'tenant_id';

-- =============================================
-- 2. 检查需要添加tenant_id字段的表
-- =============================================

-- 定义需要添加tenant_id字段的表列表
CREATE TEMPORARY TABLE IF NOT EXISTS tables_need_tenant_id (
    table_name VARCHAR(64) PRIMARY KEY,
    module_name VARCHAR(32),
    priority INT DEFAULT 1
);

-- 插入需要改造的表列表
INSERT IGNORE INTO tables_need_tenant_id (table_name, module_name, priority) VALUES
-- 会员模块
('member_user', '会员模块', 1),
('member_level', '会员模块', 1),
('member_level_record', '会员模块', 1),
('member_point_record', '会员模块', 1),
('member_sign_in_record', '会员模块', 1),
('member_group', '会员模块', 2),

-- 支付模块
('pay_app', '支付模块', 1),
('pay_order', '支付模块', 1),
('pay_refund', '支付模块', 1),
('pay_wallet', '支付模块', 1),
('pay_wallet_transaction', '支付模块', 1),
('pay_transfer', '支付模块', 2),

-- 业务模块
('article', '业务模块', 1),
('author_article_append', '业务模块', 1),
('match_team', '业务模块', 1),
('banner', '业务模块', 2),
('gold_order', '业务模块', 2),

-- 基础设施模块
('infra_file', '基础设施', 1),
('infra_file_config', '基础设施', 1),
('infra_codegen_table', '基础设施', 1),
('infra_codegen_column', '基础设施', 1),

-- 微信模块
('mp_account', '微信模块', 1),
('mp_message', '微信模块', 1),
('mp_user', '微信模块', 1),
('mp_tag', '微信模块', 1),
('mp_menu', '微信模块', 1),
('mp_auto_reply', '微信模块', 1),

-- 其他可能的表
('coupon', '其他模块', 2),
('activity', '其他模块', 2);

-- 检查哪些表存在但还没有添加tenant_id字段
SELECT 
    '需要添加tenant_id字段的表:' AS category,
    t.table_name,
    t.module_name,
    CASE t.priority 
        WHEN 1 THEN '必须' 
        WHEN 2 THEN '可选' 
        ELSE '未知' 
    END as priority_desc
FROM 
    tables_need_tenant_id t
    INNER JOIN INFORMATION_SCHEMA.TABLES it ON it.TABLE_NAME = t.table_name AND it.TABLE_SCHEMA = DATABASE()
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS ic ON ic.TABLE_NAME = t.table_name AND ic.TABLE_SCHEMA = DATABASE() AND ic.COLUMN_NAME = 'tenant_id'
WHERE 
    ic.COLUMN_NAME IS NULL
ORDER BY 
    t.priority, t.module_name, t.table_name;

-- =============================================
-- 3. 检查租户相关索引状态
-- =============================================

SELECT '=== 租户索引状态检查 ===' AS message;

-- 检查已创建的tenant_id相关索引
SELECT 
    '已创建的tenant_id索引:' AS category,
    TABLE_NAME as table_name,
    INDEX_NAME as index_name,
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as columns
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME LIKE '%tenant%'
GROUP BY 
    TABLE_NAME, INDEX_NAME
ORDER BY 
    TABLE_NAME, INDEX_NAME;

-- =============================================
-- 4. 检查数据迁移状态
-- =============================================

SELECT '=== 数据迁移状态检查 ===' AS message;

-- 检查各表中tenant_id为0的记录数量
DELIMITER $$

DROP PROCEDURE IF EXISTS CheckTenantDataMigration$$

CREATE PROCEDURE CheckTenantDataMigration()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(64);
    DECLARE zero_count INT;
    DECLARE total_count INT;
    
    DECLARE table_cursor CURSOR FOR 
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND COLUMN_NAME = 'tenant_id';
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 创建临时表存储结果
    CREATE TEMPORARY TABLE IF NOT EXISTS migration_status (
        table_name VARCHAR(64),
        total_records INT,
        zero_tenant_records INT,
        migration_status VARCHAR(20)
    );
    
    OPEN table_cursor;
    
    read_loop: LOOP
        FETCH table_cursor INTO table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 获取总记录数
        SET @sql = CONCAT('SELECT COUNT(*) INTO @total_count FROM ', table_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SET total_count = @total_count;
        
        -- 获取tenant_id为0的记录数
        SET @sql = CONCAT('SELECT COUNT(*) INTO @zero_count FROM ', table_name, ' WHERE tenant_id = 0');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SET zero_count = @zero_count;
        
        -- 插入结果
        INSERT INTO migration_status VALUES (
            table_name, 
            total_count, 
            zero_count,
            CASE 
                WHEN total_count = 0 THEN '空表'
                WHEN zero_count = 0 THEN '已完成'
                WHEN zero_count = total_count THEN '未开始'
                ELSE '部分完成'
            END
        );
        
    END LOOP;
    
    CLOSE table_cursor;
    
    -- 显示结果
    SELECT 
        '数据迁移状态:' AS category,
        table_name as '表名',
        total_records as '总记录数',
        zero_tenant_records as 'tenant_id为0的记录数',
        migration_status as '迁移状态'
    FROM migration_status
    ORDER BY 
        CASE migration_status 
            WHEN '未开始' THEN 1
            WHEN '部分完成' THEN 2
            WHEN '已完成' THEN 3
            WHEN '空表' THEN 4
        END,
        table_name;
    
    DROP TEMPORARY TABLE migration_status;
END$$

DELIMITER ;

CALL CheckTenantDataMigration();

-- =============================================
-- 5. 检查系统租户表状态
-- =============================================

SELECT '=== 系统租户表状态检查 ===' AS message;

-- 检查system_tenant表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ system_tenant表存在'
        ELSE '❌ system_tenant表不存在'
    END as tenant_table_status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'system_tenant';

-- 检查默认租户是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✅ 默认租户存在，ID: ', GROUP_CONCAT(id))
        ELSE '❌ 默认租户不存在'
    END as default_tenant_status
FROM system_tenant 
WHERE id = 1;

-- =============================================
-- 6. 生成修复建议
-- =============================================

SELECT '=== 修复建议 ===' AS message;

-- 根据检查结果生成修复建议
SELECT 
    '修复建议:' AS category,
    CASE 
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'tenant_id') = 0 
        THEN '1. 执行 01-add-tenant-fields.sql 添加租户字段'
        
        WHEN (SELECT COUNT(*) FROM tables_need_tenant_id t INNER JOIN INFORMATION_SCHEMA.TABLES it ON it.TABLE_NAME = t.table_name AND it.TABLE_SCHEMA = DATABASE() LEFT JOIN INFORMATION_SCHEMA.COLUMNS ic ON ic.TABLE_NAME = t.table_name AND ic.TABLE_SCHEMA = DATABASE() AND ic.COLUMN_NAME = 'tenant_id' WHERE ic.COLUMN_NAME IS NULL AND t.priority = 1) > 0
        THEN '1. 重新执行 01-add-tenant-fields.sql 完成字段添加'
        
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND INDEX_NAME LIKE '%tenant%') = 0
        THEN '2. 执行 02-create-indexes.sql 创建索引'
        
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS ic WHERE ic.TABLE_SCHEMA = DATABASE() AND ic.COLUMN_NAME = 'tenant_id' LIMIT 1)
        THEN '3. 执行 03-migrate-data.sql 迁移数据'
        
        ELSE '4. 检查应用配置，启用多租户功能'
    END as suggestion;

-- 清理临时表
DROP TEMPORARY TABLE IF EXISTS tables_need_tenant_id;

-- 清理存储过程
DROP PROCEDURE IF EXISTS CheckTenantDataMigration;

-- 恢复SQL模式
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

SELECT '✅ 状态检查完成' AS message;
