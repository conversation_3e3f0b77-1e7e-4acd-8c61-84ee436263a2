-- =============================================
-- 数据库结构分析SQL脚本
-- 用于分析数据库表结构、关系和约束
-- =============================================

-- 1. 获取所有表的基本信息
SELECT '=== 数据库表概览 ===' AS section;

SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '估计行数',
    CREATE_TIME as '创建时间',
    UPDATE_TIME as '更新时间'
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_TYPE = 'BASE TABLE'
ORDER BY 
    TABLE_NAME;

-- 2. 按模块统计表数量
SELECT '=== 按模块统计表数量 ===' AS section;

SELECT 
    CASE 
        WHEN TABLE_NAME LIKE 'member_%' THEN '会员模块'
        WHEN TABLE_NAME LIKE 'pay_%' THEN '支付模块'
        WHEN TABLE_NAME LIKE 'system_%' THEN '系统模块'
        WHEN TABLE_NAME LIKE 'infra_%' THEN '基础设施模块'
        WHEN TABLE_NAME LIKE 'mp_%' THEN '微信模块'
        WHEN TABLE_NAME LIKE 'author_%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'match_%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'banner%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'gold_%' THEN '业务模块'
        ELSE '其他模块'
    END as '模块',
    COUNT(*) as '表数量',
    GROUP_CONCAT(TABLE_NAME ORDER BY TABLE_NAME) as '表列表'
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_TYPE = 'BASE TABLE'
GROUP BY 
    CASE 
        WHEN TABLE_NAME LIKE 'member_%' THEN '会员模块'
        WHEN TABLE_NAME LIKE 'pay_%' THEN '支付模块'
        WHEN TABLE_NAME LIKE 'system_%' THEN '系统模块'
        WHEN TABLE_NAME LIKE 'infra_%' THEN '基础设施模块'
        WHEN TABLE_NAME LIKE 'mp_%' THEN '微信模块'
        WHEN TABLE_NAME LIKE 'author_%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'match_%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'banner%' THEN '业务模块'
        WHEN TABLE_NAME LIKE 'gold_%' THEN '业务模块'
        ELSE '其他模块'
    END
ORDER BY 
    COUNT(*) DESC;

-- 3. 检查多租户字段覆盖情况
SELECT '=== 多租户字段覆盖情况 ===' AS section;

SELECT 
    t.TABLE_NAME as '表名',
    t.TABLE_COMMENT as '表注释',
    CASE 
        WHEN c.COLUMN_NAME IS NOT NULL THEN '✅ 已添加'
        ELSE '❌ 未添加'
    END as 'tenant_id字段状态',
    CASE 
        WHEN t.TABLE_NAME LIKE 'member_%' OR 
             t.TABLE_NAME LIKE 'pay_%' OR 
             t.TABLE_NAME LIKE 'author_%' OR 
             t.TABLE_NAME LIKE 'match_%' OR 
             t.TABLE_NAME LIKE 'banner%' OR 
             t.TABLE_NAME LIKE 'gold_%' OR 
             t.TABLE_NAME LIKE 'infra_file%' OR 
             t.TABLE_NAME LIKE 'infra_codegen%' OR 
             t.TABLE_NAME LIKE 'mp_%'
        THEN '需要'
        ELSE '不需要'
    END as '是否需要租户隔离'
FROM 
    INFORMATION_SCHEMA.TABLES t
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS c 
        ON t.TABLE_NAME = c.TABLE_NAME 
        AND t.TABLE_SCHEMA = c.TABLE_SCHEMA 
        AND c.COLUMN_NAME = 'tenant_id'
WHERE 
    t.TABLE_SCHEMA = DATABASE()
    AND t.TABLE_TYPE = 'BASE TABLE'
ORDER BY 
    CASE 
        WHEN t.TABLE_NAME LIKE 'member_%' OR 
             t.TABLE_NAME LIKE 'pay_%' OR 
             t.TABLE_NAME LIKE 'author_%' OR 
             t.TABLE_NAME LIKE 'match_%' OR 
             t.TABLE_NAME LIKE 'banner%' OR 
             t.TABLE_NAME LIKE 'gold_%' OR 
             t.TABLE_NAME LIKE 'infra_file%' OR 
             t.TABLE_NAME LIKE 'infra_codegen%' OR 
             t.TABLE_NAME LIKE 'mp_%'
        THEN 1
        ELSE 2
    END,
    t.TABLE_NAME;

-- 4. 获取外键关系
SELECT '=== 外键关系分析 ===' AS section;

SELECT 
    kcu.TABLE_NAME as '主表',
    kcu.COLUMN_NAME as '外键字段',
    kcu.REFERENCED_TABLE_NAME as '引用表',
    kcu.REFERENCED_COLUMN_NAME as '引用字段',
    kcu.CONSTRAINT_NAME as '约束名称'
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
WHERE 
    kcu.TABLE_SCHEMA = DATABASE()
    AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY 
    kcu.TABLE_NAME, kcu.COLUMN_NAME;

-- 5. 检查主键和唯一键
SELECT '=== 主键和唯一键分析 ===' AS section;

SELECT 
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    CASE 
        WHEN COLUMN_KEY = 'PRI' THEN '主键'
        WHEN COLUMN_KEY = 'UNI' THEN '唯一键'
        WHEN COLUMN_KEY = 'MUL' THEN '索引'
        ELSE '普通字段'
    END as '键类型',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '是否可空',
    COLUMN_COMMENT as '字段注释'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND COLUMN_KEY IN ('PRI', 'UNI')
ORDER BY 
    TABLE_NAME, 
    CASE COLUMN_KEY 
        WHEN 'PRI' THEN 1 
        WHEN 'UNI' THEN 2 
        ELSE 3 
    END;

-- 6. 检查常用字段的标准化情况
SELECT '=== 标准字段检查 ===' AS section;

SELECT 
    '审计字段覆盖情况' as '检查项',
    COUNT(DISTINCT t.TABLE_NAME) as '总表数',
    COUNT(DISTINCT CASE WHEN c1.COLUMN_NAME IS NOT NULL THEN t.TABLE_NAME END) as '有creator字段的表数',
    COUNT(DISTINCT CASE WHEN c2.COLUMN_NAME IS NOT NULL THEN t.TABLE_NAME END) as '有create_time字段的表数',
    COUNT(DISTINCT CASE WHEN c3.COLUMN_NAME IS NOT NULL THEN t.TABLE_NAME END) as '有updater字段的表数',
    COUNT(DISTINCT CASE WHEN c4.COLUMN_NAME IS NOT NULL THEN t.TABLE_NAME END) as '有update_time字段的表数',
    COUNT(DISTINCT CASE WHEN c5.COLUMN_NAME IS NOT NULL THEN t.TABLE_NAME END) as '有deleted字段的表数'
FROM 
    INFORMATION_SCHEMA.TABLES t
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS c1 ON t.TABLE_NAME = c1.TABLE_NAME AND t.TABLE_SCHEMA = c1.TABLE_SCHEMA AND c1.COLUMN_NAME = 'creator'
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS c2 ON t.TABLE_NAME = c2.TABLE_NAME AND t.TABLE_SCHEMA = c2.TABLE_SCHEMA AND c2.COLUMN_NAME = 'create_time'
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS c3 ON t.TABLE_NAME = c3.TABLE_NAME AND t.TABLE_SCHEMA = c3.TABLE_SCHEMA AND c3.COLUMN_NAME = 'updater'
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS c4 ON t.TABLE_NAME = c4.TABLE_NAME AND t.TABLE_SCHEMA = c4.TABLE_SCHEMA AND c4.COLUMN_NAME = 'update_time'
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS c5 ON t.TABLE_NAME = c5.TABLE_NAME AND t.TABLE_SCHEMA = c5.TABLE_SCHEMA AND c5.COLUMN_NAME = 'deleted'
WHERE 
    t.TABLE_SCHEMA = DATABASE()
    AND t.TABLE_TYPE = 'BASE TABLE';

-- 7. 检查索引使用情况
SELECT '=== 索引使用情况 ===' AS section;

SELECT 
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    CASE NON_UNIQUE 
        WHEN 0 THEN '唯一索引'
        ELSE '普通索引'
    END as '索引类型',
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as '索引字段',
    COUNT(*) as '字段数量'
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME != 'PRIMARY'
GROUP BY 
    TABLE_NAME, INDEX_NAME, NON_UNIQUE
ORDER BY 
    TABLE_NAME, INDEX_NAME;

-- 8. 数据类型使用统计
SELECT '=== 数据类型使用统计 ===' AS section;

SELECT 
    DATA_TYPE as '数据类型',
    COUNT(*) as '使用次数',
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE()), 2) as '使用比例(%)'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
GROUP BY 
    DATA_TYPE
ORDER BY 
    COUNT(*) DESC;

SELECT '=== 分析完成 ===' AS section;
