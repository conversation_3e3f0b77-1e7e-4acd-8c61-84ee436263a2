# 足球彩票系统开发技术文档

## 1. 项目概述

### 1.1 项目简介
足球彩票系统是基于芋道项目脚手架开发的企业级应用系统，采用Spring Boot多模块架构，支持多租户SaaS模式，集成了用户管理、权限控制、支付系统、会员中心等核心功能。

### 1.2 技术特点
- **微服务架构**：采用Spring Boot多模块设计，模块化开发
- **多租户支持**：支持SaaS多租户模式，数据隔离
- **权限管理**：基于RBAC的细粒度权限控制
- **支付集成**：支持支付宝、微信等多种支付方式
- **会员体系**：完整的会员等级和积分体系
- **多端支持**：支持Web管理后台、移动端APP、小程序

## 2. 技术栈

### 2.1 后端技术栈
| 技术 | 版本 | 说明 |
|------|------|------|
| Java | 1.8 | 开发语言 |
| Spring Boot | 2.7.18 | 应用开发框架 |
| Spring Security | 5.7.11 | 安全框架 |
| MyBatis Plus | 3.5.7 | ORM框架 |
| MySQL | 5.7/8.0+ | 数据库 |
| Redis | 5.0/6.0/7.0 | 缓存数据库 |
| Redisson | 3.32.0 | Redis客户端 |
| Druid | 1.2.23 | 数据库连接池 |
| Quartz | 2.3.2 | 任务调度 |
| Flowable | 6.8.0 | 工作流引擎 |
| Swagger | 1.7.0 | API文档 |
| MapStruct | 1.5.5.Final | Bean转换 |
| Lombok | 1.18.34 | 代码简化 |

### 2.2 前端技术栈
| 技术 | 说明 |
|------|------|
| Vue3 + Element Plus | 管理后台(现代版) |
| Vue3 + Ant Design Vue | 管理后台(Vben版) |
| Vue2 + Element UI | 管理后台(经典版) |
| uni-app | 移动端跨平台开发 |

### 2.3 运维技术栈
| 技术 | 说明 |
|------|------|
| Docker | 容器化部署 |
| Jenkins | CI/CD |
| SkyWalking | 链路追踪 |
| Spring Boot Admin | 应用监控 |

## 3. 项目架构

### 3.1 整体架构
```
mir-football-lottery/
├── mir-dependencies/           # Maven依赖版本管理
├── mir-framework/             # 框架扩展组件
├── mir-server/               # 主服务模块
├── mir-module-system/        # 系统功能模块
├── mir-module-infra/         # 基础设施模块
├── mir-module-member/        # 会员中心模块
├── mir-module-pay/           # 支付系统模块
├── mir-module-mp/            # 微信公众号模块
├── mir-ui/                   # 前端项目集合
├── sql/                      # 数据库脚本
└── script/                   # 部署脚本
```

### 3.2 模块说明
| 模块 | 说明 | 功能 |
|------|------|------|
| mir-dependencies | Maven依赖版本管理 | 统一管理所有依赖版本 |
| mir-framework | Java框架扩展 | 通用组件和工具类 |
| mir-server | 主服务端 | 管理后台+用户APP的服务端 |
| mir-module-system | 系统功能模块 | 用户、角色、权限、菜单等 |
| mir-module-infra | 基础设施模块 | 文件、配置、定时任务等 |
| mir-module-member | 会员中心模块 | 会员管理、等级、积分等 |
| mir-module-pay | 支付系统模块 | 支付、退款、钱包等 |
| mir-module-mp | 微信公众号模块 | 微信集成功能 |

### 3.3 框架组件
mir-framework包含以下核心组件：
- **mir-common**: 基础POJO类、枚举、工具类
- **mir-spring-boot-starter-web**: Web相关配置
- **mir-spring-boot-starter-security**: 安全认证配置
- **mir-spring-boot-starter-mybatis**: MyBatis配置
- **mir-spring-boot-starter-redis**: Redis配置
- **mir-spring-boot-starter-biz-tenant**: 多租户业务组件
- **mir-spring-boot-starter-biz-data-permission**: 数据权限组件

## 4. 数据库设计

### 4.1 核心业务表

#### 4.1.1 会员相关表
- **member_user**: 会员用户表 - 存储用户基本信息、登录凭证、鱼币余额等
- **member_level**: 会员等级表 - 定义会员等级体系和权益
- **member_level_record**: 会员等级记录表 - 记录用户等级变更历史
- **member_point_record**: 积分记录表 - 记录用户积分变动明细
- **member_sign_in_record**: 签到记录表 - 记录用户每日签到情况
- **member_group**: 用户分组表 - 用户分组管理和标签

#### 4.1.2 支付相关表
- **pay_app**: 支付应用表 - 支付应用配置和回调地址
- **pay_order**: 支付订单表 - 支付订单记录和状态跟踪
- **pay_refund**: 退款订单表 - 退款申请和处理记录
- **pay_wallet**: 用户钱包表 - 用户钱包余额和统计信息
- **pay_wallet_transaction**: 钱包交易记录表 - 钱包收支流水明细

#### 4.1.3 业务相关表
- **author_article**: 文章表 - 文章内容、定价、发布状态管理
- **author_article_append**: 文章追加表 - 文章后续追加内容
- **match_team**: 球队信息表 - 球队基础信息和数据
- **banner**: 轮播图表 - 首页轮播图和广告位管理
- **gold_order**: 鱼币充值订单表 - 虚拟货币充值记录

#### 4.1.4 系统管理表
- **system_users**: 管理员用户表 - 后台管理用户账号
- **system_role**: 角色表 - 权限角色定义
- **system_menu**: 菜单权限表 - 菜单结构和权限控制
- **system_tenant**: 租户表 - 多租户管理和配置
- **system_dept**: 部门表 - 组织架构管理

#### 4.1.5 基础设施表
- **infra_file**: 文件表 - 文件存储记录和元数据
- **infra_file_config**: 文件配置表 - 文件存储配置
- **infra_codegen_table**: 代码生成表 - 代码生成模板配置
- **infra_codegen_column**: 代码生成字段表 - 字段生成规则

#### 4.1.6 微信模块表
- **mp_account**: 微信账号表 - 微信公众号配置信息
- **mp_message**: 微信消息表 - 微信消息收发记录
- **mp_user**: 微信用户表 - 微信用户信息和标签
- **mp_tag**: 微信标签表 - 用户标签分类管理
- **mp_menu**: 微信菜单表 - 自定义菜单配置

### 4.2 数据库特性
- **多租户支持**: 所有业务表都包含tenant_id字段，实现行级数据隔离
- **软删除**: 使用deleted字段实现软删除，保证数据安全性
- **审计字段**: 包含creator、create_time、updater、update_time完整审计信息
- **数据库兼容**: 支持MySQL、PostgreSQL、Oracle等多种数据库
- **字符集**: 使用utf8mb4字符集，支持emoji等特殊字符
- **索引优化**: 针对多租户和业务查询场景优化索引设计

### 4.3 多租户架构
- **隔离级别**: 共享数据库、共享Schema、行级隔离
- **租户标识**: 通过tenant_id字段实现数据隔离
- **权限控制**: 基于租户ID的数据访问控制
- **扩展性**: 支持租户数据独立迁移和水平扩展

## 5. 核心功能模块

### 5.1 系统管理模块
- **用户管理**: 管理员用户的增删改查
- **角色管理**: 角色权限分配
- **菜单管理**: 系统菜单和权限配置
- **部门管理**: 组织架构管理
- **字典管理**: 系统字典数据维护
- **操作日志**: 系统操作审计
- **登录日志**: 用户登录记录

### 5.2 会员中心模块
- **会员管理**: 会员信息管理
- **会员等级**: 等级体系配置
- **积分管理**: 积分获取和消费
- **签到功能**: 每日签到奖励

### 5.3 支付系统模块
- **支付应用**: 支付渠道配置
- **支付订单**: 订单支付处理
- **退款管理**: 退款流程处理
- **钱包系统**: 用户余额管理

### 5.4 基础设施模块
- **文件管理**: 文件上传和存储
- **配置管理**: 系统参数配置
- **定时任务**: 任务调度管理
- **代码生成**: 自动生成CRUD代码
- **API文档**: Swagger接口文档

## 6. 开发规范

### 6.1 代码规范
- 遵循《阿里巴巴Java开发手册》
- 使用Lombok简化代码
- 统一异常处理
- 完善的单元测试

### 6.2 包结构规范
```
com.pinwan.mir.module.{module}
├── controller/     # 控制器层
├── service/        # 业务逻辑层
├── dal/           # 数据访问层
│   ├── dataobject/ # 数据对象
│   └── mysql/      # Mapper接口
├── convert/        # 对象转换
└── enums/         # 枚举类
```

### 6.3 API设计规范
- RESTful API设计
- 统一响应格式
- 完整的参数校验
- 详细的接口文档

## 7. 部署说明

### 7.1 环境要求
- JDK 1.8+
- MySQL 5.7+
- Redis 5.0+
- Maven 3.6+

### 7.2 配置文件
主要配置文件位于`mir-server/src/main/resources/`：
- `application.yaml`: 主配置文件
- `application-dev.yaml`: 开发环境配置
- `application-prod.yaml`: 生产环境配置

### 7.3 Docker部署
项目提供了Dockerfile，支持容器化部署：
```bash
# 构建镜像
docker build -t mir-football-server .

# 运行容器
docker run -d -p 48080:48080 mir-football-server
```

## 8. 开发指南

### 8.1 本地开发环境搭建
1. 安装JDK 1.8
2. 安装MySQL和Redis
3. 导入数据库脚本
4. 配置application-dev.yaml
5. 启动MirServerApplication

### 8.2 新增模块开发
1. 创建模块目录结构
2. 配置pom.xml依赖
3. 实现业务逻辑
4. 编写单元测试
5. 更新文档

### 8.3 前端开发
- 管理后台：选择Vue3或Vue2版本
- 移动端：使用uni-app开发
- API对接：使用统一的HTTP客户端

## 9. 监控运维

### 9.1 应用监控
- Spring Boot Admin监控应用状态
- SkyWalking链路追踪
- 自定义健康检查接口

### 9.2 日志管理
- 统一日志格式
- 分级日志输出
- 日志文件轮转

### 9.3 性能优化
- Redis缓存优化
- 数据库索引优化
- 连接池配置优化

## 10. 常见问题

### 10.1 启动问题
如果遇到启动问题，请参考：https://doc.iocoder.cn/quick-start/

### 10.2 数据库连接
检查数据库配置和网络连接

### 10.3 Redis连接
确认Redis服务状态和配置

## 11. 安全机制

### 11.1 认证授权
- **JWT Token**: 基于JWT的无状态认证
- **Spring Security**: 统一安全框架
- **多终端支持**: 支持Web、APP、小程序等多终端
- **SSO单点登录**: 支持多应用单点登录

### 11.2 数据安全
- **数据加密**: 敏感数据加密存储
- **SQL注入防护**: MyBatis预编译防护
- **XSS防护**: 输入输出过滤
- **CSRF防护**: 跨站请求伪造防护

### 11.3 权限控制
- **RBAC模型**: 基于角色的访问控制
- **数据权限**: 支持部门、个人等数据范围权限
- **按钮权限**: 细粒度的操作权限控制
- **API权限**: 接口级别的权限验证

## 12. 缓存策略

### 12.1 Redis缓存
- **用户会话**: 用户登录状态缓存
- **权限数据**: 用户权限信息缓存
- **字典数据**: 系统字典缓存
- **热点数据**: 频繁访问数据缓存

### 12.2 缓存配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
```

### 12.3 缓存策略
- **缓存穿透**: 布隆过滤器防护
- **缓存雪崩**: 过期时间随机化
- **缓存击穿**: 分布式锁保护
- **数据一致性**: 缓存更新策略

## 13. 消息队列

### 13.1 支持的MQ
- **Redis Stream**: 轻量级消息队列
- **Redis Pub/Sub**: 发布订阅模式
- **RabbitMQ**: 企业级消息队列
- **Kafka**: 高吞吐量消息队列
- **RocketMQ**: 阿里云消息队列

### 13.2 使用场景
- **异步处理**: 耗时操作异步化
- **系统解耦**: 模块间松耦合
- **流量削峰**: 高并发场景缓冲
- **事件驱动**: 事件通知机制

## 14. 分布式特性

### 14.1 分布式锁
```java
@Resource
private RedissonClient redissonClient;

public void businessMethod() {
    RLock lock = redissonClient.getLock("business:lock");
    try {
        if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
            // 业务逻辑
        }
    } finally {
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
```

### 14.2 分布式事务
- **本地事务**: @Transactional注解
- **分布式事务**: Seata分布式事务
- **最终一致性**: 基于消息的最终一致性

### 14.3 分布式配置
- **Nacos**: 配置中心和服务发现
- **Apollo**: 携程配置中心
- **Spring Cloud Config**: 配置中心

## 15. 性能优化

### 15.1 数据库优化
- **索引优化**: 合理创建数据库索引
- **SQL优化**: 避免慢SQL查询
- **分页查询**: 大数据量分页处理
- **读写分离**: 主从数据库分离

### 15.2 应用优化
- **连接池**: 数据库连接池优化
- **线程池**: 异步任务线程池
- **JVM调优**: 内存和GC优化
- **代码优化**: 算法和数据结构优化

### 15.3 缓存优化
- **多级缓存**: 本地缓存+Redis缓存
- **缓存预热**: 系统启动时预加载
- **缓存更新**: 合理的缓存更新策略

## 16. 测试策略

### 16.1 单元测试
```java
@SpringBootTest
@Transactional
@Rollback
class UserServiceTest {

    @Resource
    private UserService userService;

    @Test
    void testCreateUser() {
        // 测试用户创建
    }
}
```

### 16.2 集成测试
- **API测试**: 接口功能测试
- **数据库测试**: 数据持久化测试
- **缓存测试**: 缓存功能测试

### 16.3 性能测试
- **压力测试**: JMeter压力测试
- **并发测试**: 高并发场景测试
- **稳定性测试**: 长时间运行测试

## 17. 日志规范

### 17.1 日志级别
- **ERROR**: 系统错误，需要立即处理
- **WARN**: 警告信息，需要关注
- **INFO**: 重要业务信息
- **DEBUG**: 调试信息，开发环境使用

### 17.2 日志格式
```yaml
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
```

### 17.3 日志管理
- **日志分类**: 按模块分类存储
- **日志轮转**: 按大小和时间轮转
- **日志清理**: 定期清理历史日志
- **日志监控**: 异常日志告警

## 18. API文档

### 18.1 Swagger配置
项目集成了Swagger3，自动生成API文档：
- 访问地址：http://localhost:48080/doc.html
- 支持在线测试
- 自动生成接口文档

### 18.2 接口规范
```java
@RestController
@RequestMapping("/admin-api/system/user")
@Tag(name = "管理后台 - 用户")
public class UserController {

    @GetMapping("/get")
    @Operation(summary = "获得用户")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<UserRespVO> getUser(@RequestParam("id") Long id) {
        // 实现逻辑
    }
}
```

## 19. 版本管理

### 19.1 版本号规范
- **主版本号**: 重大功能变更
- **次版本号**: 新功能添加
- **修订版本号**: Bug修复

### 19.2 分支管理
- **master**: 主分支，生产环境代码
- **develop**: 开发分支，集成测试
- **feature**: 功能分支，新功能开发
- **hotfix**: 热修复分支，紧急修复

## 20. 扩展指南

### 20.1 新增业务模块
1. 创建模块目录结构
2. 配置Maven依赖
3. 实现数据访问层
4. 实现业务逻辑层
5. 实现控制器层
6. 编写单元测试
7. 更新API文档

### 20.2 集成第三方服务
1. 添加依赖配置
2. 创建配置类
3. 实现服务接口
4. 添加异常处理
5. 编写测试用例

---

**文档维护**: 本技术文档需要随着项目发展持续更新，建议每个版本发布时同步更新文档内容。

**技术支持**: 如有技术问题，请参考项目README或联系开发团队。
