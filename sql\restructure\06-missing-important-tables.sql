-- =============================================
-- 足球彩票系统 - 重要遗漏表补充
-- 补充重构方案中遗漏的重要表
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 模板相关表（高优先级）
-- =============================================

-- 邮件模板表
CREATE TABLE IF NOT EXISTS `system_mail_template` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `name` varchar(63) NOT NULL COMMENT '模板名称',
    `code` varchar(63) NOT NULL COMMENT '模板编码',
    `account_id` bigint NOT NULL COMMENT '发送的邮箱账号编号',
    `nickname` varchar(255) DEFAULT NULL COMMENT '发送人名称',
    `title` varchar(255) NOT NULL COMMENT '模板标题',
    `content` text NOT NULL COMMENT '模板内容',
    `params` varchar(255) NOT NULL COMMENT '参数数组',
    `status` tinyint NOT NULL COMMENT '开启状态',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件模板表';

-- 站内信模板表
CREATE TABLE IF NOT EXISTS `system_notify_template` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(63) NOT NULL COMMENT '模板名称',
    `code` varchar(64) NOT NULL COMMENT '模版编码',
    `nickname` varchar(255) NOT NULL COMMENT '发送人名称',
    `content` text NOT NULL COMMENT '模版内容',
    `type` tinyint NOT NULL COMMENT '类型',
    `params` varchar(255) DEFAULT NULL COMMENT '参数数组',
    `status` tinyint NOT NULL COMMENT '状态',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站内信模板表';

-- 短信模板表
CREATE TABLE IF NOT EXISTS `system_sms_template` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `type` tinyint NOT NULL COMMENT '短信签名',
    `status` tinyint NOT NULL COMMENT '开启状态',
    `code` varchar(63) NOT NULL COMMENT '模板编码',
    `name` varchar(63) NOT NULL COMMENT '模板名称',
    `content` varchar(255) NOT NULL COMMENT '模板内容',
    `params` varchar(255) NOT NULL COMMENT '参数数组',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `api_template_id` varchar(63) NOT NULL COMMENT '短信 API 的模板编号',
    `channel_id` bigint NOT NULL COMMENT '短信渠道编号',
    `channel_code` varchar(63) NOT NULL COMMENT '短信渠道编码',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信模板表';

-- =============================================
-- 系统配置表（高优先级）
-- =============================================

-- 邮箱账号表
CREATE TABLE IF NOT EXISTS `system_mail_account` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `mail` varchar(255) NOT NULL COMMENT '邮箱',
    `username` varchar(255) NOT NULL COMMENT '用户名',
    `password` varchar(255) NOT NULL COMMENT '密码',
    `host` varchar(255) NOT NULL COMMENT 'SMTP 服务器域名',
    `port` int NOT NULL COMMENT 'SMTP 服务器端口',
    `ssl_enable` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启 SSL',
    `starttls_enable` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启 STARTTLS',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_mail` (`mail`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱账号表';

-- 短信渠道表
CREATE TABLE IF NOT EXISTS `system_sms_channel` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `signature` varchar(12) NOT NULL COMMENT '短信签名',
    `code` varchar(63) NOT NULL COMMENT '渠道编码',
    `status` tinyint NOT NULL COMMENT '开启状态',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `api_key` varchar(128) NOT NULL COMMENT '短信 API 的账号',
    `api_secret` varchar(128) DEFAULT NULL COMMENT '短信 API 的密钥',
    `callback_url` varchar(255) DEFAULT NULL COMMENT '短信发送回调 URL',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信渠道表';

-- 参数配置表
CREATE TABLE IF NOT EXISTS `infra_config` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
    `category` varchar(50) DEFAULT NULL COMMENT '参数分组',
    `type` tinyint NOT NULL COMMENT '参数类型',
    `name` varchar(100) NOT NULL DEFAULT '' COMMENT '参数名称',
    `config_key` varchar(100) NOT NULL DEFAULT '' COMMENT '参数键名',
    `value` varchar(500) NOT NULL DEFAULT '' COMMENT '参数键值',
    `sensitive` bit(1) NOT NULL COMMENT '是否敏感',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参数配置表';

-- =============================================
-- OAuth2相关表（可选）
-- =============================================

-- OAuth2客户端表
CREATE TABLE IF NOT EXISTS `system_oauth2_client` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `client_id` varchar(255) NOT NULL COMMENT '客户端编号',
    `secret` varchar(255) NOT NULL COMMENT '客户端密钥',
    `name` varchar(255) NOT NULL COMMENT '应用名',
    `logo` varchar(255) NOT NULL COMMENT '应用图标',
    `description` varchar(255) DEFAULT NULL COMMENT '应用描述',
    `status` tinyint NOT NULL COMMENT '状态',
    `access_token_validity_seconds` int NOT NULL COMMENT '访问令牌的有效期',
    `refresh_token_validity_seconds` int NOT NULL COMMENT '刷新令牌的有效期',
    `redirect_uris` varchar(255) NOT NULL COMMENT '可重定向的 URI 地址',
    `authorized_grant_types` varchar(255) NOT NULL COMMENT '授权类型',
    `scopes` varchar(255) DEFAULT NULL COMMENT '授权范围',
    `auto_approve_scopes` varchar(255) DEFAULT NULL COMMENT '自动通过的授权范围',
    `authorities` varchar(255) DEFAULT NULL COMMENT '权限',
    `resource_ids` varchar(255) DEFAULT NULL COMMENT '资源',
    `additional_information` varchar(4096) DEFAULT NULL COMMENT '附加信息',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_client_id` (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth2 客户端表';

-- =============================================
-- 基础设施扩展表
-- =============================================

-- 定时任务表
CREATE TABLE IF NOT EXISTS `infra_job` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务编号',
    `name` varchar(32) NOT NULL COMMENT '任务名称',
    `status` tinyint NOT NULL COMMENT '任务状态',
    `handler_name` varchar(64) NOT NULL COMMENT '处理器的名字',
    `handler_param` varchar(255) DEFAULT NULL COMMENT '处理器的参数',
    `cron_expression` varchar(32) NOT NULL COMMENT 'CRON 表达式',
    `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
    `retry_interval` int NOT NULL DEFAULT '0' COMMENT '重试间隔',
    `monitor_timeout` int NOT NULL DEFAULT '0' COMMENT '监控超时时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时任务表';

-- 代码生成表定义
CREATE TABLE IF NOT EXISTS `infra_codegen_table` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `data_source_config_id` bigint NOT NULL COMMENT '数据源配置的编号',
    `scene` tinyint NOT NULL DEFAULT '1' COMMENT '生成场景',
    `table_name` varchar(200) NOT NULL DEFAULT '' COMMENT '表名称',
    `table_comment` varchar(500) NOT NULL DEFAULT '' COMMENT '表描述',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `module_name` varchar(30) NOT NULL COMMENT '模块名',
    `business_name` varchar(30) NOT NULL COMMENT '业务名',
    `class_name` varchar(100) NOT NULL DEFAULT '' COMMENT '类名称',
    `class_comment` varchar(50) NOT NULL COMMENT '类描述',
    `author` varchar(50) NOT NULL COMMENT '作者',
    `template_type` tinyint NOT NULL DEFAULT '1' COMMENT '模板类型',
    `front_type` tinyint NOT NULL COMMENT '前端类型',
    `parent_menu_id` bigint DEFAULT NULL COMMENT '父菜单编号',
    `master_table_id` bigint DEFAULT NULL COMMENT '主表的编号',
    `sub_join_column_id` bigint DEFAULT NULL COMMENT '子表关联主表的字段编号',
    `sub_join_many` bit(1) DEFAULT NULL COMMENT '主表与子表是否一对多',
    `tree_parent_column_id` bigint DEFAULT NULL COMMENT '树表的父字段编号',
    `tree_name_column_id` bigint DEFAULT NULL COMMENT '树表的名字字段编号',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代码生成表定义';

-- 代码生成表字段定义
CREATE TABLE IF NOT EXISTS `infra_codegen_column` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `table_id` bigint NOT NULL COMMENT '表编号',
    `column_name` varchar(200) NOT NULL COMMENT '字段名',
    `data_type` varchar(100) NOT NULL COMMENT '字段类型',
    `column_comment` varchar(500) NOT NULL COMMENT '字段描述',
    `nullable` bit(1) NOT NULL COMMENT '是否允许为空',
    `primary_key` bit(1) NOT NULL COMMENT '是否主键',
    `ordinal_position` int NOT NULL COMMENT '排序',
    `java_type` varchar(32) NOT NULL COMMENT 'Java 属性类型',
    `java_field` varchar(64) NOT NULL COMMENT 'Java 属性名',
    `dict_type` varchar(200) DEFAULT '' COMMENT '字典类型',
    `example` varchar(64) DEFAULT NULL COMMENT '数据示例',
    `create_operation` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否为 Create 创建操作的字段',
    `update_operation` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否为 Update 更新操作的字段',
    `list_operation` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否为 List 查询操作的字段',
    `list_operation_condition` varchar(32) NOT NULL DEFAULT '=' COMMENT 'List 查询操作的条件类型',
    `list_operation_result` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否为 List 查询操作的返回字段',
    `html_type` varchar(32) NOT NULL COMMENT '显示类型',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_table_id` (`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代码生成表字段定义';

SET FOREIGN_KEY_CHECKS = 1;
