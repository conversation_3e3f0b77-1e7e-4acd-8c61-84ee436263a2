#!/bin/bash

# =============================================
# 调试通用数据库分析器的参数传递
# =============================================

echo "========================================"
echo "调试通用数据库分析器参数传递"
echo "========================================"

# 默认值
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="123456"
DB_NAME="sports_gaming"
OUTPUT_DIR="./debug-analysis"
WITH_STRUCTURE="false"
WITH_ER_DIAGRAM="false"
WITH_DATA_DICT="false"

echo "=== 默认参数值 ==="
echo "DB_HOST: $DB_HOST"
echo "DB_PORT: $DB_PORT"
echo "DB_USER: $DB_USER"
echo "DB_PASSWORD: $DB_PASSWORD"
echo "DB_NAME: $DB_NAME"
echo "OUTPUT_DIR: $OUTPUT_DIR"
echo "WITH_STRUCTURE: $WITH_STRUCTURE"
echo "WITH_ER_DIAGRAM: $WITH_ER_DIAGRAM"
echo "WITH_DATA_DICT: $WITH_DATA_DICT"
echo

# 解析命令行参数
echo "=== 解析命令行参数 ==="
echo "传入参数: $@"
echo "参数数量: $#"

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--database)
            DB_NAME="$2"
            echo "设置 DB_NAME = $DB_NAME"
            shift 2
            ;;
        --database=*)
            DB_NAME="${1#*=}"
            echo "设置 DB_NAME = $DB_NAME"
            shift
            ;;
        -u|--user)
            DB_USER="$2"
            echo "设置 DB_USER = $DB_USER"
            shift 2
            ;;
        --user=*)
            DB_USER="${1#*=}"
            echo "设置 DB_USER = $DB_USER"
            shift
            ;;
        -p|--password)
            DB_PASSWORD="$2"
            echo "设置 DB_PASSWORD = $DB_PASSWORD"
            shift 2
            ;;
        --password=*)
            DB_PASSWORD="${1#*=}"
            echo "设置 DB_PASSWORD = $DB_PASSWORD"
            shift
            ;;
        -H|--host)
            DB_HOST="$2"
            echo "设置 DB_HOST = $DB_HOST"
            shift 2
            ;;
        --host=*)
            DB_HOST="${1#*=}"
            echo "设置 DB_HOST = $DB_HOST"
            shift
            ;;
        -P|--port)
            DB_PORT="$2"
            echo "设置 DB_PORT = $DB_PORT"
            shift 2
            ;;
        --port=*)
            DB_PORT="${1#*=}"
            echo "设置 DB_PORT = $DB_PORT"
            shift
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            echo "设置 OUTPUT_DIR = $OUTPUT_DIR"
            shift 2
            ;;
        --output=*)
            OUTPUT_DIR="${1#*=}"
            echo "设置 OUTPUT_DIR = $OUTPUT_DIR"
            shift
            ;;
        --with-structure)
            WITH_STRUCTURE="true"
            echo "设置 WITH_STRUCTURE = $WITH_STRUCTURE"
            shift
            ;;
        --with-er-diagram)
            WITH_ER_DIAGRAM="true"
            echo "设置 WITH_ER_DIAGRAM = $WITH_ER_DIAGRAM"
            shift
            ;;
        --with-data-dict)
            WITH_DATA_DICT="true"
            echo "设置 WITH_DATA_DICT = $WITH_DATA_DICT"
            shift
            ;;
        -h|--help)
            echo "显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            shift
            ;;
    esac
done

echo
echo "=== 最终参数值 ==="
echo "DB_HOST: $DB_HOST"
echo "DB_PORT: $DB_PORT"
echo "DB_USER: $DB_USER"
echo "DB_PASSWORD: $DB_PASSWORD"
echo "DB_NAME: $DB_NAME"
echo "OUTPUT_DIR: $OUTPUT_DIR"
echo "WITH_STRUCTURE: $WITH_STRUCTURE"
echo "WITH_ER_DIAGRAM: $WITH_ER_DIAGRAM"
echo "WITH_DATA_DICT: $WITH_DATA_DICT"
echo

echo "=== 条件检查 ==="
if [ "$WITH_STRUCTURE" = "true" ]; then
    echo "✅ 将生成表结构详情"
else
    echo "❌ 不会生成表结构详情"
fi

if [ "$WITH_ER_DIAGRAM" = "true" ]; then
    echo "✅ 将生成ER图"
else
    echo "❌ 不会生成ER图"
fi

if [ "$WITH_DATA_DICT" = "true" ]; then
    echo "✅ 将生成数据字典"
else
    echo "❌ 不会生成数据字典"
fi

echo
echo "=== 测试数据库连接 ==="
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    echo "✅ 数据库连接成功"
else
    echo "❌ 数据库连接失败"
fi

echo
echo "=== 建议的正确命令 ==="
echo "bash scripts/universal-database-analyzer.sh \\"
echo "    --database=$DB_NAME \\"
echo "    --user=$DB_USER \\"
echo "    --password=$DB_PASSWORD \\"
echo "    --with-structure \\"
echo "    --with-er-diagram \\"
echo "    --with-data-dict"

echo
echo "========================================"
echo "调试完成！"
echo "========================================"
