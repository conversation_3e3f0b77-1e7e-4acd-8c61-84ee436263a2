-- =============================================
-- 足球彩票系统 - 后台前端表
-- 后台管理系统专用表和配置
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 组织架构管理
-- =============================================

-- 部门表
CREATE TABLE IF NOT EXISTS `system_dept` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(30) NOT NULL DEFAULT '' COMMENT '部门名称',
    `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父部门id',
    `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
    `leader_user_id` bigint DEFAULT NULL COMMENT '负责人',
    `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
    `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
    `status` tinyint NOT NULL COMMENT '部门状态（0正常 1停用）',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 岗位信息表
CREATE TABLE IF NOT EXISTS `system_post` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `code` varchar(64) NOT NULL COMMENT '岗位编码',
    `name` varchar(50) NOT NULL COMMENT '岗位名称',
    `sort` int NOT NULL COMMENT '显示顺序',
    `status` tinyint NOT NULL COMMENT '状态（0正常 1停用）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位信息表';

-- 通知公告表
CREATE TABLE IF NOT EXISTS `system_notice` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '公告ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `title` varchar(50) NOT NULL COMMENT '公告标题',
    `content` text NOT NULL COMMENT '公告内容',
    `type` tinyint NOT NULL COMMENT '公告类型（1通知 2公告）',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知公告表';

-- =============================================
-- 文件管理
-- =============================================

-- 文件表
CREATE TABLE IF NOT EXISTS `infra_file` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `config_id` bigint DEFAULT NULL COMMENT '配置编号',
    `name` varchar(256) DEFAULT NULL COMMENT '文件名',
    `path` varchar(512) NOT NULL COMMENT '文件路径',
    `url` varchar(1024) NOT NULL COMMENT '文件 URL',
    `type` varchar(128) DEFAULT NULL COMMENT '文件类型',
    `size` int NOT NULL COMMENT '文件大小',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件表';

-- 文件配置表
CREATE TABLE IF NOT EXISTS `infra_file_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(63) NOT NULL COMMENT '配置名',
    `storage` tinyint NOT NULL COMMENT '存储器',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `master` bit(1) NOT NULL COMMENT '是否为主配置',
    `config` varchar(4096) NOT NULL COMMENT '存储配置',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件配置表';

SET FOREIGN_KEY_CHECKS = 1;
