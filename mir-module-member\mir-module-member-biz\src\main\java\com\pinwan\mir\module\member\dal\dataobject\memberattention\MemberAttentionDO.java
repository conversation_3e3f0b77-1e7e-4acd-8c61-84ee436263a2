package com.pinwan.mir.module.member.dal.dataobject.memberattention;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.pinwan.mir.framework.tenant.core.db.TenantBaseDO;

/**
 * 用户关注 DO
 *
 * <AUTHOR>
 */
@TableName("member_attention")
@KeySequence("member_attention_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberAttentionDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 作者id
     */
    private Long authorId;
    /**
     * 关注时间
     */
    private Date attentionTime;
    /**
     * 关注状态 0.关注  1.取消关注
     */
    private Integer status;

}
