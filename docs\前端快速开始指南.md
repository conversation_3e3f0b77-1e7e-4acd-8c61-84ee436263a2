# 足球彩票系统前端快速开始指南

## 🚀 快速开始

### 环境要求
- Node.js 16.0+
- npm 7.0+ 或 yarn 1.22+
- Git

### 1. 项目克隆
```bash
# 克隆主项目
git clone <repository-url>
cd football-lottery

# 进入前端项目目录（选择一个版本）
cd mir-ui/mir-ui-admin-vue3  # Vue3 + Element Plus版本
# 或
cd mir-ui/mir-ui-admin-vben  # Vue3 + Ant Design版本
# 或
cd mir-ui/mir-ui-admin-vue2  # Vue2 + Element UI版本
```

### 2. 安装依赖
```bash
# 使用npm
npm install

# 或使用yarn（推荐）
yarn install

# 或使用pnpm（更快）
pnpm install
```

### 3. 配置环境变量
```bash
# 复制环境变量文件
cp .env.example .env.development

# 编辑环境变量
vim .env.development
```

```bash
# .env.development 示例配置
NODE_ENV = 'development'
VUE_APP_TITLE = '足球彩票管理系统'
VUE_APP_BASE_API = '/dev-api'
VUE_APP_TENANT_ENABLE = true
VUE_APP_CAPTCHA_ENABLE = true
VUE_APP_DOC_ENABLE = true
```

### 4. 启动开发服务器
```bash
# 启动开发服务器
npm run dev
# 或
yarn dev

# 服务器启动后访问
# http://localhost:3000
```

### 5. 默认登录账号
```
管理员账号：admin
密码：admin123

普通用户：test
密码：test123
```

## 📁 项目版本选择

### Vue3 + Element Plus（推荐）
- **路径**: `mir-ui/mir-ui-admin-vue3`
- **特点**: 最新技术栈，性能优秀，TypeScript支持
- **适用**: 新项目开发，追求最新技术

### Vue3 + Ant Design Vue（企业级）
- **路径**: `mir-ui/mir-ui-admin-vben`
- **特点**: 企业级组件库，功能丰富，设计精美
- **适用**: 大型企业项目，复杂业务场景

### Vue2 + Element UI（稳定版）
- **路径**: `mir-ui/mir-ui-admin-vue2`
- **特点**: 技术成熟稳定，兼容性好
- **适用**: 维护现有项目，团队技术栈保守

### uni-app移动端
- **路径**: `mir-ui/mir-ui-admin-uniapp`
- **特点**: 跨平台，一套代码多端运行
- **适用**: 移动端管理，小程序开发

## 🛠️ 开发工具推荐

### IDE配置
```json
// .vscode/settings.json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "emmet.includeLanguages": {
    "vue": "html"
  },
  "typescript.preferences.quoteStyle": "single",
  "javascript.preferences.quoteStyle": "single"
}
```

### 推荐插件
- **Vue Language Features (Volar)** - Vue3支持
- **TypeScript Vue Plugin (Volar)** - Vue TypeScript支持
- **ESLint** - 代码规范检查
- **Prettier** - 代码格式化
- **Auto Rename Tag** - 自动重命名标签
- **Bracket Pair Colorizer** - 括号配对着色
- **GitLens** - Git增强
- **Thunder Client** - API测试

## 📋 常用命令

### 开发命令
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 代码格式化
npm run lint

# 修复代码格式
npm run lint:fix

# 类型检查（TypeScript项目）
npm run type-check
```

### 测试命令
```bash
# 运行单元测试
npm run test:unit

# 运行E2E测试
npm run test:e2e

# 测试覆盖率
npm run test:coverage
```

## 🔧 开发配置

### 代理配置
```javascript
// vite.config.js
export default defineConfig({
  server: {
    port: 3000,
    proxy: {
      '/dev-api': {
        target: 'http://localhost:48080',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/dev-api/, '/admin-api')
      }
    }
  }
})
```

### ESLint配置
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'plugin:vue/vue3-essential',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier'
  ],
  rules: {
    'vue/multi-word-component-names': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    'prettier/prettier': 'error'
  }
}
```

## 🎨 主题定制

### 自定义主题色
```scss
// styles/variables.scss
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;

// 导入Element Plus变量
@use 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': $primary-color,
    ),
  )
);
```

### 暗色主题
```javascript
// utils/theme.js
export function toggleDarkMode() {
  const html = document.documentElement
  const isDark = html.classList.contains('dark')
  
  if (isDark) {
    html.classList.remove('dark')
    localStorage.setItem('theme', 'light')
  } else {
    html.classList.add('dark')
    localStorage.setItem('theme', 'dark')
  }
}
```

## 📱 响应式开发

### 断点配置
```scss
// styles/mixins.scss
$breakpoints: (
  'xs': 0,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1600px
);

@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}
```

### 移动端适配
```vue
<template>
  <div class="responsive-container">
    <el-row :gutter="isMobile ? 0 : 20">
      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <!-- 内容 -->
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  computed: {
    isMobile() {
      return this.$store.getters.device === 'mobile'
    }
  }
}
</script>
```

## 🔐 权限控制

### 路由权限
```javascript
// router/permission.js
import router from './index'
import store from '@/store'
import { getToken } from '@/utils/auth'

router.beforeEach(async (to, from, next) => {
  const hasToken = getToken()
  
  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      const hasRoles = store.getters.roles && store.getters.roles.length > 0
      if (hasRoles) {
        next()
      } else {
        try {
          const { roles } = await store.dispatch('user/getInfo')
          const accessRoutes = await store.dispatch('permission/generateRoutes', roles)
          router.addRoute(accessRoutes)
          next({ ...to, replace: true })
        } catch (error) {
          await store.dispatch('user/resetToken')
          next(`/login?redirect=${to.path}`)
        }
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
    }
  }
})
```

### 按钮权限
```vue
<template>
  <div>
    <el-button 
      v-if="checkPermission(['system:user:create'])"
      type="primary"
      @click="handleAdd"
    >
      新增用户
    </el-button>
  </div>
</template>

<script>
import { checkPermission } from '@/utils/permission'

export default {
  methods: {
    checkPermission
  }
}
</script>
```

## 📊 状态管理

### Pinia使用（Vue3推荐）
```javascript
// stores/user.js
import { defineStore } from 'pinia'
import { login, getInfo } from '@/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: '',
    userInfo: null,
    roles: []
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    userName: (state) => state.userInfo?.name || ''
  },
  
  actions: {
    async login(loginForm) {
      const response = await login(loginForm)
      this.token = response.data.token
      return response
    },
    
    async getUserInfo() {
      const response = await getInfo()
      this.userInfo = response.data.user
      this.roles = response.data.roles
      return response
    }
  }
})
```

## 🚀 构建部署

### 构建优化
```javascript
// vite.config.js
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          element: ['element-plus'],
          utils: ['axios', 'lodash']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
})
```

### Docker部署
```dockerfile
# Dockerfile
FROM node:16-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:stable-alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🐛 常见问题

### 1. 依赖安装失败
```bash
# 清除缓存
npm cache clean --force
# 或
yarn cache clean

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 2. 端口被占用
```bash
# 查看端口占用
lsof -i :3000

# 杀死进程
kill -9 <PID>

# 或修改端口
npm run dev -- --port 3001
```

### 3. 代理不生效
检查vite.config.js中的proxy配置，确保target地址正确

### 4. 热更新不生效
检查文件路径是否正确，确保在src目录下

## 📞 技术支持

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 📱 微信群: 扫码加入
- 🐛 问题反馈: GitHub Issues

---

**🎯 开发建议**:
1. 优先使用Vue3 + Element Plus版本
2. 遵循项目代码规范和最佳实践
3. 及时更新依赖版本，关注安全漏洞
4. 编写单元测试，保证代码质量
5. 使用TypeScript提高代码可维护性
