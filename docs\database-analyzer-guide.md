# 通用数据库分析器使用指南

## 📊 功能概览

增强的通用数据库分析器提供以下功能：

1. **🏗️ 表分类分析** - 智能识别表前缀，自动分类
2. **📊 表结构详情** - 生成每个表的详细字段信息
3. **📖 数据字典** - 完整的数据库文档
4. **🎨 ER图生成** - Mermaid格式的实体关系图
5. **📈 统计分析** - 数据库规模和结构统计

## 🚀 快速开始

### 基础分析
```bash
# 最简单的使用方式
./scripts/universal-database-analyzer.sh -d your_database -u root -p your_password
```

### 完整分析
```bash
# 生成所有类型的分析报告
./scripts/universal-database-analyzer.sh \
    --database=your_database \
    --user=root \
    --password=your_password \
    --with-structure \
    --with-er-diagram \
    --with-data-dict
```

### 足球彩票项目专用
```bash
# 使用项目专用脚本
chmod +x scripts/analyze-football-lottery-db.sh
./scripts/analyze-football-lottery-db.sh
```

## 📋 命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `-d, --database` | 数据库名称（必需） | `-d mydb` |
| `-u, --user` | 数据库用户 | `-u root` |
| `-p, --password` | 数据库密码 | `-p mypass` |
| `-H, --host` | 数据库主机 | `-H localhost` |
| `-P, --port` | 数据库端口 | `-P 3306` |
| `-o, --output` | 输出目录 | `-o ./analysis` |
| `-c, --config` | 配置文件 | `-c db.conf` |
| `--with-structure` | 生成表结构详情 | |
| `--with-er-diagram` | 生成ER图 | |
| `--with-data-dict` | 生成数据字典 | |

## 📄 输出文件说明

### 1. 基础分析报告 (`database-analysis-YYYYMMDD-HHMMSS.md`)
- 数据库概览信息
- 智能表分类结果
- 前缀模式分析
- 统计信息和建议

### 2. 表结构详情 (`table-structure-YYYYMMDD-HHMMSS.md`)
- 每个表的详细字段信息
- 字段类型、约束、默认值
- 索引信息
- 表注释和字段注释

### 3. 数据字典 (`data-dictionary-YYYYMMDD-HHMMSS.md`)
- 表概览统计
- 完整的字段清单
- 标准化的文档格式
- 便于打印和分享

### 4. ER图 (`er-diagram-YYYYMMDD-HHMMSS.mmd`)
- Mermaid格式的实体关系图
- 包含所有表和字段
- 自动推断的表关系
- 可在线查看和编辑

## 🔧 配置文件使用

创建配置文件 `database.conf`：
```bash
# 数据库连接配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=your_database

# 分析选项
WITH_STRUCTURE=true
WITH_ER_DIAGRAM=true
WITH_DATA_DICT=true
```

使用配置文件：
```bash
./scripts/universal-database-analyzer.sh --config=database.conf
```

## 🎨 ER图查看方法

### 在线查看
1. 访问 [Mermaid Live Editor](https://mermaid.live/)
2. 复制生成的 `.mmd` 文件内容
3. 粘贴到编辑器中查看

### 本地查看
```bash
# 安装mermaid-cli
npm install -g @mermaid-js/mermaid-cli

# 生成PNG图片
mmdc -i er-diagram.mmd -o er-diagram.png
```

### VS Code查看
安装 "Mermaid Preview" 扩展，直接在VS Code中预览。

## 📊 表分类规则

分析器会根据表前缀自动分类：

| 前缀 | 分类 | 说明 |
|------|------|------|
| `system_` | 系统管理模块 | 用户权限、配置等 |
| `member_` | 会员管理模块 | 用户、积分、等级等 |
| `pay_` | 支付管理模块 | 订单、钱包、支付等 |
| `mp_` | 微信公众号模块 | 微信集成功能 |
| `qrtz_` | 任务调度模块 | Quartz调度器 |
| `infra_` | 基础设施模块 | 文件、配置等 |
| `match_` | 赛事管理模块 | 比赛、球队等 |
| `author_` | 作者管理模块 | 内容创作等 |

## 💡 使用技巧

### 1. 批量分析多个数据库
```bash
for db in db1 db2 db3; do
    ./scripts/universal-database-analyzer.sh -d $db -u root -p pass --with-structure
done
```

### 2. 定期生成文档
```bash
# 添加到crontab，每周生成一次
0 2 * * 1 /path/to/universal-database-analyzer.sh --config=/path/to/db.conf --with-data-dict
```

### 3. 集成到CI/CD
```yaml
# GitHub Actions示例
- name: Generate Database Documentation
  run: |
    ./scripts/universal-database-analyzer.sh \
      --database=${{ secrets.DB_NAME }} \
      --user=${{ secrets.DB_USER }} \
      --password=${{ secrets.DB_PASSWORD }} \
      --with-data-dict
```

## ⚠️ 注意事项

1. **权限要求**: 需要数据库的读取权限
2. **大型数据库**: 对于表数量很多的数据库，分析可能需要较长时间
3. **字符编码**: 确保数据库使用UTF-8编码以正确显示中文注释
4. **网络连接**: 远程数据库需要确保网络连接稳定

## 🆘 常见问题

### Q: 生成的ER图关系不准确？
A: ER图的关系是基于字段命名约定推断的（如`user_id`关联到`user`表）。如果命名不规范，关系可能不准确。

### Q: 表结构分析很慢？
A: 对于大型数据库，可以只生成基础分析报告，跳过详细的表结构分析。

### Q: 中文注释显示乱码？
A: 确保数据库和表使用UTF-8字符集，并且终端支持UTF-8显示。

## 📞 技术支持

如有问题或建议，请：
1. 检查数据库连接和权限
2. 查看生成的日志信息
3. 联系系统管理员

---

**工具版本**: v2.0 (增强版)
**更新时间**: 2025-01-10
**维护者**: 数据库团队
