#!/bin/bash

# =============================================
# 测试表结构获取脚本
# 专门测试单个表的字段信息获取
# =============================================

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 获取第一个表名进行测试
TABLE_NAME=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null | head -1)

if [ -z "$TABLE_NAME" ]; then
    echo "❌ 无法获取表名"
    exit 1
fi

echo "测试表: $TABLE_NAME"
echo "数据库: $DB_NAME"
echo

# 方法1: 使用INFORMATION_SCHEMA
echo "=== 方法1: INFORMATION_SCHEMA ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT 
        COLUMN_NAME as '字段名',
        COLUMN_TYPE as '类型',
        IS_NULLABLE as '允许空值',
        COLUMN_KEY as '键',
        COLUMN_DEFAULT as '默认值',
        COLUMN_COMMENT as '注释'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$TABLE_NAME' 
    ORDER BY ORDINAL_POSITION;" 2>/dev/null

echo
echo "=== 方法2: DESCRIBE ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "DESCRIBE $TABLE_NAME;" 2>/dev/null

echo
echo "=== 方法3: SHOW COLUMNS ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW COLUMNS FROM $TABLE_NAME;" 2>/dev/null

echo
echo "=== 测试批处理模式 ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT 
        COLUMN_NAME,
        COLUMN_TYPE,
        IS_NULLABLE,
        IFNULL(COLUMN_KEY, ''),
        IFNULL(COLUMN_DEFAULT, ''),
        IFNULL(COLUMN_COMMENT, '')
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$TABLE_NAME' 
    ORDER BY ORDINAL_POSITION;" \
    --batch --skip-column-names 2>/dev/null > /tmp/test_columns.txt

echo "输出到文件的结果:"
if [ -s "/tmp/test_columns.txt" ]; then
    echo "✅ 成功获取到 $(wc -l < /tmp/test_columns.txt) 行数据"
    echo "前3行内容:"
    head -3 /tmp/test_columns.txt | while IFS=$'\t' read col_name col_type is_null col_key col_default col_comment; do
        echo "  字段: $col_name, 类型: $col_type, 注释: $col_comment"
    done
else
    echo "❌ 未获取到数据"
fi

rm -f /tmp/test_columns.txt

echo
echo "=== 测试表注释 ==="
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$TABLE_NAME';" \
    --batch --skip-column-names 2>/dev/null
