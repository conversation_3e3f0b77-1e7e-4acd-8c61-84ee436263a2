# 数据库表结构详情

## 📊 数据库: sports_gaming

**生成时间**: 2025年07月16日 11:07:26
**处理表数**: 5

### 表: account_statistic

**表说明**: 

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| day | date | ❌ |  |  | 日期 |
| account_id | bigint(20) | ❌ |  |  | 公众号id |
| user_id | bigint(20) | ❌ |  |  | 所属主播id |
| article_num | int(11) | ❌ |  | 0 | 发布文章数量 |
| read_num | int(11) | ❌ |  | 0 | 阅读人数 |
| read_percent | decimal(10,2) | ❌ |  | 0.00 | 阅读率 |
| total_pay_num | int(11) | ❌ |  | 0 | 总付费人数 |
| total_pay_percent | decimal(10,2) | ❌ |  | 0.00 | 总付费率 |
| new_pay_num | int(11) | ❌ |  | 0 | 新人付费人数 |
| new_pay_percent | decimal(10,2) | ❌ |  | 0.00 | 新人付费率 |
| new_pay_price_avg | decimal(10,2) | ❌ |  | 0.00 | 新人客单价 |
| creator | varchar(64) | ✅ |  |  | 创建者 |
| create_time | datetime | ❌ |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | ✅ |  |  | 更新者 |
| update_time | datetime | ❌ |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | ❌ |  | b'0' | 是否删除 |

---

### 表: account_users

**表说明**: 公众号用户信息

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  |  |
| account_id | bigint(20) | ❌ |  |  | 公众号id |
| app_id | varchar(255) | ❌ |  |  | 公众号appid |
| open_id | varchar(255) | ❌ | 📇 FK |  | 用户openid |
| union_id | varchar(255) | ❌ | 📇 FK |  | 用户unionid |
| subscribe_time | datetime | ❌ |  |  | 关注时间 |
| creator | varchar(64) | ✅ |  |  | 创建者 |
| create_time | datetime | ❌ |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | ✅ |  |  | 更新者 |
| update_time | datetime | ❌ |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | ❌ |  | b'0' | 是否删除 |

---

### 表: article_push_config

**表说明**: 文章推送设置

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  | id |
| article_id | bigint(20) | ❌ |  |  | 文章id |
| author_id | bigint(20) | ❌ |  |  | 作者id |
| consume_status | int(10) | ❌ |  | 0 | 消费状态 0 全部 -1 不推送 1 有条件推送 |
| consume_min_num | int(11) | ✅ |  |  | 最小消费次数 |
| consume_max_num | int(11) | ✅ |  |  | 最大消费次数 |
| consume_min_amount | decimal(10,2) | ✅ |  |  | 最小消费金额 |
| consume_max_amount | decimal(10,2) | ✅ |  |  | 最大消费金额 |
| push_time | datetime | ❌ |  |  | 推送时间 |
| template_id | varchar(255) | ✅ |  |  | 推送模板id |
| idx | int(3) | ✅ |  | 1 | 标识位 |

---

### 表: author_accomplishment

**表说明**: 作者战绩

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) | ❌ | 🔑 PK |  | id |
| author_id | bigint(20) | ❌ |  |  | 作者id |
| show_data | mediumtext | ❌ |  |  | 展示数据 |
| show_pic | varchar(255) | ✅ |  |  | 分享图片 |
| creator | varchar(64) | ✅ |  |  | 创建者 |
| create_time | datetime | ❌ |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | ✅ |  |  | 更新者 |
| update_time | datetime | ❌ |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | ❌ |  | b'0' | 是否删除 |

---

### 表: author_article

**表说明**: 文章

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 注释 |
|--------|------|----------|-----|--------|------|
| id | bigint(20) unsigned | ❌ | 🔑 PK |  | ID |
| author_id | bigint(20) | ❌ | 📇 FK |  | 作者ID |
| title | varchar(255) | ✅ |  |  | 标题 |
| intro | varchar(512) | ✅ |  |  | 简介 |
| free_contents | longtext | ✅ |  |  | 免费内容 |
| contents | longtext | ✅ |  |  | 新闻内容 |
| start_time | datetime | ✅ |  |  | 显示时间 |
| second_push_time | datetime | ✅ |  |  | 二次推送时间 |
| end_time | datetime | ✅ |  |  | 下架时间 |
| price | decimal(10,2) | ❌ |  | 0.00 | 价格 |
| refund_type | int(11) | ❌ |  | 0 | 不中退款：0：否  1：是 |
| win | int(11) | ❌ |  | 0 | 红黑：0：未知  1：红  2：黑 3:走水 4:2中1 5:3中2 6:4中3 7:被绝杀 |
| win_name | varchar(50) | ✅ |  |  | 红黑描述 |
| win_exc | int(11) | ❌ |  | 0 | 0:待处理 1:收款 2：退款 |
| state | tinyint(4) | ❌ |  | 0 | 状态：0：正常 1：封禁 |
| conclusion | varchar(255) | ✅ |  |  | 结语 |
| creator | varchar(255) | ✅ |  |  | 创建人 |
| updater | varchar(255) | ✅ |  |  | 更新人 |
| create_time | timestamp | ✅ |  |  | 创建时间 |
| update_time | timestamp | ✅ |  |  | 更新时间 |
| deleted | tinyint(4) | ✅ |  | 0 | 删除标志 |
| tenant_id | bigint(20) | ✅ |  | 1 | 租户ID |
| share_pic_url | varchar(255) | ✅ |  |  | 分享图片地址 |
| top | int(10) | ✅ |  | 0 | 是否置顶 |
| top_time | datetime | ✅ |  |  | 置顶时间 |
| status | int(11) | ❌ |  | 1 | 上架状态 0:已下架，1：已上架 |
| consume_status | int(11) | ❌ |  | 0 | 是否有消费条件 0 无 -1 有但是不公开 1 有条件 |
| consume_min_num | int(11) | ✅ |  |  | 最小消费次数 |
| consume_max_num | int(11) | ✅ |  |  | 最大消费次数 |
| consume_min_amount | decimal(10,2) | ✅ |  |  | 最低消费金额 |
| consume_max_amount | decimal(10,2) | ✅ |  |  | 最大消费金额 |
| add_free_content | longtext | ✅ |  |  | 追加免费内容 |
| add_content | longtext | ✅ |  |  | 追加付费内容 |
| scheme_play | bigint(20) | ✅ |  |  | 方案玩法 |
| match_scheme | text | ✅ |  |  | 比赛方案 |
| auto_replacement | tinyint(4) | ✅ |  | 0 | 自动补单 0:否 1:是 |
| match_ids | varchar(255) | ✅ |  |  | 关联比赛id |
| draft | tinyint(4) | ✅ |  | 0 | 0 非草稿  1.草稿 |
| recommend_win | int(10) | ✅ |  |  | 推荐结果 |
| recommend_win_name | varchar(100) | ✅ |  |  | 推荐结果描述 |
| fourteen | tinyint(3) | ✅ |  | 0 | 是否14场 0.否  1.是 |
| issue | varchar(100) | ✅ |  |  | 体彩期数 |
| accomplishment | varchar(255) | ✅ |  |  | 引用的战绩ID |
| top_bg | varchar(255) | ✅ |  |  | 文章方案顶部背景 |

---

