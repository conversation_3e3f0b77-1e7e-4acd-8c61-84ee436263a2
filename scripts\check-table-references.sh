#!/bin/bash

# =============================================
# 检查表在代码中的引用情况
# 分析哪些表在Java代码中被使用
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "========================================"
echo "表代码引用检查工具"
echo "========================================"

# 检查是否在项目根目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 请在项目根目录执行此脚本"
    exit 1
fi

# 创建输出目录
mkdir -p docs/database

log_info "获取数据库表列表..."

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

# 获取所有表名
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null > /tmp/all_tables.txt

if [ $? -ne 0 ]; then
    echo "❌ 获取表列表失败"
    exit 1
fi

TOTAL_TABLES=$(wc -l < /tmp/all_tables.txt)
log_success "发现 $TOTAL_TABLES 个表"

log_info "分析代码中的表引用..."

# 生成引用分析报告
cat > docs/database/table-references-analysis.md << 'EOF'
# 数据库表代码引用分析

## 📊 概览

本报告分析项目代码中对数据库表的引用情况，帮助识别未使用的表。

## 🔍 分析方法

1. **DO实体类检查**: 查找对应的DO实体类文件
2. **@TableName注解**: 检查MyBatis Plus的表名注解
3. **SQL文件引用**: 检查XML和SQL文件中的表引用
4. **字符串引用**: 检查Java代码中的字符串引用

## 📋 详细分析结果

EOF

# 分析每个表的引用情况
> /tmp/referenced_tables.txt
> /tmp/unreferenced_tables.txt

while read table_name; do
    if [ -n "$table_name" ]; then
        log_info "检查表: $table_name"
        
        FOUND_REFERENCES=0
        REFERENCE_INFO=""
        
        # 1. 检查DO实体类
        # 将表名转换为类名格式 (例: member_user -> MemberUser)
        CLASS_NAME=$(echo "$table_name" | sed 's/_\([a-z]\)/\U\1/g' | sed 's/^./\U&/')
        DO_CLASS="${CLASS_NAME}DO"
        
        DO_FILES=$(find . -name "*.java" -type f -exec grep -l "class.*${DO_CLASS}" {} \; 2>/dev/null)
        if [ -n "$DO_FILES" ]; then
            FOUND_REFERENCES=1
            REFERENCE_INFO="$REFERENCE_INFO\n  - DO实体类: $DO_FILES"
        fi
        
        # 2. 检查@TableName注解
        TABLENAME_FILES=$(find . -name "*.java" -type f -exec grep -l "@TableName.*$table_name" {} \; 2>/dev/null)
        if [ -n "$TABLENAME_FILES" ]; then
            FOUND_REFERENCES=1
            REFERENCE_INFO="$REFERENCE_INFO\n  - @TableName注解: $TABLENAME_FILES"
        fi
        
        # 3. 检查SQL文件引用
        SQL_FILES=$(find . -name "*.xml" -o -name "*.sql" -type f -exec grep -l "$table_name" {} \; 2>/dev/null)
        if [ -n "$SQL_FILES" ]; then
            FOUND_REFERENCES=1
            REFERENCE_INFO="$REFERENCE_INFO\n  - SQL文件引用: $SQL_FILES"
        fi
        
        # 4. 检查Java代码中的字符串引用
        JAVA_STRING_FILES=$(find . -name "*.java" -type f -exec grep -l "\".*$table_name.*\"" {} \; 2>/dev/null)
        if [ -n "$JAVA_STRING_FILES" ]; then
            FOUND_REFERENCES=1
            REFERENCE_INFO="$REFERENCE_INFO\n  - Java字符串引用: $JAVA_STRING_FILES"
        fi
        
        # 记录结果
        if [ $FOUND_REFERENCES -eq 1 ]; then
            echo "$table_name" >> /tmp/referenced_tables.txt
            echo "### ✅ $table_name" >> docs/database/table-references-analysis.md
            echo "" >> docs/database/table-references-analysis.md
            echo "**引用情况**:" >> docs/database/table-references-analysis.md
            echo -e "$REFERENCE_INFO" >> docs/database/table-references-analysis.md
            echo "" >> docs/database/table-references-analysis.md
        else
            echo "$table_name" >> /tmp/unreferenced_tables.txt
        fi
    fi
done < /tmp/all_tables.txt

# 统计结果
REFERENCED_COUNT=$(wc -l < /tmp/referenced_tables.txt 2>/dev/null || echo "0")
UNREFERENCED_COUNT=$(wc -l < /tmp/unreferenced_tables.txt 2>/dev/null || echo "0")

# 添加未引用的表
echo "## ❌ 未找到代码引用的表" >> docs/database/table-references-analysis.md
echo "" >> docs/database/table-references-analysis.md

if [ $UNREFERENCED_COUNT -gt 0 ]; then
    echo "以下表在代码中未找到明显引用:" >> docs/database/table-references-analysis.md
    echo "" >> docs/database/table-references-analysis.md
    
    while read table_name; do
        if [ -n "$table_name" ]; then
            # 获取表注释和行数
            TABLE_INFO=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
                -e "SELECT CONCAT(IFNULL(TABLE_COMMENT, '无注释'), ' (', IFNULL(TABLE_ROWS, 0), '行)') FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '$table_name';" \
                --batch --skip-column-names 2>/dev/null)
            
            echo "- **$table_name**: $TABLE_INFO" >> docs/database/table-references-analysis.md
        fi
    done < /tmp/unreferenced_tables.txt
    
    echo "" >> docs/database/table-references-analysis.md
else
    echo "✅ 所有表都在代码中有引用" >> docs/database/table-references-analysis.md
    echo "" >> docs/database/table-references-analysis.md
fi

# 添加统计信息
echo "## 📊 统计信息" >> docs/database/table-references-analysis.md
echo "" >> docs/database/table-references-analysis.md
echo "- **总表数**: $TOTAL_TABLES" >> docs/database/table-references-analysis.md
echo "- **有代码引用的表**: $REFERENCED_COUNT" >> docs/database/table-references-analysis.md
echo "- **无代码引用的表**: $UNREFERENCED_COUNT" >> docs/database/table-references-analysis.md
echo "- **引用覆盖率**: $(( REFERENCED_COUNT * 100 / TOTAL_TABLES ))%" >> docs/database/table-references-analysis.md
echo "" >> docs/database/table-references-analysis.md

# 生成删除建议
echo "## 🗑️ 删除建议" >> docs/database/table-references-analysis.md
echo "" >> docs/database/table-references-analysis.md

if [ $UNREFERENCED_COUNT -gt 0 ]; then
    echo "### 可能可以删除的表" >> docs/database/table-references-analysis.md
    echo "" >> docs/database/table-references-analysis.md
    echo "以下表在代码中未找到引用，可能可以删除（请仔细确认）:" >> docs/database/table-references-analysis.md
    echo "" >> docs/database/table-references-analysis.md
    echo '```sql' >> docs/database/table-references-analysis.md
    echo "-- 未找到代码引用的表（请确认后删除）" >> docs/database/table-references-analysis.md
    
    while read table_name; do
        if [ -n "$table_name" ]; then
            echo "-- DROP TABLE IF EXISTS $table_name; -- 请确认是否需要" >> docs/database/table-references-analysis.md
        fi
    done < /tmp/unreferenced_tables.txt
    
    echo '```' >> docs/database/table-references-analysis.md
    echo "" >> docs/database/table-references-analysis.md
    
    # 特别标注demo表
    echo "### 特别关注的表" >> docs/database/table-references-analysis.md
    echo "" >> docs/database/table-references-analysis.md
    
    DEMO_UNREFERENCED=$(grep -E "demo|test|temp|sample" /tmp/unreferenced_tables.txt 2>/dev/null)
    if [ -n "$DEMO_UNREFERENCED" ]; then
        echo "以下表看起来是demo/测试表，优先考虑删除:" >> docs/database/table-references-analysis.md
        echo "" >> docs/database/table-references-analysis.md
        echo '```sql' >> docs/database/table-references-analysis.md
        echo "-- Demo/测试表（优先删除）" >> docs/database/table-references-analysis.md
        echo "$DEMO_UNREFERENCED" | while read table; do
            echo "DROP TABLE IF EXISTS $table;" >> docs/database/table-references-analysis.md
        done
        echo '```' >> docs/database/table-references-analysis.md
        echo "" >> docs/database/table-references-analysis.md
    fi
fi

echo "## ⚠️ 注意事项" >> docs/database/table-references-analysis.md
echo "" >> docs/database/table-references-analysis.md
echo "1. **代码引用检查的局限性**:" >> docs/database/table-references-analysis.md
echo "   - 可能存在动态表名的情况" >> docs/database/table-references-analysis.md
echo "   - 可能存在通过配置文件引用的表" >> docs/database/table-references-analysis.md
echo "   - 可能存在第三方工具直接操作的表" >> docs/database/table-references-analysis.md
echo "" >> docs/database/table-references-analysis.md
echo "2. **删除前的确认步骤**:" >> docs/database/table-references-analysis.md
echo "   - 检查表中是否有重要数据" >> docs/database/table-references-analysis.md
echo "   - 确认表是否被外部系统使用" >> docs/database/table-references-analysis.md
echo "   - 在测试环境先执行删除操作" >> docs/database/table-references-analysis.md
echo "   - 备份数据库" >> docs/database/table-references-analysis.md
echo "" >> docs/database/table-references-analysis.md

echo "---" >> docs/database/table-references-analysis.md
echo "**生成时间**: $(date)" >> docs/database/table-references-analysis.md

log_success "表引用分析完成: docs/database/table-references-analysis.md"

# 生成简要的删除脚本
if [ $UNREFERENCED_COUNT -gt 0 ]; then
    cat > docs/database/cleanup-unreferenced-tables.sql << 'EOF'
-- =============================================
-- 清理未引用表的SQL脚本
-- 注意：执行前请仔细确认并备份数据库
-- =============================================

-- 设置安全模式
SET SQL_SAFE_UPDATES = 0;

EOF

    echo "-- 未找到代码引用的表" >> docs/database/cleanup-unreferenced-tables.sql
    while read table_name; do
        if [ -n "$table_name" ]; then
            echo "-- DROP TABLE IF EXISTS $table_name; -- 请确认后取消注释" >> docs/database/cleanup-unreferenced-tables.sql
        fi
    done < /tmp/unreferenced_tables.txt
    
    echo "" >> docs/database/cleanup-unreferenced-tables.sql
    echo "-- 恢复安全模式" >> docs/database/cleanup-unreferenced-tables.sql
    echo "SET SQL_SAFE_UPDATES = 1;" >> docs/database/cleanup-unreferenced-tables.sql
    
    log_success "清理脚本已生成: docs/database/cleanup-unreferenced-tables.sql"
fi

# 清理临时文件
rm -f /tmp/all_tables.txt /tmp/referenced_tables.txt /tmp/unreferenced_tables.txt

echo
echo "========================================"
echo "表引用分析完成！"
echo "========================================"
echo "📋 docs/database/table-references-analysis.md - 详细分析报告"
if [ $UNREFERENCED_COUNT -gt 0 ]; then
    echo "🗑️ docs/database/cleanup-unreferenced-tables.sql - 清理脚本"
fi
echo "========================================"
echo
echo "📊 统计结果:"
echo "  - 总表数: $TOTAL_TABLES"
echo "  - 有引用: $REFERENCED_COUNT"
echo "  - 无引用: $UNREFERENCED_COUNT"
