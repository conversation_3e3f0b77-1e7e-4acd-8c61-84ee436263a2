# 前端管理后台使用表分析

## 📊 概览

本报告分析football-lottery-frontend（后台管理前端）项目可能使用的数据库表。
基于后端API模块和业务功能进行分析。

## 🎯 分析依据

1. **后端Controller分析**: 检查各模块的Controller接口
2. **业务模块划分**: 基于系统功能模块
3. **管理后台特性**: 后台管理通常需要的功能表

## 📋 前端使用的表分类

### 1. 系统管理模块

后台管理系统的核心功能，前端必须使用：


#### 系统核心表

- **system_command**: 关键词与接口关联表 (0行) 🟢 一般
- **system_dept**: 部门表 (17行) 🟡 重要 - 部门管理
- **system_dict_data**: 字典数据表 (451行) 🟡 重要 - 字典数据
- **system_dict_type**: 字典类型表 (101行) 🟡 重要 - 字典管理
- **system_login_log**: 系统访问记录 (828行) 🟢 一般
- **system_mail_account**: 邮箱账号表 (4行) 🟢 一般
- **system_mail_log**: 邮件日志表 (0行) 🟢 一般
- **system_mail_template**: 邮件模版表 (3行) 🟢 一般
- **system_menu**: 菜单权限表 (887行) 🔴 核心 - 菜单权限
- **system_notice**: 通知公告表 (3行) 🟢 一般 - 通知公告
- **system_notify_message**: 站内信消息表 (9行) 🟢 一般
- **system_notify_template**: 站内信模板表 (0行) 🟢 一般
- **system_oauth2_access_token**: OAuth2 访问令牌 (3250行) 🟢 一般
- **system_oauth2_approve**: OAuth2 批准表 (0行) 🟢 一般
- **system_oauth2_client**: OAuth2 客户端表 (4行) 🟢 一般
- **system_oauth2_code**: OAuth2 授权码表 (0行) 🟢 一般
- **system_oauth2_refresh_token**: OAuth2 刷新令牌 (1748行) 🟢 一般
- **system_operate_log**: 操作日志记录 V2 版本 (0行) 🟢 一般
- **system_post**: 岗位信息表 (5行) 🟡 重要 - 岗位管理
- **system_role**: 角色信息表 (10行) 🔴 核心 - 角色管理
- **system_role_menu**: 角色和菜单关联表 (1510行) 🟢 一般
- **system_sensitive_word**: 敏感词 (0行) 🟢 一般
- **system_sms_channel**: 短信渠道 (0行) 🟢 一般
- **system_sms_code**: 手机验证码 (11行) 🟢 一般
- **system_sms_log**: 短信日志 (5行) 🟢 一般
- **system_sms_template**: 短信模板 (13行) 🟢 一般
- **system_social_client**: 社交客户端表 (5行) 🟢 一般
- **system_social_user**: 社交用户表 (0行) 🟢 一般
- **system_social_user_bind**: 社交绑定表 (0行) 🟢 一般
- **system_tenant**: 租户表 (3行) 🔴 核心 - 租户管理
- **system_tenant_package**: 租户套餐表 (3行) 🟢 一般
- **system_user_post**: 用户岗位表 (14行) 🟢 一般
- **system_user_role**: 用户和角色关联表 (24行) 🟢 一般
- **system_users**: 用户信息表 (22行) 🔴 核心 - 管理员登录

### 2. 会员管理模块

前端需要管理会员信息、等级、积分等：

- **member_address**:  (0行) 🟢 一般
- **member_attention**: 用户关注表 (63行) 🟢 一般
- **member_author_privilege**: 用户作者特权 (49行) 🟢 一般
- **member_bind_record**: 会员绑定记录表 (0行) 🟢 一般
- **member_config**:  (0行) 🟢 一般
- **member_experience_record**:  (0行) 🟢 一般
- **member_group**:  (0行) 🟢 一般
- **member_level**:  (0行) 🟡 重要 - 会员等级配置
- **member_level_record**:  (0行) 🟢 一般
- **member_match_attention**: 用户关注比赛信息 (14行) 🟢 一般
- **member_match_chat**: 比赛场次聊天记录 (3行) 🟢 一般
- **member_match_vote**: 比赛场次投票 (0行) 🟢 一般
- **member_point_record**:  (0行) 🟡 重要 - 积分记录查询
- **member_privilege_log**: 用户特权使用记录 (11行) 🟢 一般
- **member_settlement_info**: 用户提现账号信息 (10行) 🟢 一般
- **member_sign_in_config**:  (0行) 🟢 一般
- **member_sign_in_record**:  (0行) 🟢 一般 - 签到记录
- **member_tag**:  (0行) 🟢 一般
- **member_user**: 用户端-用户表 (45行) 🔴 核心 - 会员列表管理
- **member_user_bak**:  (0行) 🟢 一般
- **member_user_balance_logs**: 用户余额变更记录 (303行) 🟢 一般
- **member_user_ex_balance_logs**: 用户推广余额变更记录 (27行) 🟢 一般
- **member_user_gold_logs**: 用户金币变更记录 (345行) 🟢 一般

### 3. 支付管理模块

前端需要管理支付配置、订单、钱包等：

- **pay_app**:  (0行) 🔴 核心 - 支付应用配置
- **pay_bank_info**: 银行信息 (4行) 🟢 一般
- **pay_channel**:  (0行) 🟢 一般
- **pay_demo_order**:  (0行) 🟢 一般
- **pay_demo_transfer**:  (0行) 🟢 一般
- **pay_notify_log**:  (0行) 🟢 一般
- **pay_notify_task**:  (0行) 🟢 一般
- **pay_order**:  (0行) 🔴 核心 - 支付订单管理
- **pay_order_extension**:  (0行) 🟢 一般
- **pay_refund**:  (0行) 🟡 重要 - 退款管理
- **pay_transfer**:  (0行) 🟢 一般
- **pay_wallet**:  (0行) 🟡 重要 - 钱包管理
- **pay_wallet_recharge**:  (0行) 🟢 一般
- **pay_wallet_recharge_package**:  (2行) 🟢 一般
- **pay_wallet_transaction**:  (0行) 🟡 重要 - 交易记录

### 4. 业务管理模块

前端需要管理文章、球队、活动等业务数据：

- **author_accomplishment**: 作者战绩 (29行) 🟢 一般
- **author_article**: 文章 (600行) 🔴 核心 - 文章管理
- **author_article_append**: 文章 (15行) 🟡 重要 - 文章追加
- **author_article_pv_logs**: 文章PV记录 (11268行) 🟢 一般
- **author_audit**: 作者审核列表 (18行) 🟢 一般
- **author_audit_copy1**: 作者审核列表 (9行) 🟢 一般
- **author_commission_rate**: 作者分成提现设置表 (3行) 🟢 一般
- **author_day_report**: 作者日报 (2654行) 🟢 一般
- **author_hit_rate**: 作者命中率记录表 (33行) 🟢 一般
- **author_info**:  (5行) 🟢 一般
- **author_privilege_set**: 作者特权设置 (63行) 🟢 一般
- **author_withdraw_logs**: 作者提现表 (33行) 🟢 一般
- **gold_order**: 金币订单表 (183行) 🟡 重要 - 鱼币充值管理
- **match_category**: 赛事分类 (8行) 🟢 一般
- **match_coach**: 教练 (127695行) 🟢 一般
- **match_competition**: 赛事信息 (2325行) 🟢 一般
- **match_country**: 国家 (212行) 🟢 一般
- **match_future_record**: 未来赛程 (6032行) 🟢 一般
- **match_history**: 比赛记录及盘口信息 (9138行) 🟢 一般
- **match_lineup_detail**: 赛事阵容详情 (2517行) 🟢 一般
- **match_list**: 比赛场次信息 (61651行) 🟢 一般
- **match_live_info**: 比赛直播信息 (100行) 🟢 一般
- **match_odds**: 比赛开盘信息 (42行) 🟢 一般
- **match_player**: 球员 (0行) 🟢 一般
- **match_player_info**: 比赛球队球员信息 (159行) 🟢 一般
- **match_player_transfer**: 球队转会信息 (2928行) 🟢 一般
- **match_point_rank**: 联赛积分排名 (4032行) 🟢 一般
- **match_referee**: 裁判表 (4144行) 🟢 一般
- **match_season**: 赛季信息 (0行) 🟢 一般
- **match_stage**: 赛事阶段表 (38753行) 🟢 一般
- **match_stats**: 比赛数据统计表 (7084行) 🟢 一般
- **match_team**: 球队信息 (70924行) 🟡 重要 - 球队管理

### 5. 基础设施模块

前端需要的系统配置和工具：

- **infra_api_access_log**: API 访问日志表 (299703行) 🟢 一般
- **infra_api_error_log**: 系统异常日志 (1254行) 🟢 一般
- **infra_codegen_column**: 代码生成表字段定义 (396行) 🟢 一般 - 代码生成
- **infra_codegen_table**: 代码生成表定义 (38行) 🟢 一般 - 代码生成
- **infra_config**: 参数配置表 (7行) 🟢 一般
- **infra_data_source_config**: 数据源配置表 (0行) 🟢 一般
- **infra_file**: 文件表 (16行) 🟡 重要 - 文件管理
- **infra_file_config**: 文件配置表 (3行) 🟡 重要 - 文件配置
- **infra_file_content**: 文件表 (0行) 🟢 一般
- **infra_job**: 定时任务表 (24行) 🟢 一般
- **infra_job_log**: 定时任务日志表 (243412行) 🟢 一般

### 6. 微信管理模块

前端需要管理微信公众号相关功能：

- **mp_account**: 公众号账号表 (7行) 🟡 重要 - 微信账号配置
- **mp_auto_reply**: 公众号消息自动回复表 (0行) 🟢 一般
- **mp_click_logs**: 公众号关注点击记录了表 (6行) 🟢 一般
- **mp_material**: 公众号素材表 (0行) 🟢 一般
- **mp_menu**: 公众号菜单表 (4行) 🟡 重要 - 菜单配置
- **mp_message**: 公众号消息表  (23行) 🟢 一般 - 消息记录
- **mp_mini_user**: 小程序用户表 (7行) 🟢 一般
- **mp_other_even_logs**: 1w1公众扫码记录 (0行) 🟢 一般
- **mp_pay_config_log**: 微信配置记录日志 (4行) 🟢 一般
- **mp_tag**: 公众号标签表 (0行) 🟢 一般
- **mp_template_config**: 微信模板配置表 (9行) 🟢 一般
- **mp_user**: 公众号粉丝表 (25行) 🟡 重要 - 微信用户管理

## 🎯 前端核心表清单

### 必须保留的表（前端核心功能）

```
# 前端管理后台核心表（不能删除）
system_users
system_role
system_menu
system_tenant
system_dept
system_dict_type
system_dict_data
member_user
member_level
pay_app
pay_order
pay_wallet
author_article
infra_file
infra_file_config
```

## 🗑️ 前端不使用的表

### 可能可以删除的表（前端不直接使用）

#### Demo/测试表（优先删除）
- **mp_template_config
**: 
- **pay_demo_order
**: 
- **pay_demo_transfer
**: 
- **system_mail_template
**: 
- **system_notify_template
**: 
- **system_sms_template
**: 
- **yudao_demo01_contact
**: 
- **yudao_demo02_category
**: 
- **yudao_demo03_course
**: 
- **yudao_demo03_grade
**: 
- **yudao_demo03_student
**: 

#### 日志表（可考虑删除）
- **author_article_pv_logs
**: 
- **author_withdraw_logs
**: 
- **infra_api_access_log
**: 
- **infra_api_error_log
**: 
- **infra_job
**: 
- **infra_job_log
**: 
- **member_privilege_log
**: 
- **member_user_balance_logs
**: 
- **member_user_ex_balance_logs
**: 
- **member_user_gold_logs
**: 
- **mp_click_logs
**: 
- **mp_other_even_logs
**: 
- **mp_pay_config_log
**: 
- **partner_invite_logs
**: 
- **partner_withdraw_logs
**: 
- **pay_notify_log
**: 
- **recommend_user_register_logs
**: 
- **system_login_log
**: 
- **system_mail_log
**: 
- **system_operate_log
**: 
- **system_sms_log
**: 

#### 其他表（需确认）
- **account_statistic
**: 
- **account_users
**: 
- **article_push_config
**: 
- **author_accomplishment
**: 
- **author_article
**: 
- **author_article_append
**: 
- **author_audit
**: 
- **author_audit_copy1
**: 
- **author_commission_rate
**: 
- **author_day_report
**: 
- **author_hit_rate
**: 
- **author_info
**: 
- **author_privilege_set
**: 
- **authors
**: 
- **broomer_match_scheme
**: 
- **broomers
**: 
- **football_bd_odds
**: 
- **football_bd_result
**: 
- **football_bd_sf_odds
**: 
- **football_bd_sf_result
**: 
- **football_bo_live_odds
**: 
- **football_company
**: 
- **football_half_live_odds
**: 
- **football_issue
**: 
- **football_jc_odds
**: 
- **football_jc_result
**: 
- **football_live_odds
**: 
- **football_match
**: 
- **football_match_result
**: 
- **football_tc_match
**: 
- **gold_order
**: 
- **guess_records
**: 
- **home_banner
**: 
- **infra_codegen_column
**: 
- **infra_codegen_table
**: 
- **infra_config
**: 
- **infra_data_source_config
**: 
- **infra_file
**: 
- **infra_file_config
**: 
- **infra_file_content
**: 
- **match_category
**: 
- **match_coach
**: 
- **match_competition
**: 
- **match_country
**: 
- **match_future_record
**: 
- **match_history
**: 
- **match_lineup_detail
**: 
- **match_list
**: 
- **match_live_info
**: 
- **match_odds
**: 
- **match_player
**: 
- **match_player_info
**: 
- **match_player_transfer
**: 
- **match_point_rank
**: 
- **match_referee
**: 
- **match_season
**: 
- **match_stage
**: 
- **match_stats
**: 
- **match_team
**: 
- **matches
**: 
- **member_address
**: 
- **member_attention
**: 
- **member_author_privilege
**: 
- **member_bind_record
**: 
- **member_config
**: 
- **member_experience_record
**: 
- **member_group
**: 
- **member_level
**: 
- **member_level_record
**: 
- **member_match_attention
**: 
- **member_match_chat
**: 
- **member_match_vote
**: 
- **member_point_record
**: 
- **member_settlement_info
**: 
- **member_sign_in_config
**: 
- **member_sign_in_record
**: 
- **member_tag
**: 
- **member_user
**: 
- **member_user_bak
**: 
- **mp_account
**: 
- **mp_auto_reply
**: 
- **mp_material
**: 
- **mp_menu
**: 
- **mp_message
**: 
- **mp_mini_user
**: 
- **mp_tag
**: 
- **mp_user
**: 
- **partner_audit
**: 
- **partner_commission_rate
**: 
- **partner_divide_config
**: 
- **partner_info
**: 
- **partner_settlement_info
**: 
- **pay_app
**: 
- **pay_bank_info
**: 
- **pay_channel
**: 
- **pay_notify_task
**: 
- **pay_order
**: 
- **pay_order_extension
**: 
- **pay_refund
**: 
- **pay_transfer
**: 
- **pay_wallet
**: 
- **pay_wallet_recharge
**: 
- **pay_wallet_recharge_package
**: 
- **pay_wallet_transaction
**: 
- **play_type
**: 
- **playing_method
**: 
- **privilege_order
**: 
- **push_amount_config
**: 
- **qrtz_blob_triggers
**: 
- **qrtz_calendars
**: 
- **qrtz_cron_triggers
**: 
- **qrtz_fired_triggers
**: 
- **qrtz_job_details
**: 
- **qrtz_locks
**: 
- **qrtz_paused_trigger_grps
**: 
- **qrtz_scheduler_state
**: 
- **qrtz_simple_triggers
**: 
- **qrtz_simprop_triggers
**: 
- **qrtz_triggers
**: 
- **recommend_author
**: 
- **refund_order
**: 
- **report_operation_day
**: 
- **scheme_order
**: 
- **scheme_play
**: 
- **sys_configs
**: 
- **system_command
**: 
- **system_dept
**: 
- **system_dict_data
**: 
- **system_dict_type
**: 
- **system_mail_account
**: 
- **system_menu
**: 
- **system_notice
**: 
- **system_notify_message
**: 
- **system_oauth2_access_token
**: 
- **system_oauth2_approve
**: 
- **system_oauth2_client
**: 
- **system_oauth2_code
**: 
- **system_oauth2_refresh_token
**: 
- **system_post
**: 
- **system_role
**: 
- **system_role_menu
**: 
- **system_sensitive_word
**: 
- **system_sms_channel
**: 
- **system_sms_code
**: 
- **system_social_client
**: 
- **system_social_user
**: 
- **system_social_user_bind
**: 
- **system_tenant
**: 
- **system_tenant_package
**: 
- **system_user_post
**: 
- **system_user_role
**: 
- **system_users
**: 
- **third_pay_channel
**: 
- **third_pay_sqb_config
**: 
- **wecom_setting
**: 
- **wx_external_contact
**: 
