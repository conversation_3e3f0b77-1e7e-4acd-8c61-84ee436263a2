#!/bin/bash

# =============================================
# 数据库分析器调试脚本
# 用于诊断数据库连接和权限问题
# =============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================"
echo "数据库分析器调试工具"
echo "========================================"

# 读取数据库配置
if [ -f "config/database.conf" ]; then
    source config/database.conf
else
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_NAME=${DB_NAME:-mir}
    DB_USER=${DB_USER:-root}
    
    read -s -p "请输入数据库密码: " DB_PASSWORD
    echo
fi

echo "数据库配置:"
echo "  主机: $DB_HOST:$DB_PORT"
echo "  数据库: $DB_NAME"
echo "  用户: $DB_USER"
echo

# 1. 测试基本连接
log_info "测试数据库连接..."
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" 2>/dev/null >/dev/null; then
    log_success "数据库连接成功"
else
    log_error "数据库连接失败"
    exit 1
fi

# 2. 测试数据库访问
log_info "测试数据库访问..."
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
    log_success "数据库访问成功"
else
    log_error "无法访问数据库 $DB_NAME"
    exit 1
fi

# 3. 获取表列表
log_info "获取表列表..."
TABLES=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SHOW TABLES;" --batch --skip-column-names 2>/dev/null)

if [ -n "$TABLES" ]; then
    TABLE_COUNT=$(echo "$TABLES" | wc -l)
    log_success "发现 $TABLE_COUNT 个表"
    echo "前5个表:"
    echo "$TABLES" | head -5 | while read table; do
        echo "  - $table"
    done
else
    log_error "未发现任何表"
    exit 1
fi

# 4. 测试INFORMATION_SCHEMA访问
log_info "测试INFORMATION_SCHEMA访问..."
SCHEMA_TEST=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '$DB_NAME';" \
    --batch --skip-column-names 2>/dev/null)

if [ -n "$SCHEMA_TEST" ] && [ "$SCHEMA_TEST" -gt 0 ]; then
    log_success "INFORMATION_SCHEMA访问正常，发现 $SCHEMA_TEST 个表"
else
    log_error "INFORMATION_SCHEMA访问失败"
    exit 1
fi

# 5. 测试字段信息获取
log_info "测试字段信息获取..."
FIRST_TABLE=$(echo "$TABLES" | head -1)
log_info "测试表: $FIRST_TABLE"

COLUMNS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
    -e "SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, COLUMN_KEY, COLUMN_DEFAULT, COLUMN_COMMENT 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = '$FIRST_TABLE' 
        ORDER BY ORDINAL_POSITION;" \
    --batch --skip-column-names 2>/dev/null)

if [ -n "$COLUMNS" ]; then
    COLUMN_COUNT=$(echo "$COLUMNS" | wc -l)
    log_success "成功获取 $COLUMN_COUNT 个字段信息"
    echo "字段信息示例:"
    echo "$COLUMNS" | head -3 | while IFS=$'\t' read col_name col_type is_null col_key col_default col_comment; do
        echo "  - $col_name ($col_type) - $col_comment"
    done
else
    log_error "无法获取字段信息"
    
    # 尝试简单查询
    log_info "尝试简单的DESCRIBE查询..."
    DESCRIBE_RESULT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
        -e "DESCRIBE $FIRST_TABLE;" 2>/dev/null)
    
    if [ -n "$DESCRIBE_RESULT" ]; then
        log_warning "DESCRIBE查询成功，但INFORMATION_SCHEMA查询失败"
        echo "$DESCRIBE_RESULT"
    else
        log_error "DESCRIBE查询也失败"
    fi
fi

# 6. 测试权限
log_info "检查用户权限..."
GRANTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    -e "SHOW GRANTS FOR CURRENT_USER();" 2>/dev/null)

if [ -n "$GRANTS" ]; then
    log_success "用户权限信息:"
    echo "$GRANTS"
else
    log_warning "无法获取权限信息"
fi

# 7. 生成测试报告
log_info "生成测试报告..."
mkdir -p debug-output

cat > debug-output/debug-report.md << EOF
# 数据库分析器调试报告

## 测试环境
- **主机**: $DB_HOST:$DB_PORT
- **数据库**: $DB_NAME
- **用户**: $DB_USER
- **测试时间**: $(date)

## 测试结果

### 1. 连接测试
- 基本连接: ✅ 成功
- 数据库访问: ✅ 成功

### 2. 表信息
- 表数量: $TABLE_COUNT
- INFORMATION_SCHEMA访问: ✅ 成功

### 3. 字段信息测试
- 测试表: $FIRST_TABLE
- 字段获取: $([ -n "$COLUMNS" ] && echo "✅ 成功" || echo "❌ 失败")

### 4. 用户权限
\`\`\`
$GRANTS
\`\`\`

## 建议

$(if [ -n "$COLUMNS" ]; then
    echo "✅ 数据库分析器应该能正常工作"
else
    echo "❌ 存在权限或配置问题，需要检查:"
    echo "1. 用户是否有SELECT权限"
    echo "2. 是否有INFORMATION_SCHEMA的访问权限"
    echo "3. MySQL版本是否兼容"
fi)
EOF

log_success "调试报告已生成: debug-output/debug-report.md"

echo
echo "========================================"
echo "调试完成！"
echo "========================================"

if [ -n "$COLUMNS" ]; then
    echo "✅ 数据库分析器应该能正常工作"
    echo "💡 如果仍有问题，请检查脚本中的SQL语句"
else
    echo "❌ 发现问题，请检查:"
    echo "  1. 用户权限设置"
    echo "  2. INFORMATION_SCHEMA访问权限"
    echo "  3. MySQL版本兼容性"
fi
